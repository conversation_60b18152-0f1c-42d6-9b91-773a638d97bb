{"version": 3, "sources": ["src/app/core/services/auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap, map, of } from 'rxjs';\nimport { Router } from '@angular/router';\nimport { \n  UserLoginDto, \n  UserRegistrationDto, \n  AuthResponseDto, \n  UserDto \n} from '../models/auth.models';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = `${environment.apiUrl}/api/Authentication`;\n  private readonly TOKEN_KEY = 'bpm_token';\n  private readonly USER_KEY = 'bpm_user';\n  \n  private currentUserSubject = new BehaviorSubject<UserDto | null>(null);\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\n  \n  public currentUser$ = this.currentUserSubject.asObservable();\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n  public redirectUrl: string | null = null;\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {\n    this.initializeAuth();\n  }\n\n  private initializeAuth(): void {\n    const token = this.getToken();\n    const user = this.getStoredUser();\n    \n    if (token && user && !this.isTokenExpired(token)) {\n      this.currentUserSubject.next(user);\n      this.isAuthenticatedSubject.next(true);\n    } else {\n      this.clearAuthData();\n    }\n  }\n\n  login(credentials: UserLoginDto): Observable<AuthResponseDto> {\n    return this.http.post<AuthResponseDto>(`${this.API_URL}/login`, credentials)\n      .pipe(\n        tap(response => {\n          if (response.IsAuthSuccessful && response.Token && response.User) {\n            this.setToken(response.Token);\n            this.setUser(response.User);\n            this.currentUserSubject.next(response.User);\n            this.isAuthenticatedSubject.next(true);\n          }\n        })\n      );\n  }\n\n  register(userData: UserRegistrationDto): Observable<AuthResponseDto> {\n    return this.http.post<AuthResponseDto>(`${this.API_URL}/register`, userData);\n  }\n\n  changePassword(passwordData: {\n    currentPassword: string;\n    newPassword: string;\n    confirmPassword: string;\n  }): Observable<void> {\n    return this.http.post<void>(`${this.API_URL}/change-password`, passwordData);\n  }\n\n  logout(): void {\n    this.clearAuthData();\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/auth/login']);\n  }\n\n  getToken(): string | null {\n    try {\n      return localStorage.getItem(this.TOKEN_KEY);\n    } catch {\n      return null;\n    }\n  }\n\n  getCurrentUser(): UserDto | null {\n    return this.currentUserSubject.value;\n  }\n\n  hasRole(role: string): boolean {\n    const user = this.getCurrentUser();\n    const userRoles = user?.roles || user?.Roles || [];\n    return userRoles.includes(role);\n  }\n\n  hasAnyRole(roles: string[]): boolean {\n    const user = this.getCurrentUser();\n    const userRoles = user?.roles || user?.Roles || [];\n    if (userRoles.length === 0) return false;\n    return roles.some(role => userRoles.includes(role));\n  }\n\n  // Get the appropriate dashboard route based on user roles\n  getDashboardRoute(): string {\n    const user = this.getCurrentUser();\n    const userRoles = user?.roles || user?.Roles || [];\n\n    // Debug logging\n    console.log('User object:', user);\n    console.log('User roles detected:', userRoles);\n\n    if (userRoles.length === 0) {\n      console.log('No roles found, defaulting to employee dashboard');\n      return '/dashboard/employee'; // Default to employee dashboard\n    }\n\n    // Priority order: Admin > HR > Manager > Employee\n    if (userRoles.includes('Admin')) {\n      console.log('Admin role detected, redirecting to reporting dashboard');\n      return '/dashboard/reporting'; // Admin gets reporting dashboard\n    }\n\n    if (userRoles.includes('HR')) {\n      console.log('HR role detected, redirecting to HR dashboard');\n      return '/dashboard/hr';\n    }\n\n    if (userRoles.includes('Manager')) {\n      console.log('Manager role detected, redirecting to manager dashboard');\n      return '/dashboard/manager';\n    }\n\n    // Default to employee dashboard for any other role or Employee role\n    console.log('No specific role matched, defaulting to employee dashboard');\n    return '/dashboard/employee';\n  }\n\n  isAuthenticated(): boolean {\n    return this.isAuthenticatedSubject.value;\n  }\n\n  refreshUserProfile(): Observable<UserDto> {\n    return this.http.get<UserDto>(`${this.API_URL}/profile`)\n      .pipe(\n        tap(user => {\n          this.setUser(user);\n          this.currentUserSubject.next(user);\n        })\n      );\n  }\n\n  private setToken(token: string): void {\n    try {\n      localStorage.setItem(this.TOKEN_KEY, token);\n    } catch (error) {\n      console.warn('Could not save token to localStorage:', error);\n    }\n  }\n\n  private setUser(user: UserDto): void {\n    try {\n      localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n    } catch (error) {\n      console.warn('Could not save user to localStorage:', error);\n    }\n  }\n\n  private getStoredUser(): UserDto | null {\n    try {\n      const userStr = localStorage.getItem(this.USER_KEY);\n      return userStr ? JSON.parse(userStr) : null;\n    } catch {\n      return null;\n    }\n  }\n\n  private clearAuthData(): void {\n    try {\n      localStorage.removeItem(this.TOKEN_KEY);\n      localStorage.removeItem(this.USER_KEY);\n    } catch (error) {\n      console.warn('Could not clear auth data from localStorage:', error);\n    }\n  }\n\n  private isTokenExpired(token: string): boolean {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      return payload.exp < currentTime;\n    } catch {\n      return true;\n    }\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;AAeM,IAAO,cAAP,MAAO,aAAW;EAaZ;EACA;EAbO,UAAU,GAAG,YAAY,MAAM;EAC/B,YAAY;EACZ,WAAW;EAEpB,qBAAqB,IAAI,gBAAgC,IAAI;EAC7D,yBAAyB,IAAI,gBAAyB,KAAK;EAE5D,eAAe,KAAK,mBAAmB,aAAY;EACnD,mBAAmB,KAAK,uBAAuB,aAAY;EAC3D,cAA6B;EAEpC,YACU,MACA,QAAc;AADd,SAAA,OAAA;AACA,SAAA,SAAA;AAER,SAAK,eAAc;EACrB;EAEQ,iBAAc;AACpB,UAAM,QAAQ,KAAK,SAAQ;AAC3B,UAAM,OAAO,KAAK,cAAa;AAE/B,QAAI,SAAS,QAAQ,CAAC,KAAK,eAAe,KAAK,GAAG;AAChD,WAAK,mBAAmB,KAAK,IAAI;AACjC,WAAK,uBAAuB,KAAK,IAAI;IACvC,OAAO;AACL,WAAK,cAAa;IACpB;EACF;EAEA,MAAM,aAAyB;AAC7B,WAAO,KAAK,KAAK,KAAsB,GAAG,KAAK,OAAO,UAAU,WAAW,EACxE,KACC,IAAI,cAAW;AACb,UAAI,SAAS,oBAAoB,SAAS,SAAS,SAAS,MAAM;AAChE,aAAK,SAAS,SAAS,KAAK;AAC5B,aAAK,QAAQ,SAAS,IAAI;AAC1B,aAAK,mBAAmB,KAAK,SAAS,IAAI;AAC1C,aAAK,uBAAuB,KAAK,IAAI;MACvC;IACF,CAAC,CAAC;EAER;EAEA,SAAS,UAA6B;AACpC,WAAO,KAAK,KAAK,KAAsB,GAAG,KAAK,OAAO,aAAa,QAAQ;EAC7E;EAEA,eAAe,cAId;AACC,WAAO,KAAK,KAAK,KAAW,GAAG,KAAK,OAAO,oBAAoB,YAAY;EAC7E;EAEA,SAAM;AACJ,SAAK,cAAa;AAClB,SAAK,mBAAmB,KAAK,IAAI;AACjC,SAAK,uBAAuB,KAAK,KAAK;AACtC,SAAK,OAAO,SAAS,CAAC,aAAa,CAAC;EACtC;EAEA,WAAQ;AACN,QAAI;AACF,aAAO,aAAa,QAAQ,KAAK,SAAS;IAC5C,QAAQ;AACN,aAAO;IACT;EACF;EAEA,iBAAc;AACZ,WAAO,KAAK,mBAAmB;EACjC;EAEA,QAAQ,MAAY;AAClB,UAAM,OAAO,KAAK,eAAc;AAChC,UAAM,YAAY,MAAM,SAAS,MAAM,SAAS,CAAA;AAChD,WAAO,UAAU,SAAS,IAAI;EAChC;EAEA,WAAW,OAAe;AACxB,UAAM,OAAO,KAAK,eAAc;AAChC,UAAM,YAAY,MAAM,SAAS,MAAM,SAAS,CAAA;AAChD,QAAI,UAAU,WAAW;AAAG,aAAO;AACnC,WAAO,MAAM,KAAK,UAAQ,UAAU,SAAS,IAAI,CAAC;EACpD;;EAGA,oBAAiB;AACf,UAAM,OAAO,KAAK,eAAc;AAChC,UAAM,YAAY,MAAM,SAAS,MAAM,SAAS,CAAA;AAGhD,YAAQ,IAAI,gBAAgB,IAAI;AAChC,YAAQ,IAAI,wBAAwB,SAAS;AAE7C,QAAI,UAAU,WAAW,GAAG;AAC1B,cAAQ,IAAI,kDAAkD;AAC9D,aAAO;IACT;AAGA,QAAI,UAAU,SAAS,OAAO,GAAG;AAC/B,cAAQ,IAAI,yDAAyD;AACrE,aAAO;IACT;AAEA,QAAI,UAAU,SAAS,IAAI,GAAG;AAC5B,cAAQ,IAAI,+CAA+C;AAC3D,aAAO;IACT;AAEA,QAAI,UAAU,SAAS,SAAS,GAAG;AACjC,cAAQ,IAAI,yDAAyD;AACrE,aAAO;IACT;AAGA,YAAQ,IAAI,4DAA4D;AACxE,WAAO;EACT;EAEA,kBAAe;AACb,WAAO,KAAK,uBAAuB;EACrC;EAEA,qBAAkB;AAChB,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,UAAU,EACpD,KACC,IAAI,UAAO;AACT,WAAK,QAAQ,IAAI;AACjB,WAAK,mBAAmB,KAAK,IAAI;IACnC,CAAC,CAAC;EAER;EAEQ,SAAS,OAAa;AAC5B,QAAI;AACF,mBAAa,QAAQ,KAAK,WAAW,KAAK;IAC5C,SAAS,OAAO;AACd,cAAQ,KAAK,yCAAyC,KAAK;IAC7D;EACF;EAEQ,QAAQ,MAAa;AAC3B,QAAI;AACF,mBAAa,QAAQ,KAAK,UAAU,KAAK,UAAU,IAAI,CAAC;IAC1D,SAAS,OAAO;AACd,cAAQ,KAAK,wCAAwC,KAAK;IAC5D;EACF;EAEQ,gBAAa;AACnB,QAAI;AACF,YAAM,UAAU,aAAa,QAAQ,KAAK,QAAQ;AAClD,aAAO,UAAU,KAAK,MAAM,OAAO,IAAI;IACzC,QAAQ;AACN,aAAO;IACT;EACF;EAEQ,gBAAa;AACnB,QAAI;AACF,mBAAa,WAAW,KAAK,SAAS;AACtC,mBAAa,WAAW,KAAK,QAAQ;IACvC,SAAS,OAAO;AACd,cAAQ,KAAK,gDAAgD,KAAK;IACpE;EACF;EAEQ,eAAe,OAAa;AAClC,QAAI;AACF,YAAM,UAAU,KAAK,MAAM,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AACpD,YAAM,cAAc,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;AAChD,aAAO,QAAQ,MAAM;IACvB,QAAQ;AACN,aAAO;IACT;EACF;;qCApLW,cAAW,mBAAA,UAAA,GAAA,mBAAA,MAAA,CAAA;EAAA;4EAAX,cAAW,SAAX,aAAW,WAAA,YAFV,OAAM,CAAA;;;sEAEP,aAAW,CAAA;UAHvB;WAAW;MACV,YAAY;KACb;;;", "names": []}