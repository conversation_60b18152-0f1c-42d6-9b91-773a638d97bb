{"version": 3, "sources": ["src/app/features/requests/components/request-list/request-list.component.ts"], "sourcesContent": ["\n    .request-list-container {\n      padding: 1rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .filters {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n      flex-wrap: wrap;\n    }\n\n    .filters mat-form-field {\n      min-width: 200px;\n    }\n\n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .requests-table {\n      width: 100%;\n    }\n\n    .request-id {\n      font-family: monospace;\n      background-color: #f5f5f5;\n      padding: 2px 6px;\n      border-radius: 4px;\n      font-size: 0.8rem;\n    }\n\n    .request-title strong {\n      display: block;\n    }\n\n    .request-title small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .status-pending {\n      background-color: #fff3cd;\n      color: #856404;\n    }\n\n    .status-approved {\n      background-color: #d4edda;\n      color: #155724;\n    }\n\n    .status-rejected {\n      background-color: #f8d7da;\n      color: #721c24;\n    }\n\n    .status-archived {\n      background-color: #e2e3e5;\n      color: #383d41;\n    }\n\n    .no-data {\n      text-align: center;\n      padding: 3rem;\n      color: #666;\n    }\n\n    .no-data mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #ccc;\n    }\n\n    @media (max-width: 768px) {\n      .filters {\n        flex-direction: column;\n      }\n      \n      .filters mat-form-field {\n        min-width: 100%;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAPA,QAOA;AACE,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,eAAA;AACA,oBAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAAA,cAAA;AACE,WAAA;;AAGF,CAJA,cAIA;AACE,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;;AAGF,CANA,QAMA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GA7EF;AA8EI,oBAAA;;AAGF,GAjFF,QAiFE;AACE,eAAA;;;", "names": []}