{"version": 3, "sources": ["src/app/shared/components/notification-list/notification-list.component.ts", "src/app/shared/components/notification-list/notification-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { NotificationService } from '../../../core/services/notification.service';\nimport { NotificationDto } from '../../../core/models/notification.models';\n\n@Component({\n  selector: 'app-notification-list',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatListModule\n  ],\n  templateUrl: './notification-list.component.html',\n  styleUrls: ['./notification-list.component.scss'],\n})\nexport class NotificationListComponent implements OnInit {\n  notifications: NotificationDto[] = [];\n\n  constructor(private notificationService: NotificationService) {}\n\n  ngOnInit(): void {\n    this.notificationService.getNotifications({ pageNumber: 1, pageSize: 50 }).subscribe((response) => {\n      this.notifications = response.data;\n    });\n  }\n\n  markAsRead(notification: NotificationDto): void {\n    this.notificationService.markAsRead(notification.id).subscribe(() => {\n      notification.isRead = true;\n    });\n  }\n\n  clearAllNotifications(): void {\n    this.notificationService.clearAllNotifications().subscribe(() => {\n      this.notifications = [];\n    });\n  }\n}", "<div class=\"notification-list-container\">\n  <h2>Notifications</h2>\n\n  <div *ngIf=\"notifications.length === 0\" class=\"no-notifications\">\n    <p>No new notifications.</p>\n  </div>\n\n  <ul *ngIf=\"notifications.length > 0\" class=\"notification-items\">\n    <li\n      *ngFor=\"let notification of notifications\"\n      [class.read]=\"notification.isRead\"\n    >\n      <div class=\"notification-content\">\n        <h4>{{ notification.title }}</h4>\n        <p>{{ notification.message }}</p>\n        <span class=\"timestamp\">{{\n          notification.createdAt | date : \"short\"\n        }}</span>\n      </div>\n      <button *ngIf=\"!notification.isRead\" (click)=\"markAsRead(notification)\">\n        Mark as Read\n      </button>\n    </li>\n  </ul>\n\n  <button\n    *ngIf=\"notifications.length > 0\"\n    (click)=\"clearAllNotifications()\"\n    class=\"clear-all-button\"\n  >\n    Clear All Notifications\n  </button>\n</div>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACGE,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAiE,GAAA,GAAA;AAC5D,IAAA,iBAAA,GAAA,uBAAA;AAAqB,IAAA,uBAAA,EAAI;;;;;;AAe1B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAqC,IAAA,qBAAA,SAAA,SAAA,gFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,kBAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,eAAA,CAAwB;IAAA,CAAA;AACpE,IAAA,iBAAA,GAAA,gBAAA;AACF,IAAA,uBAAA;;;;;AAbF,IAAA,yBAAA,GAAA,IAAA,EAGC,GAAA,OAAA,CAAA,EACmC,GAAA,IAAA;AAC5B,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;AAC7B,IAAA,yBAAA,GAAA,QAAA,CAAA;AAAwB,IAAA,iBAAA,CAAA;;AAEtB,IAAA,uBAAA,EAAO;AAEX,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,UAAA,CAAA;AAGF,IAAA,uBAAA;;;;AAZE,IAAA,sBAAA,QAAA,gBAAA,MAAA;AAGM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,gBAAA,KAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,gBAAA,OAAA;AACqB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,gBAAA,WAAA,OAAA,CAAA;AAIjB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,gBAAA,MAAA;;;;;AAZb,IAAA,yBAAA,GAAA,MAAA,CAAA;AACE,IAAA,qBAAA,GAAA,8CAAA,IAAA,GAAA,MAAA,CAAA;AAeF,IAAA,uBAAA;;;;AAd6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,aAAA;;;;;;AAgB7B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,sBAAA,CAAuB;IAAA,CAAA;AAGhC,IAAA,iBAAA,GAAA,2BAAA;AACF,IAAA,uBAAA;;;ADTI,IAAO,4BAAP,MAAO,2BAAyB;EAGhB;EAFpB,gBAAmC,CAAA;EAEnC,YAAoB,qBAAwC;AAAxC,SAAA,sBAAA;EAA2C;EAE/D,WAAQ;AACN,SAAK,oBAAoB,iBAAiB,EAAE,YAAY,GAAG,UAAU,GAAE,CAAE,EAAE,UAAU,CAAC,aAAY;AAChG,WAAK,gBAAgB,SAAS;IAChC,CAAC;EACH;EAEA,WAAW,cAA6B;AACtC,SAAK,oBAAoB,WAAW,aAAa,EAAE,EAAE,UAAU,MAAK;AAClE,mBAAa,SAAS;IACxB,CAAC;EACH;EAEA,wBAAqB;AACnB,SAAK,oBAAoB,sBAAqB,EAAG,UAAU,MAAK;AAC9D,WAAK,gBAAgB,CAAA;IACvB,CAAC;EACH;;qCArBW,4BAAyB,4BAAA,mBAAA,CAAA;EAAA;yEAAzB,4BAAyB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,6BAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,QAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,mCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACtBtC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAyC,GAAA,IAAA;AACnC,MAAA,iBAAA,GAAA,eAAA;AAAa,MAAA,uBAAA;AAEjB,MAAA,qBAAA,GAAA,0CAAA,GAAA,GAAA,OAAA,CAAA,EAAiE,GAAA,yCAAA,GAAA,GAAA,MAAA,CAAA,EAID,GAAA,6CAAA,GAAA,GAAA,UAAA,CAAA;AAyBlE,MAAA,uBAAA;;;AA7BQ,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,WAAA,CAAA;AAID,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,SAAA,CAAA;AAmBF,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,SAAA,CAAA;;;IDbD;IAAY;IAAA;IAAA;IACZ;IACA;IACA;IACA;EAAa,GAAA,QAAA,CAAA,mmGAAA,EAAA,CAAA;;;sEAKJ,2BAAyB,CAAA;UAbrC;uBACW,yBAAuB,YACrB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;OACD,UAAA,85BAAA,QAAA,CAAA,qzEAAA,EAAA,CAAA;;;;6EAIU,2BAAyB,EAAA,WAAA,6BAAA,UAAA,8EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}