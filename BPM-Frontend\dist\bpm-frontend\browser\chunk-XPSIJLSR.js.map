{"version": 3, "sources": ["src/app/features/admin/components/admin-layout/admin-layout.component.ts", "src/app/features/admin/components/admin-layout/admin-layout.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  selector: 'app-admin-layout',\n  templateUrl: './admin-layout.component.html',\n  styleUrls: ['./admin-layout.component.scss'],\n  standalone: true,\n  imports: [RouterModule]\n})\nexport class AdminLayoutComponent {}\n", "<div class=\"admin-layout\">\n  <aside class=\"sidebar\">\n    <nav>\n      <ul>\n        <li>\n          <a routerLink=\"users\" routerLinkActive=\"active\">User Management</a>\n        </li>\n        <li>\n          <a routerLink=\"workflow-designer\" routerLinkActive=\"active\"\n            >Workflow Designer</a\n          >\n        </li>\n        <!-- Add more admin navigation links here -->\n      </ul>\n    </nav>\n  </aside>\n  <main class=\"content\">\n    <router-outlet></router-outlet>\n  </main>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAUM,IAAO,uBAAP,MAAO,sBAAoB;;qCAApB,uBAAoB;EAAA;yEAApB,uBAAoB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,cAAA,SAAA,oBAAA,QAAA,GAAA,CAAA,cAAA,qBAAA,oBAAA,QAAA,GAAA,CAAA,GAAA,SAAA,CAAA,GAAA,UAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACVjC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,SAAA,CAAA,EACD,GAAA,KAAA,EAChB,GAAA,IAAA,EACC,GAAA,IAAA,EACE,GAAA,KAAA,CAAA;AAC8C,MAAA,iBAAA,GAAA,iBAAA;AAAe,MAAA,uBAAA,EAAI;AAErE,MAAA,yBAAA,GAAA,IAAA,EAAI,GAAA,KAAA,CAAA;AAEC,MAAA,iBAAA,GAAA,mBAAA;AAAiB,MAAA,uBAAA,EACnB,EACE,EAEF,EACD;AAER,MAAA,yBAAA,IAAA,QAAA,CAAA;AACE,MAAA,oBAAA,IAAA,eAAA;AACF,MAAA,uBAAA,EAAO;;oBDVG,cAAY,cAAA,YAAA,gBAAA,GAAA,QAAA,CAAA,09BAAA,EAAA,CAAA;;;sEAEX,sBAAoB,CAAA;UAPhC;uBACW,oBAAkB,YAGhB,MAAI,SACP,CAAC,YAAY,GAAC,UAAA,ggBAAA,QAAA,CAAA,6nBAAA,EAAA,CAAA;;;;6EAEZ,sBAAoB,EAAA,WAAA,wBAAA,UAAA,4EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}