{"version": 3, "sources": ["src/app/features/workflows/workflows-routing.module.ts", "src/app/features/workflows/workflows.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\n\nconst routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/workflow-list/workflow-list.component').then(c => c.WorkflowListComponent)\n  },\n  {\n    path: 'designer',\n    loadComponent: () => import('./components/workflow-designer/workflow-designer.component').then(c => c.WorkflowDesignerComponent)\n  },\n  {\n    path: 'details/:id',\n    loadComponent: () => import('./components/workflow-details/workflow-details.component').then(c => c.WorkflowDetailsComponent)\n  },\n  {\n    path: '**',\n    loadComponent: () => import('./components/workflows-not-found/workflows-not-found.component').then(c => c.WorkflowsNotFoundComponent)\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class WorkflowsRoutingModule { }\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\nimport { WorkflowsRoutingModule } from './workflows-routing.module';\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule,\n    RouterModule,\n    WorkflowsRoutingModule\n  ]\n})\nexport class WorkflowsModule { }\n"], "mappings": ";;;;;;;;;;;;AAGA,IAAM,SAAiB;EACrB;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAoD,EAAE,KAAK,OAAK,EAAE,qBAAqB;;EAErH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA4D,EAAE,KAAK,OAAK,EAAE,yBAAyB;;EAEjI;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA0D,EAAE,KAAK,OAAK,EAAE,wBAAwB;;EAE9H;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAgE,EAAE,KAAK,OAAK,EAAE,0BAA0B;;;AAQlI,IAAO,yBAAP,MAAO,wBAAsB;;qCAAtB,yBAAsB;EAAA;wEAAtB,wBAAsB,CAAA;4EAHvB,aAAa,SAAS,MAAM,GAC5B,YAAY,EAAA,CAAA;;;sEAEX,wBAAsB,CAAA;UAJlC;WAAS;MACR,SAAS,CAAC,aAAa,SAAS,MAAM,CAAC;MACvC,SAAS,CAAC,YAAY;KACvB;;;;;ACXK,IAAO,kBAAP,MAAO,iBAAe;;qCAAf,kBAAe;EAAA;wEAAf,iBAAe,CAAA;;IALxB;IACA;IACA;EAAsB,EAAA,CAAA;;;sEAGb,iBAAe,CAAA;UAR3B;WAAS;MACR,cAAc,CAAA;MACd,SAAS;QACP;QACA;QACA;;KAEH;;;", "names": []}