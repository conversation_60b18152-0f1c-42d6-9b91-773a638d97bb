import {
  AuthService
} from "./chunk-EP3FZZM6.js";
import {
  Router
} from "./chunk-ECY6M2NH.js";
import {
  Injectable,
  Observable,
  map,
  setClassMetadata,
  take,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-XLTWONBW.js";

// src/app/core/guards/auth.guard.ts
var AuthGuard = class _AuthGuard {
  authService;
  router;
  constructor(authService, router) {
    this.authService = authService;
    this.router = router;
  }
  canActivate(route, state) {
    return this.checkAuth(route, state);
  }
  canActivateChild(childRoute, state) {
    return this.checkAuth(childRoute, state);
  }
  checkAuth(route, state) {
    return this.authService.isAuthenticated$.pipe(take(1), map((isAuthenticated) => {
      if (isAuthenticated) {
        const requiredRoles = route.data?.["roles"];
        if (requiredRoles && requiredRoles.length > 0) {
          const hasRequiredRole = this.authService.hasAnyRole(requiredRoles);
          if (!hasRequiredRole) {
            this.router.navigate(["/unauthorized"]);
            return false;
          }
        }
        return true;
      } else {
        this.authService.redirectUrl = state.url;
        this.router.navigate(["/auth/login"]);
        return false;
      }
    }));
  }
  static \u0275fac = function AuthGuard_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AuthGuard)(\u0275\u0275inject(AuthService), \u0275\u0275inject(Router));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AuthGuard, factory: _AuthGuard.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AuthService }, { type: Router }], null);
})();

// src/app/core/guards/role.guard.ts
var RoleGuard = class _RoleGuard {
  authService;
  router;
  constructor(authService, router) {
    this.authService = authService;
    this.router = router;
  }
  canActivate(route) {
    const requiredRoles = route.data?.["roles"];
    if (!requiredRoles || requiredRoles.length === 0) {
      return new Observable((observer) => {
        observer.next(true);
        observer.complete();
      });
    }
    return this.authService.isAuthenticated$.pipe(take(1), map((isAuthenticated) => {
      if (!isAuthenticated) {
        this.router.navigate(["/auth/login"]);
        return false;
      }
      const hasRequiredRole = this.authService.hasAnyRole(requiredRoles);
      if (!hasRequiredRole) {
        this.router.navigate(["/unauthorized"]);
        return false;
      }
      return true;
    }));
  }
  static \u0275fac = function RoleGuard_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RoleGuard)(\u0275\u0275inject(AuthService), \u0275\u0275inject(Router));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _RoleGuard, factory: _RoleGuard.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RoleGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AuthService }, { type: Router }], null);
})();

// src/app/core/guards/no-auth.guard.ts
var NoAuthGuard = class _NoAuthGuard {
  authService;
  router;
  constructor(authService, router) {
    this.authService = authService;
    this.router = router;
  }
  canActivate() {
    return this.authService.isAuthenticated$.pipe(take(1), map((isAuthenticated) => {
      if (isAuthenticated) {
        this.router.navigate(["/dashboard"]);
        return false;
      }
      return true;
    }));
  }
  static \u0275fac = function NoAuthGuard_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NoAuthGuard)(\u0275\u0275inject(AuthService), \u0275\u0275inject(Router));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _NoAuthGuard, factory: _NoAuthGuard.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NoAuthGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AuthService }, { type: Router }], null);
})();

export {
  AuthGuard,
  RoleGuard,
  NoAuthGuard
};
//# sourceMappingURL=chunk-MI7J4LQF.js.map
