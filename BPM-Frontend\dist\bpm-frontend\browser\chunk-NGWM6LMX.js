import {
  Mat<PERSON>rogress<PERSON><PERSON>,
  MatProgressBarModule
} from "./chunk-FSJKBZWH.js";
import "./chunk-CPP3G34D.js";
import {
  RequestService
} from "./chunk-AM3AE65N.js";
import {
  MatChip,
  MatChipsModule
} from "./chunk-KJM35DAP.js";
import {
  RequestStatus,
  RequestType
} from "./chunk-XJ5TS5V6.js";
import "./chunk-PJGOPMTU.js";
import "./chunk-4WCUDP7B.js";
import {
  AuthService
} from "./chunk-EP3FZZM6.js";
import {
  MatTab,
  MatTabGroup,
  MatTabsModule
} from "./chunk-ZR5MXJ33.js";
import {
  Mat<PERSON>ell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
  MatTableModule
} from "./chunk-Z6DK6RU5.js";
import "./chunk-I7LZP7WV.js";
import "./chunk-FTI6XIW5.js";
import "./chunk-R6R3KTSK.js";
import {
  MatCard,
  MatCardContent,
  MatCardModule
} from "./chunk-QRMDFVVO.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-FSO3A3K2.js";
import {
  MatButton,
  MatButtonModule,
  MatIconButton
} from "./chunk-SVX3GQPM.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  Component,
  DatePipe,
  NgForOf,
  NgIf,
  Subject,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpropertyInterpolate1,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-XLTWONBW.js";

// src/app/features/dashboard/components/manager-dashboard/manager-dashboard.component.ts
var _c0 = (a0) => ["/requests/details", a0];
function ManagerDashboardComponent_div_62_th_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 41);
    \u0275\u0275text(1, "Employee");
    \u0275\u0275elementEnd();
  }
}
function ManagerDashboardComponent_div_62_td_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 42)(1, "div", 43)(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "small");
    \u0275\u0275text(5);
    \u0275\u0275pipe(6, "date");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const request_r1 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(request_r1.initiatorName);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(6, 2, request_r1.createdAt, "short"));
  }
}
function ManagerDashboardComponent_div_62_th_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 41);
    \u0275\u0275text(1, "Type");
    \u0275\u0275elementEnd();
  }
}
function ManagerDashboardComponent_div_62_td_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 42)(1, "mat-chip");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275classMap(ctx_r2.getRequestTypeClass(request_r2.type));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r2.getRequestTypeLabel(request_r2.type), " ");
  }
}
function ManagerDashboardComponent_div_62_th_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 41);
    \u0275\u0275text(1, "Request");
    \u0275\u0275elementEnd();
  }
}
function ManagerDashboardComponent_div_62_td_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 42)(1, "div", 44);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r4 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", request_r4.title || "No Title", " ");
  }
}
function ManagerDashboardComponent_div_62_th_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 41);
    \u0275\u0275text(1, "Priority");
    \u0275\u0275elementEnd();
  }
}
function ManagerDashboardComponent_div_62_td_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 42)(1, "mat-chip");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r5 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275classMap(ctx_r2.getPriorityClass(request_r5));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r2.getPriorityLabel(request_r5), " ");
  }
}
function ManagerDashboardComponent_div_62_th_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 41);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function ManagerDashboardComponent_div_62_td_16_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "td", 42)(1, "div", 45)(2, "button", 46)(3, "mat-icon");
    \u0275\u0275text(4, "visibility");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "button", 47);
    \u0275\u0275listener("click", function ManagerDashboardComponent_div_62_td_16_Template_button_click_5_listener() {
      const request_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.quickApprove(request_r7));
    });
    \u0275\u0275elementStart(6, "mat-icon");
    \u0275\u0275text(7, "check");
    \u0275\u0275elementEnd();
    \u0275\u0275text(8, " Approve ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "button", 48);
    \u0275\u0275listener("click", function ManagerDashboardComponent_div_62_td_16_Template_button_click_9_listener() {
      const request_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.quickReject(request_r7));
    });
    \u0275\u0275elementStart(10, "mat-icon");
    \u0275\u0275text(11, "close");
    \u0275\u0275elementEnd();
    \u0275\u0275text(12, " Reject ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const request_r7 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(1, _c0, request_r7.id));
  }
}
function ManagerDashboardComponent_div_62_tr_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 49);
  }
}
function ManagerDashboardComponent_div_62_tr_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 50);
  }
}
function ManagerDashboardComponent_div_62_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 30)(1, "table", 31);
    \u0275\u0275elementContainerStart(2, 32);
    \u0275\u0275template(3, ManagerDashboardComponent_div_62_th_3_Template, 2, 0, "th", 33)(4, ManagerDashboardComponent_div_62_td_4_Template, 7, 5, "td", 34);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(5, 35);
    \u0275\u0275template(6, ManagerDashboardComponent_div_62_th_6_Template, 2, 0, "th", 33)(7, ManagerDashboardComponent_div_62_td_7_Template, 3, 3, "td", 34);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(8, 36);
    \u0275\u0275template(9, ManagerDashboardComponent_div_62_th_9_Template, 2, 0, "th", 33)(10, ManagerDashboardComponent_div_62_td_10_Template, 3, 1, "td", 34);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(11, 37);
    \u0275\u0275template(12, ManagerDashboardComponent_div_62_th_12_Template, 2, 0, "th", 33)(13, ManagerDashboardComponent_div_62_td_13_Template, 3, 3, "td", 34);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(14, 38);
    \u0275\u0275template(15, ManagerDashboardComponent_div_62_th_15_Template, 2, 0, "th", 33)(16, ManagerDashboardComponent_div_62_td_16_Template, 13, 3, "td", 34);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(17, ManagerDashboardComponent_div_62_tr_17_Template, 1, 0, "tr", 39)(18, ManagerDashboardComponent_div_62_tr_18_Template, 1, 0, "tr", 40);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("dataSource", ctx_r2.pendingRequests);
    \u0275\u0275advance(16);
    \u0275\u0275property("matHeaderRowDef", ctx_r2.pendingColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r2.pendingColumns);
  }
}
function ManagerDashboardComponent_div_63_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 51)(1, "mat-icon");
    \u0275\u0275text(2, "assignment_turned_in");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h3");
    \u0275\u0275text(4, "No Pending Approvals");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "All requests from your team have been processed.");
    \u0275\u0275elementEnd()();
  }
}
function ManagerDashboardComponent_div_95_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 52)(1, "div", 53)(2, "mat-icon");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "div", 54)(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "small");
    \u0275\u0275text(8);
    \u0275\u0275pipe(9, "date");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const activity_r8 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(activity_r8.icon);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(activity_r8.description);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(9, 3, activity_r8.timestamp, "short"));
  }
}
var ManagerDashboardComponent = class _ManagerDashboardComponent {
  requestService;
  authService;
  destroy$ = new Subject();
  teamMetrics = {
    totalTeamMembers: 8,
    pendingApprovals: 5,
    approvedThisMonth: 23,
    rejectedThisMonth: 3,
    averageApprovalTime: 4.2
  };
  pendingRequests = [];
  pendingColumns = ["employee", "type", "title", "priority", "actions"];
  recentActivities = [
    {
      icon: "check_circle",
      description: "Approved leave request from John Doe",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1e3)
      // 2 hours ago
    },
    {
      icon: "assignment",
      description: "New expense report submitted by Jane Smith",
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1e3)
      // 4 hours ago
    },
    {
      icon: "cancel",
      description: "Rejected training request from Mike Johnson",
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1e3)
      // 6 hours ago
    }
  ];
  constructor(requestService, authService) {
    this.requestService = requestService;
    this.authService = authService;
  }
  ngOnInit() {
    this.loadPendingRequests();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadPendingRequests() {
    this.pendingRequests = [
      {
        id: "1",
        type: RequestType.Leave,
        initiatorId: "user1",
        initiatorName: "John Doe",
        status: RequestStatus.Pending,
        title: "Vacation Leave - Dec 20-30",
        description: "Annual vacation leave",
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1e3),
        isDeleted: false,
        requestSteps: []
      },
      {
        id: "2",
        type: RequestType.Expense,
        initiatorId: "user2",
        initiatorName: "Jane Smith",
        status: RequestStatus.Pending,
        title: "Business Trip Expenses",
        description: "Client meeting expenses",
        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1e3),
        isDeleted: false,
        requestSteps: []
      }
    ];
  }
  getRequestTypeLabel(type) {
    switch (type) {
      case RequestType.Leave:
        return "Leave";
      case RequestType.Expense:
        return "Expense";
      case RequestType.Training:
        return "Training";
      case RequestType.ITSupport:
        return "IT Support";
      case RequestType.ProfileUpdate:
        return "Profile";
      default:
        return "Unknown";
    }
  }
  getRequestTypeClass(type) {
    switch (type) {
      case RequestType.Leave:
        return "type-leave";
      case RequestType.Expense:
        return "type-expense";
      case RequestType.Training:
        return "type-training";
      case RequestType.ITSupport:
        return "type-it";
      case RequestType.ProfileUpdate:
        return "type-profile";
      default:
        return "";
    }
  }
  getPriorityLabel(request) {
    const daysSinceCreated = Math.floor((Date.now() - new Date(request.createdAt).getTime()) / (1e3 * 60 * 60 * 24));
    if (daysSinceCreated > 3)
      return "High";
    if (daysSinceCreated > 1)
      return "Medium";
    return "Low";
  }
  getPriorityClass(request) {
    const priority = this.getPriorityLabel(request);
    switch (priority) {
      case "High":
        return "priority-high";
      case "Medium":
        return "priority-medium";
      case "Low":
        return "priority-low";
      default:
        return "";
    }
  }
  quickApprove(request) {
    console.log("Quick approve:", request.id);
  }
  quickReject(request) {
    console.log("Quick reject:", request.id);
  }
  getApprovalPercentage() {
    const total = this.teamMetrics.approvedThisMonth + this.teamMetrics.rejectedThisMonth;
    if (total === 0)
      return 0;
    return this.teamMetrics.approvedThisMonth / total * 100;
  }
  getRejectionPercentage() {
    const total = this.teamMetrics.approvedThisMonth + this.teamMetrics.rejectedThisMonth;
    if (total === 0)
      return 0;
    return this.teamMetrics.rejectedThisMonth / total * 100;
  }
  static \u0275fac = function ManagerDashboardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ManagerDashboardComponent)(\u0275\u0275directiveInject(RequestService), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ManagerDashboardComponent, selectors: [["app-manager-dashboard"]], decls: 96, vars: 13, consts: [[1, "manager-dashboard"], [1, "dashboard-header"], [1, "metrics-grid"], [1, "metric-card", "pending"], [1, "metric"], [1, "metric-icon"], [1, "metric-info"], [1, "metric-card", "team"], [1, "metric-card", "approved"], [1, "metric-card", "time"], [1, "content-card"], [3, "label"], [1, "tab-content"], [1, "tab-header"], ["mat-raised-button", "", "color", "primary", "routerLink", "/requests/approval"], ["class", "requests-table", 4, "ngIf"], ["class", "no-data", 4, "ngIf"], ["label", "Team Overview"], [1, "team-stats"], [1, "stat-card"], [1, "stat-chart"], [1, "chart-placeholder"], [1, "approval-breakdown"], [1, "breakdown-item"], [1, "label"], [1, "value"], ["mode", "determinate", 3, "value"], ["label", "Recent Activity"], [1, "activity-list"], ["class", "activity-item", 4, "ngFor", "ngForOf"], [1, "requests-table"], ["mat-table", "", 3, "dataSource"], ["matColumnDef", "employee"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "type"], ["matColumnDef", "title"], ["matColumnDef", "priority"], ["matColumnDef", "actions"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", ""], ["mat-cell", ""], [1, "employee-info"], [1, "request-title"], [1, "action-buttons"], ["mat-icon-button", "", 3, "routerLink"], ["mat-raised-button", "", "color", "primary", 3, "click"], ["mat-raised-button", "", "color", "warn", 3, "click"], ["mat-header-row", ""], ["mat-row", ""], [1, "no-data"], [1, "activity-item"], [1, "activity-icon"], [1, "activity-content"]], template: function ManagerDashboardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "Manager Dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "Manage your team's requests and approvals");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 2)(7, "mat-card", 3)(8, "mat-card-content")(9, "div", 4)(10, "div", 5)(11, "mat-icon");
      \u0275\u0275text(12, "pending_actions");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(13, "div", 6)(14, "h3");
      \u0275\u0275text(15);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "p");
      \u0275\u0275text(17, "Pending Approvals");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(18, "mat-card", 7)(19, "mat-card-content")(20, "div", 4)(21, "div", 5)(22, "mat-icon");
      \u0275\u0275text(23, "group");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(24, "div", 6)(25, "h3");
      \u0275\u0275text(26);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "p");
      \u0275\u0275text(28, "Team Members");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(29, "mat-card", 8)(30, "mat-card-content")(31, "div", 4)(32, "div", 5)(33, "mat-icon");
      \u0275\u0275text(34, "check_circle");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(35, "div", 6)(36, "h3");
      \u0275\u0275text(37);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(38, "p");
      \u0275\u0275text(39, "Approved This Month");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(40, "mat-card", 9)(41, "mat-card-content")(42, "div", 4)(43, "div", 5)(44, "mat-icon");
      \u0275\u0275text(45, "schedule");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(46, "div", 6)(47, "h3");
      \u0275\u0275text(48);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(49, "p");
      \u0275\u0275text(50, "Avg. Approval Time");
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275elementStart(51, "mat-card", 10)(52, "mat-tab-group")(53, "mat-tab", 11)(54, "div", 12)(55, "div", 13)(56, "h3");
      \u0275\u0275text(57, "Requests Awaiting Your Approval");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "button", 14)(59, "mat-icon");
      \u0275\u0275text(60, "approval");
      \u0275\u0275elementEnd();
      \u0275\u0275text(61, " View All Approvals ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(62, ManagerDashboardComponent_div_62_Template, 19, 3, "div", 15)(63, ManagerDashboardComponent_div_63_Template, 7, 0, "div", 16);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(64, "mat-tab", 17)(65, "div", 12)(66, "div", 18)(67, "div", 19)(68, "h4");
      \u0275\u0275text(69, "Request Volume");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(70, "div", 20)(71, "div", 21)(72, "mat-icon");
      \u0275\u0275text(73, "bar_chart");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(74, "p");
      \u0275\u0275text(75, "Request volume chart would be displayed here");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(76, "div", 19)(77, "h4");
      \u0275\u0275text(78, "Approval Trends");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(79, "div", 22)(80, "div", 23)(81, "span", 24);
      \u0275\u0275text(82, "Approved:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(83, "span", 25);
      \u0275\u0275text(84);
      \u0275\u0275elementEnd();
      \u0275\u0275element(85, "mat-progress-bar", 26);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(86, "div", 23)(87, "span", 24);
      \u0275\u0275text(88, "Rejected:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(89, "span", 25);
      \u0275\u0275text(90);
      \u0275\u0275elementEnd();
      \u0275\u0275element(91, "mat-progress-bar", 26);
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275elementStart(92, "mat-tab", 27)(93, "div", 12)(94, "div", 28);
      \u0275\u0275template(95, ManagerDashboardComponent_div_95_Template, 10, 6, "div", 29);
      \u0275\u0275elementEnd()()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(15);
      \u0275\u0275textInterpolate(ctx.teamMetrics.pendingApprovals);
      \u0275\u0275advance(11);
      \u0275\u0275textInterpolate(ctx.teamMetrics.totalTeamMembers);
      \u0275\u0275advance(11);
      \u0275\u0275textInterpolate(ctx.teamMetrics.approvedThisMonth);
      \u0275\u0275advance(11);
      \u0275\u0275textInterpolate1("", ctx.teamMetrics.averageApprovalTime, "h");
      \u0275\u0275advance(5);
      \u0275\u0275propertyInterpolate1("label", "Pending Approvals (", ctx.pendingRequests.length, ")");
      \u0275\u0275advance(9);
      \u0275\u0275property("ngIf", ctx.pendingRequests.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.pendingRequests.length === 0);
      \u0275\u0275advance(21);
      \u0275\u0275textInterpolate(ctx.teamMetrics.approvedThisMonth);
      \u0275\u0275advance();
      \u0275\u0275property("value", ctx.getApprovalPercentage());
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.teamMetrics.rejectedThisMonth);
      \u0275\u0275advance();
      \u0275\u0275property("value", ctx.getRejectionPercentage());
      \u0275\u0275advance(4);
      \u0275\u0275property("ngForOf", ctx.recentActivities);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, DatePipe, RouterModule, RouterLink, MatCardModule, MatCard, MatCardContent, MatButtonModule, MatButton, MatIconButton, MatIconModule, MatIcon, MatTableModule, MatTable, MatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatHeaderCell, MatCell, MatHeaderRow, MatRow, MatChipsModule, MatChip, MatProgressBarModule, MatProgressBar, MatTabsModule, MatTab, MatTabGroup], styles: ["\n\n.manager-dashboard[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.dashboard-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #333;\n}\n.dashboard-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.5rem 0 0 0;\n  color: #666;\n}\n.metrics-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.metric-card[_ngcontent-%COMP%] {\n  transition: transform 0.2s ease;\n}\n.metric-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n}\n.metric-card.pending[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #ff9800 0%,\n      #f57c00 100%);\n  color: white;\n}\n.metric-card.team[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #2196f3 0%,\n      #1976d2 100%);\n  color: white;\n}\n.metric-card.approved[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #4caf50 0%,\n      #388e3c 100%);\n  color: white;\n}\n.metric-card.time[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #9c27b0 0%,\n      #7b1fa2 100%);\n  color: white;\n}\n.metric[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.metric-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(255, 255, 255, 0.2);\n}\n.metric-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  width: 2rem;\n  height: 2rem;\n}\n.metric-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: bold;\n}\n.metric-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.25rem 0 0 0;\n  font-size: 1rem;\n}\n.content-card[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n}\n.tab-content[_ngcontent-%COMP%] {\n  padding: 1rem;\n}\n.tab-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.requests-table[_ngcontent-%COMP%] {\n  overflow-x: auto;\n}\n.requests-table[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.employee-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.employee-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.8rem;\n}\n.request-title[_ngcontent-%COMP%] {\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.type-leave[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n.type-expense[_ngcontent-%COMP%] {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n}\n.type-training[_ngcontent-%COMP%] {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.type-it[_ngcontent-%COMP%] {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.type-profile[_ngcontent-%COMP%] {\n  background-color: #fce4ec;\n  color: #c2185b;\n}\n.priority-high[_ngcontent-%COMP%] {\n  background-color: #ffebee;\n  color: #c62828;\n}\n.priority-medium[_ngcontent-%COMP%] {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.priority-low[_ngcontent-%COMP%] {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #4caf50;\n}\n.team-stats[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 2rem;\n}\n.stat-card[_ngcontent-%COMP%] {\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.stat-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 1rem 0;\n  color: #333;\n}\n.chart-placeholder[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.chart-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  width: 3rem;\n  height: 3rem;\n  margin-bottom: 1rem;\n}\n.approval-breakdown[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.breakdown-item[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.breakdown-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.activity-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.activity-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n.activity-icon[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: #2196f3;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.activity-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-weight: 500;\n}\n.activity-content[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #666;\n}\n@media (max-width: 768px) {\n  .metrics-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .tab-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n  }\n  .action-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .team-stats[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=manager-dashboard.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ManagerDashboardComponent, [{
    type: Component,
    args: [{ selector: "app-manager-dashboard", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatTableModule,
      MatChipsModule,
      MatProgressBarModule,
      MatTabsModule
    ], template: `
    <div class="manager-dashboard">
      <div class="dashboard-header">
        <h1>Manager Dashboard</h1>
        <p>Manage your team's requests and approvals</p>
      </div>

      <!-- Key Metrics -->
      <div class="metrics-grid">
        <mat-card class="metric-card pending">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>pending_actions</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{teamMetrics.pendingApprovals}}</h3>
                <p>Pending Approvals</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card team">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>group</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{teamMetrics.totalTeamMembers}}</h3>
                <p>Team Members</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card approved">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>check_circle</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{teamMetrics.approvedThisMonth}}</h3>
                <p>Approved This Month</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card time">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>schedule</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{teamMetrics.averageApprovalTime}}h</h3>
                <p>Avg. Approval Time</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Main Content Tabs -->
      <mat-card class="content-card">
        <mat-tab-group>
          <!-- Pending Approvals -->
          <mat-tab label="Pending Approvals ({{pendingRequests.length}})">
            <div class="tab-content">
              <div class="tab-header">
                <h3>Requests Awaiting Your Approval</h3>
                <button mat-raised-button color="primary" routerLink="/requests/approval">
                  <mat-icon>approval</mat-icon>
                  View All Approvals
                </button>
              </div>

              <div *ngIf="pendingRequests.length > 0" class="requests-table">
                <table mat-table [dataSource]="pendingRequests">
                  <!-- Employee Column -->
                  <ng-container matColumnDef="employee">
                    <th mat-header-cell *matHeaderCellDef>Employee</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="employee-info">
                        <strong>{{request.initiatorName}}</strong>
                        <small>{{request.createdAt | date:'short'}}</small>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Request Type Column -->
                  <ng-container matColumnDef="type">
                    <th mat-header-cell *matHeaderCellDef>Type</th>
                    <td mat-cell *matCellDef="let request">
                      <mat-chip [class]="getRequestTypeClass(request.type)">
                        {{getRequestTypeLabel(request.type)}}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Title Column -->
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Request</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="request-title">
                        {{request.title || 'No Title'}}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Priority Column -->
                  <ng-container matColumnDef="priority">
                    <th mat-header-cell *matHeaderCellDef>Priority</th>
                    <td mat-cell *matCellDef="let request">
                      <mat-chip [class]="getPriorityClass(request)">
                        {{getPriorityLabel(request)}}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="action-buttons">
                        <button mat-icon-button [routerLink]="['/requests/details', request.id]">
                          <mat-icon>visibility</mat-icon>
                        </button>
                        <button mat-raised-button color="primary" (click)="quickApprove(request)">
                          <mat-icon>check</mat-icon>
                          Approve
                        </button>
                        <button mat-raised-button color="warn" (click)="quickReject(request)">
                          <mat-icon>close</mat-icon>
                          Reject
                        </button>
                      </div>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="pendingColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: pendingColumns;"></tr>
                </table>
              </div>

              <div *ngIf="pendingRequests.length === 0" class="no-data">
                <mat-icon>assignment_turned_in</mat-icon>
                <h3>No Pending Approvals</h3>
                <p>All requests from your team have been processed.</p>
              </div>
            </div>
          </mat-tab>

          <!-- Team Overview -->
          <mat-tab label="Team Overview">
            <div class="tab-content">
              <div class="team-stats">
                <div class="stat-card">
                  <h4>Request Volume</h4>
                  <div class="stat-chart">
                    <div class="chart-placeholder">
                      <mat-icon>bar_chart</mat-icon>
                      <p>Request volume chart would be displayed here</p>
                    </div>
                  </div>
                </div>

                <div class="stat-card">
                  <h4>Approval Trends</h4>
                  <div class="approval-breakdown">
                    <div class="breakdown-item">
                      <span class="label">Approved:</span>
                      <span class="value">{{teamMetrics.approvedThisMonth}}</span>
                      <mat-progress-bar mode="determinate" [value]="getApprovalPercentage()"></mat-progress-bar>
                    </div>
                    <div class="breakdown-item">
                      <span class="label">Rejected:</span>
                      <span class="value">{{teamMetrics.rejectedThisMonth}}</span>
                      <mat-progress-bar mode="determinate" [value]="getRejectionPercentage()"></mat-progress-bar>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Recent Activity -->
          <mat-tab label="Recent Activity">
            <div class="tab-content">
              <div class="activity-list">
                <div *ngFor="let activity of recentActivities" class="activity-item">
                  <div class="activity-icon">
                    <mat-icon>{{activity.icon}}</mat-icon>
                  </div>
                  <div class="activity-content">
                    <p>{{activity.description}}</p>
                    <small>{{activity.timestamp | date:'short'}}</small>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;41ab2fcb74b5a560f6a274437f8e0dc9c8438dd48225ccbfbf2d83f2ca9504f6;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/dashboard/components/manager-dashboard/manager-dashboard.component.ts */\n.manager-dashboard {\n  padding: 1rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.dashboard-header {\n  margin-bottom: 2rem;\n}\n.dashboard-header h1 {\n  margin: 0;\n  color: #333;\n}\n.dashboard-header p {\n  margin: 0.5rem 0 0 0;\n  color: #666;\n}\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.metric-card {\n  transition: transform 0.2s ease;\n}\n.metric-card:hover {\n  transform: translateY(-2px);\n}\n.metric-card.pending {\n  background:\n    linear-gradient(\n      135deg,\n      #ff9800 0%,\n      #f57c00 100%);\n  color: white;\n}\n.metric-card.team {\n  background:\n    linear-gradient(\n      135deg,\n      #2196f3 0%,\n      #1976d2 100%);\n  color: white;\n}\n.metric-card.approved {\n  background:\n    linear-gradient(\n      135deg,\n      #4caf50 0%,\n      #388e3c 100%);\n  color: white;\n}\n.metric-card.time {\n  background:\n    linear-gradient(\n      135deg,\n      #9c27b0 0%,\n      #7b1fa2 100%);\n  color: white;\n}\n.metric {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.metric-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(255, 255, 255, 0.2);\n}\n.metric-icon mat-icon {\n  font-size: 2rem;\n  width: 2rem;\n  height: 2rem;\n}\n.metric-info h3 {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: bold;\n}\n.metric-info p {\n  margin: 0.25rem 0 0 0;\n  font-size: 1rem;\n}\n.content-card {\n  margin-top: 1rem;\n}\n.tab-content {\n  padding: 1rem;\n}\n.tab-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.requests-table {\n  overflow-x: auto;\n}\n.requests-table table {\n  width: 100%;\n}\n.employee-info {\n  display: flex;\n  flex-direction: column;\n}\n.employee-info small {\n  color: #666;\n  font-size: 0.8rem;\n}\n.request-title {\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.type-leave {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n.type-expense {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n}\n.type-training {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.type-it {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.type-profile {\n  background-color: #fce4ec;\n  color: #c2185b;\n}\n.priority-high {\n  background-color: #ffebee;\n  color: #c62828;\n}\n.priority-medium {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.priority-low {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data mat-icon {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #4caf50;\n}\n.team-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 2rem;\n}\n.stat-card {\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.stat-card h4 {\n  margin: 0 0 1rem 0;\n  color: #333;\n}\n.chart-placeholder {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.chart-placeholder mat-icon {\n  font-size: 3rem;\n  width: 3rem;\n  height: 3rem;\n  margin-bottom: 1rem;\n}\n.approval-breakdown {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.breakdown-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.breakdown-item .label {\n  font-weight: 500;\n}\n.activity-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.activity-item {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n.activity-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: #2196f3;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.activity-content p {\n  margin: 0;\n  font-weight: 500;\n}\n.activity-content small {\n  color: #666;\n}\n@media (max-width: 768px) {\n  .metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  .tab-header {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n  }\n  .action-buttons {\n    flex-direction: column;\n  }\n  .team-stats {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=manager-dashboard.component.css.map */\n"] }]
  }], () => [{ type: RequestService }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ManagerDashboardComponent, { className: "ManagerDashboardComponent", filePath: "src/app/features/dashboard/components/manager-dashboard/manager-dashboard.component.ts", lineNumber: 510 });
})();
export {
  ManagerDashboardComponent
};
//# sourceMappingURL=chunk-NGWM6LMX.js.map
