import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MatCardModule,
  Mat<PERSON>ardTit<PERSON>
} from "./chunk-QRMDFVVO.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-FSO3A3K2.js";
import {
  <PERSON><PERSON><PERSON>on,
  MatButtonModule
} from "./chunk-SVX3GQPM.js";
import {
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-XLTWONBW.js";

// src/app/features/admin/components/workflow-designer/workflow-designer.component.ts
var WorkflowDesignerComponent = class _WorkflowDesignerComponent {
  static \u0275fac = function WorkflowDesignerComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _WorkflowDesignerComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _WorkflowDesignerComponent, selectors: [["app-workflow-designer"]], decls: 51, vars: 0, consts: [[1, "workflow-designer-container"], [1, "header-actions"], ["mat-raised-button", "", "color", "primary"], [1, "designer-placeholder"], [1, "placeholder-icon"], [1, "feature-list"], [1, "feature-item"], [1, "action-buttons"], ["mat-button", ""]], template: function WorkflowDesignerComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title")(4, "mat-icon");
      \u0275\u0275text(5, "account_tree");
      \u0275\u0275elementEnd();
      \u0275\u0275text(6, " Workflow Designer ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "div", 1)(8, "button", 2)(9, "mat-icon");
      \u0275\u0275text(10, "add");
      \u0275\u0275elementEnd();
      \u0275\u0275text(11, " New Workflow ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(12, "mat-card-content")(13, "div", 3)(14, "div", 4)(15, "mat-icon");
      \u0275\u0275text(16, "account_tree");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(17, "h2");
      \u0275\u0275text(18, "Workflow Designer");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "p");
      \u0275\u0275text(20, "Design and manage business process workflows with our visual workflow designer.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "div", 5)(22, "div", 6)(23, "mat-icon");
      \u0275\u0275text(24, "drag_indicator");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "span");
      \u0275\u0275text(26, "Drag & Drop Interface");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(27, "div", 6)(28, "mat-icon");
      \u0275\u0275text(29, "settings");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "span");
      \u0275\u0275text(31, "Configurable Steps");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(32, "div", 6)(33, "mat-icon");
      \u0275\u0275text(34, "people");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(35, "span");
      \u0275\u0275text(36, "Role-based Assignments");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(37, "div", 6)(38, "mat-icon");
      \u0275\u0275text(39, "timeline");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "span");
      \u0275\u0275text(41, "Process Automation");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(42, "div", 7)(43, "button", 2)(44, "mat-icon");
      \u0275\u0275text(45, "add");
      \u0275\u0275elementEnd();
      \u0275\u0275text(46, " Create New Workflow ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(47, "button", 8)(48, "mat-icon");
      \u0275\u0275text(49, "folder_open");
      \u0275\u0275elementEnd();
      \u0275\u0275text(50, " Browse Templates ");
      \u0275\u0275elementEnd()()()()()();
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon
  ], styles: ["\n\n.workflow-designer-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%] {\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e0e0e0;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #333;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #1976d2;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem 2rem;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .placeholder-icon[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .placeholder-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 64px;\n  width: 64px;\n  height: 64px;\n  color: #1976d2;\n  opacity: 0.7;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  margin: 1rem 0;\n  color: #333;\n  font-weight: 500;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 1.1rem;\n  margin-bottom: 2rem;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .feature-list[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n  margin: 2rem 0;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .feature-list[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border-left: 4px solid #1976d2;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .feature-list[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #1976d2;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .feature-list[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #333;\n}\n.workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 2rem;\n  flex-wrap: wrap;\n}\n@media (max-width: 768px) {\n  .workflow-designer-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .workflow-designer-container[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .workflow-designer-container[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\n    width: 100%;\n    justify-content: flex-start;\n  }\n  .workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%] {\n    padding: 2rem 1rem;\n  }\n  .workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .feature-list[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: center;\n  }\n  .workflow-designer-container[_ngcontent-%COMP%]   .designer-placeholder[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n    width: 100%;\n    max-width: 300px;\n  }\n}\n/*# sourceMappingURL=workflow-designer.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WorkflowDesignerComponent, [{
    type: Component,
    args: [{ selector: "app-workflow-designer", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule
    ], template: '<div class="workflow-designer-container">\n  <mat-card>\n    <mat-card-header>\n      <mat-card-title>\n        <mat-icon>account_tree</mat-icon>\n        Workflow Designer\n      </mat-card-title>\n      <div class="header-actions">\n        <button mat-raised-button color="primary">\n          <mat-icon>add</mat-icon>\n          New Workflow\n        </button>\n      </div>\n    </mat-card-header>\n\n    <mat-card-content>\n      <div class="designer-placeholder">\n        <div class="placeholder-icon">\n          <mat-icon>account_tree</mat-icon>\n        </div>\n        <h2>Workflow Designer</h2>\n        <p>Design and manage business process workflows with our visual workflow designer.</p>\n\n        <div class="feature-list">\n          <div class="feature-item">\n            <mat-icon>drag_indicator</mat-icon>\n            <span>Drag & Drop Interface</span>\n          </div>\n          <div class="feature-item">\n            <mat-icon>settings</mat-icon>\n            <span>Configurable Steps</span>\n          </div>\n          <div class="feature-item">\n            <mat-icon>people</mat-icon>\n            <span>Role-based Assignments</span>\n          </div>\n          <div class="feature-item">\n            <mat-icon>timeline</mat-icon>\n            <span>Process Automation</span>\n          </div>\n        </div>\n\n        <div class="action-buttons">\n          <button mat-raised-button color="primary">\n            <mat-icon>add</mat-icon>\n            Create New Workflow\n          </button>\n          <button mat-button>\n            <mat-icon>folder_open</mat-icon>\n            Browse Templates\n          </button>\n        </div>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n', styles: ["/* src/app/features/admin/components/workflow-designer/workflow-designer.component.scss */\n.workflow-designer-container {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.workflow-designer-container mat-card {\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n}\n.workflow-designer-container mat-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #e0e0e0;\n}\n.workflow-designer-container mat-card-header mat-card-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #333;\n}\n.workflow-designer-container mat-card-header mat-card-title mat-icon {\n  color: #1976d2;\n}\n.workflow-designer-container mat-card-header .header-actions {\n  display: flex;\n  gap: 1rem;\n}\n.workflow-designer-container .designer-placeholder {\n  text-align: center;\n  padding: 3rem 2rem;\n}\n.workflow-designer-container .designer-placeholder .placeholder-icon {\n  margin-bottom: 1rem;\n}\n.workflow-designer-container .designer-placeholder .placeholder-icon mat-icon {\n  font-size: 64px;\n  width: 64px;\n  height: 64px;\n  color: #1976d2;\n  opacity: 0.7;\n}\n.workflow-designer-container .designer-placeholder h2 {\n  margin: 1rem 0;\n  color: #333;\n  font-weight: 500;\n}\n.workflow-designer-container .designer-placeholder p {\n  color: #666;\n  font-size: 1.1rem;\n  margin-bottom: 2rem;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n.workflow-designer-container .designer-placeholder .feature-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n  margin: 2rem 0;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n}\n.workflow-designer-container .designer-placeholder .feature-list .feature-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border-left: 4px solid #1976d2;\n}\n.workflow-designer-container .designer-placeholder .feature-list .feature-item mat-icon {\n  color: #1976d2;\n}\n.workflow-designer-container .designer-placeholder .feature-list .feature-item span {\n  font-weight: 500;\n  color: #333;\n}\n.workflow-designer-container .designer-placeholder .action-buttons {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 2rem;\n  flex-wrap: wrap;\n}\n@media (max-width: 768px) {\n  .workflow-designer-container {\n    padding: 1rem;\n  }\n  .workflow-designer-container mat-card-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .workflow-designer-container mat-card-header .header-actions {\n    width: 100%;\n    justify-content: flex-start;\n  }\n  .workflow-designer-container .designer-placeholder {\n    padding: 2rem 1rem;\n  }\n  .workflow-designer-container .designer-placeholder .feature-list {\n    grid-template-columns: 1fr;\n  }\n  .workflow-designer-container .designer-placeholder .action-buttons {\n    flex-direction: column;\n    align-items: center;\n  }\n  .workflow-designer-container .designer-placeholder .action-buttons button {\n    width: 100%;\n    max-width: 300px;\n  }\n}\n/*# sourceMappingURL=workflow-designer.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(WorkflowDesignerComponent, { className: "WorkflowDesignerComponent", filePath: "src/app/features/admin/components/workflow-designer/workflow-designer.component.ts", lineNumber: 21 });
})();
export {
  WorkflowDesignerComponent
};
//# sourceMappingURL=chunk-BHPM5ISZ.js.map
