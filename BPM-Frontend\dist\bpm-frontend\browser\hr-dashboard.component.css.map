{"version": 3, "sources": ["src/app/features/dashboard/components/hr-dashboard/hr-dashboard.component.ts"], "sourcesContent": ["\n    .hr-dashboard {\n      padding: 1rem;\n      max-width: 1400px;\n      margin: 0 auto;\n    }\n\n    .dashboard-header {\n      margin-bottom: 2rem;\n    }\n\n    .dashboard-header h1 {\n      margin: 0;\n      color: #333;\n    }\n\n    .dashboard-header p {\n      margin: 0.5rem 0 0 0;\n      color: #666;\n    }\n\n    .metrics-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 1rem;\n      margin-bottom: 2rem;\n    }\n\n    .metric-card {\n      transition: transform 0.2s ease;\n    }\n\n    .metric-card:hover {\n      transform: translateY(-2px);\n    }\n\n    .metric-card.employees {\n      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);\n      color: white;\n    }\n\n    .metric-card.active {\n      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);\n      color: white;\n    }\n\n    .metric-card.processed {\n      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);\n      color: white;\n    }\n\n    .metric-card.time {\n      background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);\n      color: white;\n    }\n\n    .metric {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .metric-icon {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: rgba(255, 255, 255, 0.2);\n    }\n\n    .metric-icon mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n    }\n\n    .metric-info h3 {\n      margin: 0;\n      font-size: 2rem;\n      font-weight: bold;\n    }\n\n    .metric-info p {\n      margin: 0.25rem 0 0 0;\n      font-size: 1rem;\n    }\n\n    .request-types-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 1rem;\n      margin-bottom: 2rem;\n    }\n\n    .type-card {\n      background-color: #f8f9fa;\n      border-left: 4px solid;\n    }\n\n    .type-card.leave {\n      border-left-color: #2196f3;\n    }\n\n    .type-card.expense {\n      border-left-color: #ff9800;\n    }\n\n    .type-card.training {\n      border-left-color: #4caf50;\n    }\n\n    .type-metric {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .type-metric mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n      color: #666;\n    }\n\n    .type-info h4 {\n      margin: 0;\n      font-size: 1.5rem;\n      color: #333;\n    }\n\n    .type-info p {\n      margin: 0.25rem 0 0 0;\n      color: #666;\n    }\n\n    .content-card {\n      margin-top: 1rem;\n    }\n\n    .tab-content {\n      padding: 1rem;\n    }\n\n    .tab-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .requests-table {\n      overflow-x: auto;\n    }\n\n    .requests-table table {\n      width: 100%;\n    }\n\n    .employee-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .employee-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .request-title {\n      max-width: 200px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .current-step {\n      background-color: #e3f2fd;\n      color: #1976d2;\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 0.8rem;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 0.5rem;\n      align-items: center;\n    }\n\n    .type-leave { background-color: #e3f2fd; color: #1976d2; }\n    .type-expense { background-color: #f3e5f5; color: #7b1fa2; }\n    .type-training { background-color: #e8f5e8; color: #2e7d32; }\n    .type-it { background-color: #fff3e0; color: #ef6c00; }\n    .type-profile { background-color: #fce4ec; color: #c2185b; }\n\n    .no-data {\n      text-align: center;\n      padding: 3rem;\n      color: #666;\n    }\n\n    .no-data mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #4caf50;\n    }\n\n    .employee-stats {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 2rem;\n    }\n\n    .stat-section {\n      padding: 1rem;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      background-color: #fafafa;\n    }\n\n    .stat-section h4 {\n      margin: 0 0 1rem 0;\n      color: #333;\n    }\n\n    .department-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .department-item {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n    }\n\n    .dept-name {\n      font-weight: 500;\n    }\n\n    .dept-count {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .recent-hires {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .hire-item {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .hire-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background-color: #2196f3;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n      font-size: 0.9rem;\n    }\n\n    .hire-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .hire-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .analytics-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 2rem;\n    }\n\n    .analytics-card {\n      padding: 1rem;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      background-color: #fafafa;\n    }\n\n    .analytics-card h4 {\n      margin: 0 0 1rem 0;\n      color: #333;\n    }\n\n    .chart-placeholder {\n      text-align: center;\n      padding: 2rem;\n      color: #666;\n    }\n\n    .chart-placeholder mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    .efficiency-metrics {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .efficiency-item {\n      display: flex;\n      justify-content: space-between;\n      padding: 0.5rem;\n      background-color: white;\n      border-radius: 4px;\n    }\n\n    .efficiency-item .label {\n      font-weight: 500;\n    }\n\n    .efficiency-item .value {\n      color: #2196f3;\n      font-weight: bold;\n    }\n\n    @media (max-width: 768px) {\n      .metrics-grid {\n        grid-template-columns: repeat(2, 1fr);\n      }\n\n      .request-types-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .tab-header {\n        flex-direction: column;\n        align-items: stretch;\n        gap: 1rem;\n      }\n\n      .action-buttons {\n        flex-direction: column;\n      }\n\n      .employee-stats,\n      .analytics-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .metrics-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAJA,iBAIA;AACE,UAAA;AACA,SAAA;;AAGF,CATA,iBASA;AACE,UAAA,OAAA,EAAA,EAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,cAAA,UAAA,KAAA;;AAGF,CAJA,WAIA;AACE,aAAA,WAAA;;AAGF,CARA,WAQA,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,SAAA;;AAGF,CAbA,WAaA,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,SAAA;;AAGF,CAlBA,WAkBA,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,SAAA;;AAGF,CAvBA,WAuBA,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CAVA,YAUA;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA,YAAA;AACE,UAAA;AACA,aAAA;AACA,eAAA;;AAGF,CANA,YAMA;AACE,UAAA,QAAA,EAAA,EAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,oBAAA;AACA,eAAA,IAAA;;AAGF,CALA,SAKA,CAAA;AACE,qBAAA;;AAGF,CATA,SASA,CAAA;AACE,qBAAA;;AAGF,CAbA,SAaA,CAAA;AACE,qBAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CANA,YAMA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;;AAGF,CAAA,UAAA;AACE,UAAA;AACA,aAAA;AACA,SAAA;;AAGF,CANA,UAMA;AACE,UAAA,QAAA,EAAA,EAAA;AACA,SAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAJA,eAIA;AACE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CALA,cAKA;AACE,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,aAAA;AACA,YAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,eAAA;;AAGF,CAAA;AAAc,oBAAA;AAA2B,SAAA;;AACzC,CAAA;AAAgB,oBAAA;AAA2B,SAAA;;AAC3C,CAAA;AAAiB,oBAAA;AAA2B,SAAA;;AAC5C,CAAA;AAAW,oBAAA;AAA2B,SAAA;;AACtC,CAAA;AAAgB,oBAAA;AAA2B,SAAA;;AAE3C,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;;AAGF,CANA,QAMA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,oBAAA;;AAGF,CAPA,aAOA;AACE,UAAA,EAAA,EAAA,KAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,eAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;AACA,oBAAA;AACA,SAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CALA,UAKA;AACE,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,oBAAA;;AAGF,CAPA,eAOA;AACE,UAAA,EAAA,EAAA,KAAA;AACA,SAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;;AAGF,CANA,kBAMA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,WAAA;AACA,oBAAA;AACA,iBAAA;;AAGF,CARA,gBAQA,CAAA;AACE,eAAA;;AAGF,CAZA,gBAYA,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GA/TF;AAgUI,2BAAA,OAAA,CAAA,EAAA;;AAGF,GA/PF;AAgQI,2BAAA;;AAGF,GA3MF;AA4MI,oBAAA;AACA,iBAAA;AACA,SAAA;;AAGF,GAzKF;AA0KI,oBAAA;;AAGF,GAnJF;EAmJE,CAzEF;AA2EI,2BAAA;;;AAIJ,OAAA,CAAA,SAAA,EAAA;AACE,GAxVF;AAyVI,2BAAA;;;", "names": []}