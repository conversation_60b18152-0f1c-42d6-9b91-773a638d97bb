{"version": 3, "sources": ["src/app/features/admin/components/role-management/role-management.component.ts"], "sourcesContent": ["\n    .role-management-container {\n      padding: 1rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    mat-card {\n      margin-bottom: 1rem;\n    }\n\n    mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .roles-table {\n      width: 100%;\n    }\n\n    .role-row:hover {\n      background-color: #f5f5f5;\n    }\n\n    .role-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .role-info strong {\n      font-size: 1rem;\n    }\n\n    .role-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .permissions-container {\n      display: flex;\n      gap: 0.25rem;\n      flex-wrap: wrap;\n      align-items: center;\n    }\n\n    .permission-chip {\n      background-color: #e3f2fd;\n      color: #1976d2;\n      font-size: 0.75rem;\n    }\n\n    .more-permissions {\n      color: #666;\n      font-size: 0.8rem;\n      font-style: italic;\n    }\n\n    .user-count {\n      color: #666;\n      font-size: 0.9rem;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 0.25rem;\n    }\n\n    .permissions-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 1rem;\n    }\n\n    .permission-category {\n      padding: 1rem;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      background-color: #fafafa;\n    }\n\n    .permission-category h4 {\n      margin: 0 0 1rem 0;\n      color: #333;\n    }\n\n    .permission-list {\n      display: flex;\n      gap: 0.25rem;\n      flex-wrap: wrap;\n    }\n\n    @media (max-width: 768px) {\n      .permissions-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .action-buttons {\n        flex-direction: column;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF;AACE,iBAAA;;AAGF;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA,QAAA;AACE,oBAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CALA,UAKA;AACE,aAAA;;AAGF,CATA,UASA;AACE,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,oBAAA;;AAGF,CAPA,oBAOA;AACE,UAAA,EAAA,EAAA,KAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,aAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GAzBF;AA0BI,2BAAA;;AAGF,GAlCF;AAmCI,oBAAA;;;", "names": []}