// src/app/core/models/notification.models.ts
var NotificationType;
(function(NotificationType2) {
  NotificationType2[NotificationType2["Info"] = 1] = "Info";
  NotificationType2[NotificationType2["Warning"] = 2] = "Warning";
  NotificationType2[NotificationType2["Error"] = 3] = "Error";
  NotificationType2[NotificationType2["Success"] = 4] = "Success";
  NotificationType2[NotificationType2["RequestUpdate"] = 5] = "RequestUpdate";
  NotificationType2[NotificationType2["WorkflowUpdate"] = 6] = "WorkflowUpdate";
})(NotificationType || (NotificationType = {}));

export {
  NotificationType
};
//# sourceMappingURL=chunk-CPP3G34D.js.map
