import {
  Mat<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MatStepperModule
} from "./chunk-XG3MANJW.js";
import "./chunk-H3UX3NVF.js";
import {
  MatTooltipModule
} from "./chunk-F4VZNS7P.js";
import {
  Mat<PERSON><PERSON><PERSON>,
  MatDividerModule
} from "./chunk-M6SWN32G.js";
import "./chunk-DQTTXTF2.js";
import "./chunk-CPP3G34D.js";
import {
  RequestService
} from "./chunk-AM3AE65N.js";
import {
  MatChip,
  MatChipsModule
} from "./chunk-KJM35DAP.js";
import {
  RequestStatus,
  RequestType,
  StepStatus
} from "./chunk-XJ5TS5V6.js";
import "./chunk-PJGOPMTU.js";
import "./chunk-4WCUDP7B.js";
import {
  MatProgressSpinner,
  MatProgressSpinnerModule
} from "./chunk-PIT7R6CL.js";
import {
  AuthService
} from "./chunk-EP3FZZM6.js";
import "./chunk-FTI6XIW5.js";
import "./chunk-R6R3KTSK.js";
import {
  MatCard,
  MatCardActions,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle
} from "./chunk-QRMDFVVO.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-FSO3A3K2.js";
import {
  MatButton,
  MatButtonModule
} from "./chunk-SVX3GQPM.js";
import {
  ActivatedRoute,
  Router,
  RouterLink,
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  Component,
  DatePipe,
  NgForOf,
  NgIf,
  Subject,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-XLTWONBW.js";

// src/app/features/requests/components/request-details/request-details.component.ts
var _c0 = (a0) => ["/requests/edit", a0];
function RequestDetailsComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4);
    \u0275\u0275element(1, "mat-spinner");
    \u0275\u0275elementEnd();
  }
}
function RequestDetailsComponent_div_2_div_33_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "strong");
    \u0275\u0275text(2, "Last Updated:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4);
    \u0275\u0275pipe(5, "date");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(5, 1, ctx_r0.request.updatedAt, "full"));
  }
}
function RequestDetailsComponent_div_2_div_35_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19)(1, "h3");
    \u0275\u0275text(2, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r0.request.description);
  }
}
function RequestDetailsComponent_div_2_button_41_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "button", 20)(1, "mat-icon");
    \u0275\u0275text(2, "edit");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " Edit Request ");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(1, _c0, ctx_r0.request.id));
  }
}
function RequestDetailsComponent_div_2_mat_step_50_ng_template_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27)(1, "span", 28);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 29);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const step_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(step_r2.workflowStepName);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("(", step_r2.responsibleRole, ")");
  }
}
function RequestDetailsComponent_div_2_mat_step_50_div_6_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 32)(1, "strong");
    \u0275\u0275text(2, "Processed by:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const step_r2 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", step_r2.validatorName, " ");
  }
}
function RequestDetailsComponent_div_2_mat_step_50_div_6_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 32)(1, "strong");
    \u0275\u0275text(2, "Date:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275pipe(4, "date");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const step_r2 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(4, 1, step_r2.validatedAt, "short"), " ");
  }
}
function RequestDetailsComponent_div_2_mat_step_50_div_6_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 32)(1, "strong");
    \u0275\u0275text(2, "Comments:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p", 33);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const step_r2 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(step_r2.comments);
  }
}
function RequestDetailsComponent_div_2_mat_step_50_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 30);
    \u0275\u0275template(1, RequestDetailsComponent_div_2_mat_step_50_div_6_div_1_Template, 4, 1, "div", 31)(2, RequestDetailsComponent_div_2_mat_step_50_div_6_div_2_Template, 5, 4, "div", 31)(3, RequestDetailsComponent_div_2_mat_step_50_div_6_div_3_Template, 5, 1, "div", 31);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const step_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", step_r2.validatorName);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", step_r2.validatedAt);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", step_r2.comments);
  }
}
function RequestDetailsComponent_div_2_mat_step_50_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34)(1, "mat-icon");
    \u0275\u0275text(2, "schedule");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const step_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1("Waiting for approval from ", step_r2.responsibleRole, "");
  }
}
function RequestDetailsComponent_div_2_mat_step_50_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-step", 21);
    \u0275\u0275template(1, RequestDetailsComponent_div_2_mat_step_50_ng_template_1_Template, 5, 2, "ng-template", 22);
    \u0275\u0275elementStart(2, "div", 23)(3, "div", 24)(4, "mat-chip");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(6, RequestDetailsComponent_div_2_mat_step_50_div_6_Template, 4, 3, "div", 25)(7, RequestDetailsComponent_div_2_mat_step_50_div_7_Template, 5, 1, "div", 26);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const step_r2 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("completed", step_r2.status === ctx_r0.StepStatus.Approved)("hasError", step_r2.status === ctx_r0.StepStatus.Rejected);
    \u0275\u0275advance(4);
    \u0275\u0275classMap(ctx_r0.getStepStatusClass(step_r2.status));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStepStatusLabel(step_r2.status), " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", step_r2.validatedAt || step_r2.validatorName);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", step_r2.status === ctx_r0.StepStatus.Pending);
  }
}
function RequestDetailsComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "mat-card", 5)(2, "mat-card-header")(3, "mat-card-title")(4, "div", 6)(5, "mat-icon");
    \u0275\u0275text(6, "assignment");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div")(8, "h2");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "span", 7);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(12, "div", 8)(13, "mat-chip");
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(15, "mat-card-content")(16, "div", 9)(17, "div", 10)(18, "strong");
    \u0275\u0275text(19, "Request ID:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "span", 11);
    \u0275\u0275text(21);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "div", 10)(23, "strong");
    \u0275\u0275text(24, "Submitted by:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "span");
    \u0275\u0275text(26);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(27, "div", 10)(28, "strong");
    \u0275\u0275text(29, "Created:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "span");
    \u0275\u0275text(31);
    \u0275\u0275pipe(32, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(33, RequestDetailsComponent_div_2_div_33_Template, 6, 4, "div", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275element(34, "mat-divider");
    \u0275\u0275template(35, RequestDetailsComponent_div_2_div_35_Template, 5, 1, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(36, "mat-card-actions")(37, "button", 14)(38, "mat-icon");
    \u0275\u0275text(39, "arrow_back");
    \u0275\u0275elementEnd();
    \u0275\u0275text(40, " Back to Requests ");
    \u0275\u0275elementEnd();
    \u0275\u0275template(41, RequestDetailsComponent_div_2_button_41_Template, 4, 3, "button", 15);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(42, "mat-card", 16)(43, "mat-card-header")(44, "mat-card-title")(45, "mat-icon");
    \u0275\u0275text(46, "timeline");
    \u0275\u0275elementEnd();
    \u0275\u0275text(47, " Workflow Progress ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(48, "mat-card-content")(49, "mat-stepper", 17);
    \u0275\u0275template(50, RequestDetailsComponent_div_2_mat_step_50_Template, 8, 7, "mat-step", 18);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(9);
    \u0275\u0275textInterpolate(ctx_r0.request.title || "Request Details");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.getRequestTypeLabel(ctx_r0.request.type));
    \u0275\u0275advance(2);
    \u0275\u0275classMap(ctx_r0.getStatusClass(ctx_r0.request.status));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStatusLabel(ctx_r0.request.status), " ");
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r0.request.id);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.request.initiatorName);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(32, 13, ctx_r0.request.createdAt, "full"));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.request.updatedAt);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.request.description);
    \u0275\u0275advance(6);
    \u0275\u0275property("ngIf", ctx_r0.canEditRequest());
    \u0275\u0275advance(8);
    \u0275\u0275property("linear", false);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.request.requestSteps);
  }
}
function RequestDetailsComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 35)(1, "mat-card")(2, "mat-card-content")(3, "div", 36)(4, "mat-icon");
    \u0275\u0275text(5, "error");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "h3");
    \u0275\u0275text(7, "Request Not Found");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9, "The request you're looking for doesn't exist or you don't have permission to view it.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "button", 37);
    \u0275\u0275text(11, " Back to Requests ");
    \u0275\u0275elementEnd()()()()();
  }
}
var RequestDetailsComponent = class _RequestDetailsComponent {
  route;
  router;
  requestService;
  authService;
  destroy$ = new Subject();
  request = null;
  loading = false;
  requestId;
  // Enums for template
  RequestStatus = RequestStatus;
  RequestType = RequestType;
  StepStatus = StepStatus;
  constructor(route, router, requestService, authService) {
    this.route = route;
    this.router = router;
    this.requestService = requestService;
    this.authService = authService;
    this.requestId = this.route.snapshot.params["id"];
  }
  ngOnInit() {
    this.loadRequestDetails();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadRequestDetails() {
    this.loading = true;
    this.requestService.getRequestById(this.requestId).pipe(takeUntil(this.destroy$)).subscribe({
      next: (request) => {
        this.request = request;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading request details:", error);
        this.request = null;
        this.loading = false;
      }
    });
  }
  getRequestTypeLabel(type) {
    switch (type) {
      case RequestType.Leave:
        return "Leave Request";
      case RequestType.Expense:
        return "Expense Report";
      case RequestType.Training:
        return "Training Request";
      case RequestType.ITSupport:
        return "IT Support";
      case RequestType.ProfileUpdate:
        return "Profile Update";
      default:
        return "Unknown";
    }
  }
  getStatusLabel(status) {
    switch (status) {
      case RequestStatus.Pending:
        return "Pending";
      case RequestStatus.Approved:
        return "Approved";
      case RequestStatus.Rejected:
        return "Rejected";
      case RequestStatus.Archived:
        return "Archived";
      default:
        return "Unknown";
    }
  }
  getStatusClass(status) {
    switch (status) {
      case RequestStatus.Pending:
        return "status-pending";
      case RequestStatus.Approved:
        return "status-approved";
      case RequestStatus.Rejected:
        return "status-rejected";
      case RequestStatus.Archived:
        return "status-archived";
      default:
        return "";
    }
  }
  getStepStatusLabel(status) {
    switch (status) {
      case StepStatus.Pending:
        return "Pending";
      case StepStatus.Approved:
        return "Approved";
      case StepStatus.Rejected:
        return "Rejected";
      default:
        return "Unknown";
    }
  }
  getStepStatusClass(status) {
    switch (status) {
      case StepStatus.Pending:
        return "step-status-pending";
      case StepStatus.Approved:
        return "step-status-approved";
      case StepStatus.Rejected:
        return "step-status-rejected";
      default:
        return "";
    }
  }
  canEditRequest() {
    if (!this.request)
      return false;
    const currentUser = this.authService.getCurrentUser();
    return this.request.status === RequestStatus.Pending && this.request.initiatorId === currentUser?.id;
  }
  static \u0275fac = function RequestDetailsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RequestDetailsComponent)(\u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(RequestService), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RequestDetailsComponent, selectors: [["app-request-details"]], decls: 4, vars: 3, consts: [[1, "request-details-container"], ["class", "loading-container", 4, "ngIf"], [4, "ngIf"], ["class", "error-container", 4, "ngIf"], [1, "loading-container"], [1, "header-card"], [1, "title-section"], [1, "request-type"], [1, "status-section"], [1, "request-info"], [1, "info-item"], [1, "request-id"], ["class", "info-item", 4, "ngIf"], ["class", "description-section", 4, "ngIf"], ["mat-button", "", "routerLink", "/requests"], ["mat-raised-button", "", "color", "primary", 3, "routerLink", 4, "ngIf"], [1, "workflow-card"], ["orientation", "vertical", 1, "workflow-stepper", 3, "linear"], [3, "completed", "hasError", 4, "ngFor", "ngForOf"], [1, "description-section"], ["mat-raised-button", "", "color", "primary", 3, "routerLink"], [3, "completed", "hasError"], ["matStepLabel", ""], [1, "step-content"], [1, "step-status"], ["class", "step-details", 4, "ngIf"], ["class", "pending-message", 4, "ngIf"], [1, "step-label"], [1, "step-name"], [1, "step-role"], [1, "step-details"], ["class", "step-detail", 4, "ngIf"], [1, "step-detail"], [1, "step-comments"], [1, "pending-message"], [1, "error-container"], [1, "error-content"], ["mat-raised-button", "", "color", "primary", "routerLink", "/requests"]], template: function RequestDetailsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, RequestDetailsComponent_div_1_Template, 2, 0, "div", 1)(2, RequestDetailsComponent_div_2_Template, 51, 16, "div", 2)(3, RequestDetailsComponent_div_3_Template, 12, 0, "div", 3);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.request);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && !ctx.request);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, DatePipe, RouterModule, RouterLink, MatCardModule, MatCard, MatCardActions, MatCardContent, MatCardHeader, MatCardTitle, MatButtonModule, MatButton, MatIconModule, MatIcon, MatChipsModule, MatChip, MatDividerModule, MatDivider, MatProgressSpinnerModule, MatProgressSpinner, MatStepperModule, MatStep, MatStepLabel, MatStepper, MatTooltipModule], styles: ["\n\n.request-details-container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 3rem;\n}\n.header-card[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.title-section[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.title-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 1.5rem;\n}\n.request-type[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n  font-weight: normal;\n}\nmat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  width: 100%;\n}\n.request-info[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin: 1rem 0;\n}\n.info-item[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.request-id[_ngcontent-%COMP%] {\n  font-family: monospace;\n  background-color: #f5f5f5;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.9rem;\n}\n.description-section[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n}\n.description-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n  color: #333;\n}\n.description-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  line-height: 1.6;\n  color: #666;\n}\n.workflow-card[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n}\n.workflow-stepper[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n}\n.step-label[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n.step-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.step-role[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #666;\n}\n.step-content[_ngcontent-%COMP%] {\n  padding: 1rem 0;\n}\n.step-status[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.step-details[_ngcontent-%COMP%] {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 4px;\n  margin-top: 1rem;\n}\n.step-detail[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n}\n.step-comments[_ngcontent-%COMP%] {\n  margin: 0.5rem 0 0 0;\n  padding: 0.5rem;\n  background-color: white;\n  border-left: 3px solid #2196f3;\n  border-radius: 0 4px 4px 0;\n}\n.pending-message[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #ff9800;\n  font-style: italic;\n}\n.status-pending[_ngcontent-%COMP%] {\n  background-color: #fff3cd;\n  color: #856404;\n}\n.status-approved[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n}\n.status-rejected[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.status-archived[_ngcontent-%COMP%] {\n  background-color: #e2e3e5;\n  color: #383d41;\n}\n.step-status-pending[_ngcontent-%COMP%] {\n  background-color: #fff3cd;\n  color: #856404;\n}\n.step-status-approved[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n}\n.step-status-rejected[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.error-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.error-content[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n}\n.error-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  color: #f44336;\n  margin-bottom: 1rem;\n}\n@media (max-width: 768px) {\n  mat-card-title[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .request-info[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=request-details.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RequestDetailsComponent, [{
    type: Component,
    args: [{ selector: "app-request-details", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatChipsModule,
      MatDividerModule,
      MatProgressSpinnerModule,
      MatStepperModule,
      MatTooltipModule
    ], template: `
    <div class="request-details-container">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
      </div>

      <!-- Request Details -->
      <div *ngIf="!loading && request">
        <!-- Header Card -->
        <mat-card class="header-card">
          <mat-card-header>
            <mat-card-title>
              <div class="title-section">
                <mat-icon>assignment</mat-icon>
                <div>
                  <h2>{{request.title || 'Request Details'}}</h2>
                  <span class="request-type">{{getRequestTypeLabel(request.type)}}</span>
                </div>
              </div>
              <div class="status-section">
                <mat-chip [class]="getStatusClass(request.status)">
                  {{getStatusLabel(request.status)}}
                </mat-chip>
              </div>
            </mat-card-title>
          </mat-card-header>

          <mat-card-content>
            <div class="request-info">
              <div class="info-item">
                <strong>Request ID:</strong>
                <span class="request-id">{{request.id}}</span>
              </div>
              <div class="info-item">
                <strong>Submitted by:</strong>
                <span>{{request.initiatorName}}</span>
              </div>
              <div class="info-item">
                <strong>Created:</strong>
                <span>{{request.createdAt | date:'full'}}</span>
              </div>
              <div class="info-item" *ngIf="request.updatedAt">
                <strong>Last Updated:</strong>
                <span>{{request.updatedAt | date:'full'}}</span>
              </div>
            </div>

            <mat-divider></mat-divider>

            <div class="description-section" *ngIf="request.description">
              <h3>Description</h3>
              <p>{{request.description}}</p>
            </div>
          </mat-card-content>

          <mat-card-actions>
            <button mat-button routerLink="/requests">
              <mat-icon>arrow_back</mat-icon>
              Back to Requests
            </button>
            <button mat-raised-button color="primary" *ngIf="canEditRequest()" [routerLink]="['/requests/edit', request.id]">
              <mat-icon>edit</mat-icon>
              Edit Request
            </button>
          </mat-card-actions>
        </mat-card>

        <!-- Workflow Steps -->
        <mat-card class="workflow-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>timeline</mat-icon>
              Workflow Progress
            </mat-card-title>
          </mat-card-header>

          <mat-card-content>
            <mat-stepper [linear]="false" orientation="vertical" class="workflow-stepper">
              <mat-step *ngFor="let step of request.requestSteps; let i = index" 
                        [completed]="step.status === StepStatus.Approved"
                        [hasError]="step.status === StepStatus.Rejected">
                <ng-template matStepLabel>
                  <div class="step-label">
                    <span class="step-name">{{step.workflowStepName}}</span>
                    <span class="step-role">({{step.responsibleRole}})</span>
                  </div>
                </ng-template>

                <div class="step-content">
                  <div class="step-status">
                    <mat-chip [class]="getStepStatusClass(step.status)">
                      {{getStepStatusLabel(step.status)}}
                    </mat-chip>
                  </div>

                  <div class="step-details" *ngIf="step.validatedAt || step.validatorName">
                    <div *ngIf="step.validatorName" class="step-detail">
                      <strong>Processed by:</strong> {{step.validatorName}}
                    </div>
                    <div *ngIf="step.validatedAt" class="step-detail">
                      <strong>Date:</strong> {{step.validatedAt | date:'short'}}
                    </div>
                    <div *ngIf="step.comments" class="step-detail">
                      <strong>Comments:</strong>
                      <p class="step-comments">{{step.comments}}</p>
                    </div>
                  </div>

                  <div *ngIf="step.status === StepStatus.Pending" class="pending-message">
                    <mat-icon>schedule</mat-icon>
                    <span>Waiting for approval from {{step.responsibleRole}}</span>
                  </div>
                </div>
              </mat-step>
            </mat-stepper>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Error State -->
      <div *ngIf="!loading && !request" class="error-container">
        <mat-card>
          <mat-card-content>
            <div class="error-content">
              <mat-icon>error</mat-icon>
              <h3>Request Not Found</h3>
              <p>The request you're looking for doesn't exist or you don't have permission to view it.</p>
              <button mat-raised-button color="primary" routerLink="/requests">
                Back to Requests
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;75b39ec012bb3e7a4b6ffec037ac59543bce2e07dc81b17d22ef4d61f30bf31a;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/requests/components/request-details/request-details.component.ts */\n.request-details-container {\n  padding: 1rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 3rem;\n}\n.header-card {\n  margin-bottom: 1rem;\n}\n.title-section {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.title-section h2 {\n  margin: 0;\n  font-size: 1.5rem;\n}\n.request-type {\n  color: #666;\n  font-size: 0.9rem;\n  font-weight: normal;\n}\nmat-card-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  width: 100%;\n}\n.request-info {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin: 1rem 0;\n}\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.request-id {\n  font-family: monospace;\n  background-color: #f5f5f5;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.9rem;\n}\n.description-section {\n  margin-top: 1rem;\n}\n.description-section h3 {\n  margin-bottom: 0.5rem;\n  color: #333;\n}\n.description-section p {\n  line-height: 1.6;\n  color: #666;\n}\n.workflow-card {\n  margin-top: 1rem;\n}\n.workflow-stepper {\n  margin-top: 1rem;\n}\n.step-label {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n.step-name {\n  font-weight: 500;\n}\n.step-role {\n  font-size: 0.8rem;\n  color: #666;\n}\n.step-content {\n  padding: 1rem 0;\n}\n.step-status {\n  margin-bottom: 1rem;\n}\n.step-details {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 4px;\n  margin-top: 1rem;\n}\n.step-detail {\n  margin-bottom: 0.5rem;\n}\n.step-comments {\n  margin: 0.5rem 0 0 0;\n  padding: 0.5rem;\n  background-color: white;\n  border-left: 3px solid #2196f3;\n  border-radius: 0 4px 4px 0;\n}\n.pending-message {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #ff9800;\n  font-style: italic;\n}\n.status-pending {\n  background-color: #fff3cd;\n  color: #856404;\n}\n.status-approved {\n  background-color: #d4edda;\n  color: #155724;\n}\n.status-rejected {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.status-archived {\n  background-color: #e2e3e5;\n  color: #383d41;\n}\n.step-status-pending {\n  background-color: #fff3cd;\n  color: #856404;\n}\n.step-status-approved {\n  background-color: #d4edda;\n  color: #155724;\n}\n.step-status-rejected {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.error-container {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.error-content {\n  text-align: center;\n  padding: 2rem;\n}\n.error-content mat-icon {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  color: #f44336;\n  margin-bottom: 1rem;\n}\n@media (max-width: 768px) {\n  mat-card-title {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .request-info {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=request-details.component.css.map */\n"] }]
  }], () => [{ type: ActivatedRoute }, { type: Router }, { type: RequestService }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RequestDetailsComponent, { className: "RequestDetailsComponent", filePath: "src/app/features/requests/components/request-details/request-details.component.ts", lineNumber: 345 });
})();
export {
  RequestDetailsComponent
};
//# sourceMappingURL=chunk-VDO2VFJD.js.map
