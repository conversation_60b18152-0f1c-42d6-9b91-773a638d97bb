{"version": 3, "sources": ["src/app/features/profile/profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { AuthGuard } from '../../core/guards';\n\nconst routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/user-profile/user-profile.component').then(c => c.UserProfileComponent),\n    canActivate: [AuthGuard]\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class ProfileModule { }\n"], "mappings": ";;;;;;;;;;;;;;;;AAIA,IAAM,SAAS;EACb;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAkD,EAAE,KAAK,OAAK,EAAE,oBAAoB;IAChH,aAAa,CAAC,SAAS;;;AAQrB,IAAO,gBAAP,MAAO,eAAa;;qCAAb,gBAAa;EAAA;wEAAb,eAAa,CAAA;4EAHd,aAAa,SAAS,MAAM,GAC5B,YAAY,EAAA,CAAA;;;sEAEX,eAAa,CAAA;UAJzB;WAAS;MACR,SAAS,CAAC,aAAa,SAAS,MAAM,CAAC;MACvC,SAAS,CAAC,YAAY;KACvB;;;", "names": []}