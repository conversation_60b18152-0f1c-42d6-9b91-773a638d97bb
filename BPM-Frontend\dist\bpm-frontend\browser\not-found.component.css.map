{"version": 3, "sources": ["src/app/shared/components/not-found/not-found.component.ts"], "sourcesContent": ["\n    .not-found-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 2rem;\n    }\n\n    .not-found-card {\n      max-width: 500px;\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .error-code {\n      font-size: 6rem;\n      font-weight: bold;\n      color: #667eea;\n      margin-bottom: 1rem;\n      line-height: 1;\n    }\n\n    .error-icon {\n      margin-bottom: 1rem;\n    }\n\n    .error-icon mat-icon {\n      font-size: 48px;\n      width: 48px;\n      height: 48px;\n      color: #666;\n    }\n\n    h1 {\n      color: #333;\n      margin-bottom: 1rem;\n    }\n\n    p {\n      color: #666;\n      margin-bottom: 1rem;\n      line-height: 1.6;\n    }\n\n    .actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      flex-wrap: wrap;\n      margin-top: 2rem;\n    }\n\n    @media (max-width: 480px) {\n      .error-code {\n        font-size: 4rem;\n      }\n      \n      .actions {\n        flex-direction: column;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,WAAA;;AAGF,CAAA;AACE,aAAA;AACA,cAAA;AACA,WAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAJA,WAIA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;;AAGF;AACE,SAAA;AACA,iBAAA;;AAGF;AACE,SAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,mBAAA;AACA,aAAA;AACA,cAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GAvCF;AAwCI,eAAA;;AAGF,GAbF;AAcI,oBAAA;;;", "names": []}