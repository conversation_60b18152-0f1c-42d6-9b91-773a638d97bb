{"version": 3, "sources": ["src/app/features/admin/components/workflow-designer/workflow-designer.component.ts", "src/app/features/admin/components/workflow-designer/workflow-designer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\n\n@Component({\n  selector: 'app-workflow-designer',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule\n  ],\n  templateUrl: './workflow-designer.component.html',\n  styleUrls: ['./workflow-designer.component.scss'],\n})\nexport class WorkflowDesignerComponent {}\n", "<div class=\"workflow-designer-container\">\n  <mat-card>\n    <mat-card-header>\n      <mat-card-title>\n        <mat-icon>account_tree</mat-icon>\n        Workflow Designer\n      </mat-card-title>\n      <div class=\"header-actions\">\n        <button mat-raised-button color=\"primary\">\n          <mat-icon>add</mat-icon>\n          New Workflow\n        </button>\n      </div>\n    </mat-card-header>\n\n    <mat-card-content>\n      <div class=\"designer-placeholder\">\n        <div class=\"placeholder-icon\">\n          <mat-icon>account_tree</mat-icon>\n        </div>\n        <h2>Workflow Designer</h2>\n        <p>Design and manage business process workflows with our visual workflow designer.</p>\n\n        <div class=\"feature-list\">\n          <div class=\"feature-item\">\n            <mat-icon>drag_indicator</mat-icon>\n            <span>Drag & Drop Interface</span>\n          </div>\n          <div class=\"feature-item\">\n            <mat-icon>settings</mat-icon>\n            <span>Configurable Steps</span>\n          </div>\n          <div class=\"feature-item\">\n            <mat-icon>people</mat-icon>\n            <span>Role-based Assignments</span>\n          </div>\n          <div class=\"feature-item\">\n            <mat-icon>timeline</mat-icon>\n            <span>Process Automation</span>\n          </div>\n        </div>\n\n        <div class=\"action-buttons\">\n          <button mat-raised-button color=\"primary\">\n            <mat-icon>add</mat-icon>\n            Create New Workflow\n          </button>\n          <button mat-button>\n            <mat-icon>folder_open</mat-icon>\n            Browse Templates\n          </button>\n        </div>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBM,IAAO,4BAAP,MAAO,2BAAyB;;qCAAzB,4BAAyB;EAAA;yEAAzB,4BAAyB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,6BAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,SAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,cAAA,EAAA,CAAA,GAAA,UAAA,SAAA,mCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACpBtC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAyC,GAAA,UAAA,EAC7B,GAAA,iBAAA,EACS,GAAA,gBAAA,EACC,GAAA,UAAA;AACJ,MAAA,iBAAA,GAAA,cAAA;AAAY,MAAA,uBAAA;AACtB,MAAA,iBAAA,GAAA,qBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,UAAA,CAAA,EACgB,GAAA,UAAA;AAC9B,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACb,MAAA,iBAAA,IAAA,gBAAA;AACF,MAAA,uBAAA,EAAS,EACL;AAGR,MAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,OAAA,CAAA,EACkB,IAAA,OAAA,CAAA,EACF,IAAA,UAAA;AAClB,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA,EAAW;AAEnC,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AACrB,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,iFAAA;AAA+E,MAAA,uBAAA;AAElF,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,OAAA,CAAA,EACE,IAAA,UAAA;AACd,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AACxB,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,uBAAA;AAAqB,MAAA,uBAAA,EAAO;AAEpC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,UAAA;AACd,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,uBAAA,EAAO;AAEjC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,UAAA;AACd,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChB,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,wBAAA;AAAsB,MAAA,uBAAA,EAAO;AAErC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,UAAA;AACd,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,uBAAA,EAAO,EAC3B;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA4B,IAAA,UAAA,CAAA,EACgB,IAAA,UAAA;AAC9B,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACb,MAAA,iBAAA,IAAA,uBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA,EAAmB,IAAA,UAAA;AACP,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrB,MAAA,iBAAA,IAAA,oBAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF,EACW,EACV;;;ID3CT;IACA;IACA;IAAa;IAAA;IAAA;IAAA;IACb;IAAe;IACf;IAAa;EAAA,GAAA,QAAA,CAAA,4/IAAA,EAAA,CAAA;;;sEAKJ,2BAAyB,CAAA;UAbrC;uBACW,yBAAuB,YACrB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;OACD,UAAA,8sDAAA,QAAA,CAAA,gvGAAA,EAAA,CAAA;;;;6EAIU,2BAAyB,EAAA,WAAA,6BAAA,UAAA,sFAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}