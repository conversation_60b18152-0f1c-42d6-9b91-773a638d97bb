{"version": 3, "sources": ["src/app/shared/components/icon-test/icon-test.component.ts"], "sourcesContent": ["\n    .icon-test-container {\n      padding: 20px;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .icon-grid {\n      display: flex;\n      flex-direction: column;\n      gap: 30px;\n    }\n\n    .icon-section {\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      padding: 20px;\n      background: #fafafa;\n    }\n\n    .icon-section h3 {\n      margin: 0 0 15px 0;\n      color: #333;\n      font-weight: 500;\n    }\n\n    .icon-row {\n      display: flex;\n      gap: 20px;\n      align-items: center;\n      flex-wrap: wrap;\n    }\n\n    .button-row {\n      display: flex;\n      gap: 15px;\n      align-items: center;\n      flex-wrap: wrap;\n    }\n\n    mat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .icon-18 {\n      font-size: 18px !important;\n      width: 18px !important;\n      height: 18px !important;\n    }\n\n    .icon-24 {\n      font-size: 24px !important;\n      width: 24px !important;\n      height: 24px !important;\n    }\n\n    .icon-36 {\n      font-size: 36px !important;\n      width: 36px !important;\n      height: 36px !important;\n    }\n\n    .icon-48 {\n      font-size: 48px !important;\n      width: 48px !important;\n      height: 48px !important;\n    }\n\n    .success-icon {\n      color: #4caf50;\n    }\n\n    .warning-icon {\n      color: #ff9800;\n    }\n\n    .error-icon {\n      color: #f44336;\n    }\n\n    .info-icon {\n      color: #2196f3;\n    }\n\n    .material-icons {\n      font-size: 24px;\n      color: #666;\n    }\n\n    .mdi {\n      font-size: 24px;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .icon-row,\n      .button-row {\n        gap: 10px;\n      }\n      \n      .icon-test-container {\n        padding: 10px;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA;;AAGF,CAPA,aAOA;AACE,UAAA,EAAA,EAAA,KAAA;AACA,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,eAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,eAAA;AACA,aAAA;;AAGF;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GAvEF;EAuEE,CAhEF;AAkEI,SAAA;;AAGF,GArGF;AAsGI,aAAA;;;", "names": []}