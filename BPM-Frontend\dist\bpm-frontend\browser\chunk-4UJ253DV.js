import {
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-XLTWONBW.js";

// src/app/features/dashboard/dashboard.module.ts
var routes = [
  {
    path: "",
    loadComponent: () => import("./chunk-PVBCULV5.js").then((c) => c.DashboardComponent)
  },
  {
    path: "employee",
    loadComponent: () => import("./chunk-H66YV4TB.js").then((c) => c.EmployeeDashboardComponent)
  },
  {
    path: "manager",
    loadComponent: () => import("./chunk-NGWM6LMX.js").then((c) => c.ManagerDashboardComponent),
    data: { roles: ["Manager", "Admin"] }
  },
  {
    path: "hr",
    loadComponent: () => import("./chunk-AAIUTEBR.js").then((c) => c.HRDashboardComponent),
    data: { roles: ["HR", "Admin"] }
  },
  {
    path: "reports",
    loadComponent: () => import("./chunk-IYUT35HG.js").then((c) => c.ReportingDashboardComponent),
    data: { roles: ["Manager", "HR", "Admin"] }
  },
  {
    path: "reporting",
    loadComponent: () => import("./chunk-IYUT35HG.js").then((c) => c.ReportingDashboardComponent),
    data: { roles: ["Manager", "HR", "Admin"] }
  }
];
var DashboardModule = class _DashboardModule {
  static \u0275fac = function DashboardModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _DashboardModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();
export {
  DashboardModule
};
//# sourceMappingURL=chunk-4UJ253DV.js.map
