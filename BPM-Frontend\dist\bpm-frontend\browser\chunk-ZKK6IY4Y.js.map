{"version": 3, "sources": ["src/app/features/workflows/components/workflow-designer/workflow-designer.component.ts"], "sourcesContent": ["import { Compo<PERSON>, OnIni<PERSON>, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, ActivatedRoute, Router } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport { WorkflowService } from '../../../../core/services/workflow.service';\nimport { WorkflowDto, CreateWorkflowDto, UpdateWorkflowDto, CreateWorkflowStepDto } from '../../../../core/models';\n\n@Component({\n  selector: 'app-workflow-designer',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatDialogModule\n  ],\n  template: `\n    <div class=\"workflow-designer-container\">\n      <!-- Loading Spinner -->\n      <div *ngIf=\"loading\" class=\"loading-container\">\n        <mat-spinner></mat-spinner>\n      </div>\n\n      <!-- Designer Form -->\n      <div *ngIf=\"!loading\">\n        <form [formGroup]=\"workflowForm\" (ngSubmit)=\"onSubmit()\">\n          <!-- Workflow Basic Info -->\n          <mat-card class=\"workflow-info-card\">\n            <mat-card-header>\n              <mat-card-title>\n                <mat-icon>settings</mat-icon>\n                {{isEditMode ? 'Edit Workflow' : 'Create New Workflow'}}\n              </mat-card-title>\n            </mat-card-header>\n\n            <mat-card-content>\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Workflow Name</mat-label>\n                  <input matInput formControlName=\"name\" placeholder=\"Enter workflow name\">\n                  <mat-error *ngIf=\"workflowForm.get('name')?.hasError('required')\">\n                    Workflow name is required\n                  </mat-error>\n                </mat-form-field>\n              </div>\n\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Description</mat-label>\n                  <textarea matInput formControlName=\"description\" rows=\"3\" placeholder=\"Describe this workflow\"></textarea>\n                </mat-form-field>\n              </div>\n\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Version</mat-label>\n                  <input matInput type=\"number\" formControlName=\"version\" min=\"1\">\n                </mat-form-field>\n\n                <mat-checkbox formControlName=\"isActive\">\n                  Active Workflow\n                </mat-checkbox>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Workflow Steps -->\n          <mat-card class=\"workflow-steps-card\">\n            <mat-card-header>\n              <mat-card-title>\n                <mat-icon>timeline</mat-icon>\n                Workflow Steps\n              </mat-card-title>\n              <div class=\"header-actions\">\n                <button type=\"button\" mat-raised-button color=\"accent\" (click)=\"addStep()\">\n                  <mat-icon>add</mat-icon>\n                  Add Step\n                </button>\n              </div>\n            </mat-card-header>\n\n            <mat-card-content>\n              <div formArrayName=\"steps\">\n                <div *ngFor=\"let stepControl of stepsArray.controls; let i = index\" \n                     [formGroupName]=\"i\" \n                     class=\"step-item\">\n                  <div class=\"step-header\">\n                    <h4>Step {{i + 1}}</h4>\n                    <button type=\"button\" mat-icon-button color=\"warn\" (click)=\"removeStep(i)\" \n                            [disabled]=\"stepsArray.length <= 1\">\n                      <mat-icon>delete</mat-icon>\n                    </button>\n                  </div>\n\n                  <div class=\"step-form\">\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Step Name</mat-label>\n                        <input matInput formControlName=\"stepName\" placeholder=\"Enter step name\">\n                        <mat-error *ngIf=\"stepControl.get('stepName')?.hasError('required')\">\n                          Step name is required\n                        </mat-error>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Order</mat-label>\n                        <input matInput type=\"number\" formControlName=\"order\" min=\"1\" [value]=\"i + 1\" readonly>\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Responsible Role</mat-label>\n                        <mat-select formControlName=\"responsibleRole\">\n                          <mat-option value=\"Employee\">Employee</mat-option>\n                          <mat-option value=\"Manager\">Manager</mat-option>\n                          <mat-option value=\"HR\">HR</mat-option>\n                          <mat-option value=\"Admin\">Admin</mat-option>\n                        </mat-select>\n                        <mat-error *ngIf=\"stepControl.get('responsibleRole')?.hasError('required')\">\n                          Responsible role is required\n                        </mat-error>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Due in Hours (Optional)</mat-label>\n                        <input matInput type=\"number\" formControlName=\"dueInHours\" min=\"1\" placeholder=\"24\">\n                      </mat-form-field>\n                    </div>\n                  </div>\n\n                  <mat-divider *ngIf=\"i < stepsArray.length - 1\"></mat-divider>\n                </div>\n\n                <div *ngIf=\"stepsArray.length === 0\" class=\"no-steps\">\n                  <mat-icon>info</mat-icon>\n                  <p>No steps added yet. Click \"Add Step\" to create your first workflow step.</p>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Actions -->\n          <div class=\"actions-section\">\n            <button type=\"button\" mat-button routerLink=\"/workflows\">\n              <mat-icon>cancel</mat-icon>\n              Cancel\n            </button>\n            <button type=\"submit\" mat-raised-button color=\"primary\" [disabled]=\"workflowForm.invalid || isSubmitting\">\n              <mat-icon>save</mat-icon>\n              {{isEditMode ? 'Update Workflow' : 'Create Workflow'}}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .workflow-designer-container {\n      padding: 1rem;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 3rem;\n    }\n\n    .workflow-info-card,\n    .workflow-steps-card {\n      margin-bottom: 1rem;\n    }\n\n    mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .form-row {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n      align-items: flex-start;\n    }\n\n    .full-width {\n      width: 100%;\n    }\n\n    .step-item {\n      margin-bottom: 1.5rem;\n      padding: 1rem;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      background-color: #fafafa;\n    }\n\n    .step-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .step-header h4 {\n      margin: 0;\n      color: #333;\n    }\n\n    .step-form {\n      background-color: white;\n      padding: 1rem;\n      border-radius: 4px;\n    }\n\n    .no-steps {\n      text-align: center;\n      padding: 2rem;\n      color: #666;\n    }\n\n    .no-steps mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n      margin-bottom: 0.5rem;\n      color: #ff9800;\n    }\n\n    .actions-section {\n      display: flex;\n      gap: 1rem;\n      justify-content: flex-end;\n      padding: 1rem;\n      background-color: #f5f5f5;\n      border-radius: 8px;\n      margin-top: 1rem;\n    }\n\n    @media (max-width: 768px) {\n      .form-row {\n        flex-direction: column;\n      }\n\n      .actions-section {\n        flex-direction: column;\n      }\n    }\n  `]\n})\nexport class WorkflowDesignerComponent implements OnInit, OnDestroy {\n  private readonly destroy$ = new Subject<void>();\n  \n  workflowForm!: FormGroup;\n  loading = false;\n  isSubmitting = false;\n  isEditMode = false;\n  workflowId?: string;\n\n  constructor(\n    private readonly fb: FormBuilder,\n    private readonly route: ActivatedRoute,\n    private readonly router: Router,\n    private readonly workflowService: WorkflowService,\n    private readonly snackBar: MatSnackBar\n  ) {\n    this.initForm();\n  }\n\n  ngOnInit(): void {\n    this.workflowId = this.route.snapshot.params['id'];\n    this.isEditMode = !!this.workflowId;\n\n    if (this.isEditMode) {\n      this.loadWorkflow();\n    } else {\n      this.addStep(); // Add initial step for new workflows\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  get stepsArray(): FormArray {\n    return this.workflowForm.get('steps') as FormArray;\n  }\n\n  initForm(): void {\n    this.workflowForm = this.fb.group({\n      name: ['', Validators.required],\n      description: [''],\n      version: [1, [Validators.required, Validators.min(1)]],\n      isActive: [true],\n      steps: this.fb.array([])\n    });\n  }\n\n  createStepFormGroup(step?: any): FormGroup {\n    return this.fb.group({\n      stepName: [step?.stepName || '', Validators.required],\n      order: [step?.order || this.stepsArray.length + 1],\n      responsibleRole: [step?.responsibleRole || '', Validators.required],\n      dueInHours: [step?.dueInHours || null]\n    });\n  }\n\n  addStep(): void {\n    const stepGroup = this.createStepFormGroup();\n    this.stepsArray.push(stepGroup);\n    this.updateStepOrders();\n  }\n\n  removeStep(index: number): void {\n    if (this.stepsArray.length > 1) {\n      this.stepsArray.removeAt(index);\n      this.updateStepOrders();\n    }\n  }\n\n  updateStepOrders(): void {\n    this.stepsArray.controls.forEach((control, index) => {\n      control.get('order')?.setValue(index + 1);\n    });\n  }\n\n  loadWorkflow(): void {\n    if (!this.workflowId) return;\n\n    this.loading = true;\n    this.workflowService.getWorkflowWithSteps(this.workflowId).pipe(\n      takeUntil(this.destroy$)\n    ).subscribe({\n      next: (workflow) => {\n        this.populateForm(workflow);\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading workflow:', error);\n        this.snackBar.open('Error loading workflow', 'Close', { duration: 3000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  populateForm(workflow: WorkflowDto): void {\n    this.workflowForm.patchValue({\n      name: workflow.name,\n      description: workflow.description,\n      version: workflow.version,\n      isActive: workflow.isActive\n    });\n\n    // Clear existing steps\n    while (this.stepsArray.length !== 0) {\n      this.stepsArray.removeAt(0);\n    }\n\n    // Add workflow steps\n    if (workflow.steps && workflow.steps.length > 0) {\n      const sortedSteps = [...workflow.steps].sort((a, b) => a.order - b.order);\n      sortedSteps.forEach(step => {\n        this.stepsArray.push(this.createStepFormGroup(step));\n      });\n    } else {\n      this.addStep(); // Add at least one step\n    }\n  }\n\n  onSubmit(): void {\n    if (this.workflowForm.invalid) {\n      this.markFormGroupTouched(this.workflowForm);\n      return;\n    }\n\n    this.isSubmitting = true;\n    const formValue = this.workflowForm.value;\n\n    const steps: CreateWorkflowStepDto[] = formValue.steps.map((step: any, index: number) => ({\n      stepName: step.stepName,\n      order: index + 1,\n      responsibleRole: step.responsibleRole,\n      dueInHours: step.dueInHours || undefined\n    }));\n\n    if (this.isEditMode && this.workflowId) {\n      const updateData: UpdateWorkflowDto = {\n        name: formValue.name,\n        description: formValue.description,\n        version: formValue.version,\n        isActive: formValue.isActive\n      };\n\n      this.workflowService.updateWorkflow(this.workflowId, updateData).pipe(\n        takeUntil(this.destroy$)\n      ).subscribe({\n        next: () => {\n          this.snackBar.open('Workflow updated successfully!', 'Close', { duration: 3000 });\n          this.router.navigate(['/workflows']);\n        },\n        error: (error) => {\n          console.error('Error updating workflow:', error);\n          this.snackBar.open('Error updating workflow', 'Close', { duration: 3000 });\n          this.isSubmitting = false;\n        }\n      });\n    } else {\n      const createData: CreateWorkflowDto = {\n        name: formValue.name,\n        description: formValue.description,\n        version: formValue.version,\n        isActive: formValue.isActive,\n        steps: steps\n      };\n\n      this.workflowService.createWorkflow(createData).pipe(\n        takeUntil(this.destroy$)\n      ).subscribe({\n        next: () => {\n          this.snackBar.open('Workflow created successfully!', 'Close', { duration: 3000 });\n          this.router.navigate(['/workflows']);\n        },\n        error: (error) => {\n          console.error('Error creating workflow:', error);\n          this.snackBar.open('Error creating workflow', 'Close', { duration: 3000 });\n          this.isSubmitting = false;\n        }\n      });\n    }\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      } else if (control instanceof FormArray) {\n        control.controls.forEach(arrayControl => {\n          if (arrayControl instanceof FormGroup) {\n            this.markFormGroupTouched(arrayControl);\n          }\n        });\n      } else {\n        control?.markAsTouched();\n      }\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CM,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,aAAA;AACF,IAAA,uBAAA;;;;;AAmBY,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA;;;;;AAyDM,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;;;;;AAkBA,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,gCAAA;AACF,IAAA,uBAAA;;;;;AAUN,IAAA,oBAAA,GAAA,aAAA;;;;;;AAhDF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAEuB,GAAA,OAAA,EAAA,EACI,GAAA,IAAA;AACnB,IAAA,iBAAA,CAAA;AAAc,IAAA,uBAAA;AAClB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAmD,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,YAAA,OAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,IAAA,CAAa;IAAA,CAAA;AAEvE,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA,EAAW,EACpB;AAGX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuB,GAAA,OAAA,CAAA,EACC,GAAA,kBAAA,EAAA,EACiB,IAAA,WAAA;AACxB,IAAA,iBAAA,IAAA,WAAA;AAAS,IAAA,uBAAA;AACpB,IAAA,oBAAA,IAAA,SAAA,EAAA;AACA,IAAA,qBAAA,IAAA,8DAAA,GAAA,GAAA,aAAA,CAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,kBAAA,EAAA,EAAqC,IAAA,WAAA;AACxB,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AAChB,IAAA,oBAAA,IAAA,SAAA,EAAA;AACF,IAAA,uBAAA,EAAiB;AAGnB,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,kBAAA,EAAA,EACiB,IAAA,WAAA;AACxB,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,IAAA,cAAA,EAAA,EAA8C,IAAA,cAAA,EAAA;AACf,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA;AACrC,IAAA,yBAAA,IAAA,cAAA,EAAA;AAA4B,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AACnC,IAAA,yBAAA,IAAA,cAAA,EAAA;AAAuB,IAAA,iBAAA,IAAA,IAAA;AAAE,IAAA,uBAAA;AACzB,IAAA,yBAAA,IAAA,cAAA,EAAA;AAA0B,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA,EAAa;AAE9C,IAAA,qBAAA,IAAA,8DAAA,GAAA,GAAA,aAAA,CAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,kBAAA,EAAA,EAAqC,IAAA,WAAA;AACxB,IAAA,iBAAA,IAAA,yBAAA;AAAuB,IAAA,uBAAA;AAClC,IAAA,oBAAA,IAAA,SAAA,EAAA;AACF,IAAA,uBAAA,EAAiB,EACb;AAGR,IAAA,qBAAA,IAAA,gEAAA,GAAA,GAAA,eAAA,CAAA;AACF,IAAA,uBAAA;;;;;;;;AAhDK,IAAA,qBAAA,iBAAA,IAAA;AAGG,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,SAAA,OAAA,GAAA,EAAA;AAEI,IAAA,oBAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,WAAA,UAAA,CAAA;AAUQ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,eAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,SAAA,UAAA,CAAA;AAOkD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,CAAA;AAalD,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,eAAA,IAAA,iBAAA,MAAA,OAAA,OAAA,QAAA,SAAA,UAAA,CAAA;AAYJ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA,WAAA,SAAA,CAAA;;;;;AAGhB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsD,GAAA,UAAA;AAC1C,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;AACd,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,0EAAA;AAAwE,IAAA,uBAAA,EAAI;;;;;;AAhH3F,IAAA,yBAAA,GAAA,KAAA,EAAsB,GAAA,QAAA,CAAA;AACa,IAAA,qBAAA,YAAA,SAAA,oEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAY,OAAA,SAAA,CAAU;IAAA,CAAA;AAErD,IAAA,yBAAA,GAAA,YAAA,CAAA,EAAqC,GAAA,iBAAA,EAClB,GAAA,gBAAA,EACC,GAAA,UAAA;AACJ,IAAA,iBAAA,GAAA,UAAA;AAAQ,IAAA,uBAAA;AAClB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAiB;AAGnB,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA,EACM,IAAA,kBAAA,CAAA,EACoC,IAAA,WAAA;AAC3C,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACxB,IAAA,oBAAA,IAAA,SAAA,CAAA;AACA,IAAA,qBAAA,IAAA,uDAAA,GAAA,GAAA,aAAA,CAAA;AAGF,IAAA,uBAAA,EAAiB;AAGnB,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,kBAAA,CAAA,EACoC,IAAA,WAAA;AAC3C,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AACtB,IAAA,oBAAA,IAAA,YAAA,CAAA;AACF,IAAA,uBAAA,EAAiB;AAGnB,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,kBAAA,EAAA,EACiB,IAAA,WAAA;AACxB,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AAClB,IAAA,oBAAA,IAAA,SAAA,EAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,gBAAA,EAAA;AACE,IAAA,iBAAA,IAAA,mBAAA;AACF,IAAA,uBAAA,EAAe,EACX,EACW;AAIrB,IAAA,yBAAA,IAAA,YAAA,EAAA,EAAsC,IAAA,iBAAA,EACnB,IAAA,gBAAA,EACC,IAAA,UAAA;AACJ,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA;AAClB,IAAA,iBAAA,IAAA,kBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,UAAA,EAAA;AAC6B,IAAA,qBAAA,SAAA,SAAA,oEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,QAAA,CAAS;IAAA,CAAA;AACvE,IAAA,yBAAA,IAAA,UAAA;AAAU,IAAA,iBAAA,IAAA,KAAA;AAAG,IAAA,uBAAA;AACb,IAAA,iBAAA,IAAA,YAAA;AACF,IAAA,uBAAA,EAAS,EACL;AAGR,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,OAAA,EAAA;AAEd,IAAA,qBAAA,IAAA,iDAAA,IAAA,GAAA,OAAA,EAAA,EAEuB,IAAA,iDAAA,GAAA,GAAA,OAAA,EAAA;AAqDzB,IAAA,uBAAA,EAAM,EACW;AAIrB,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,UAAA,EAAA,EAC8B,IAAA,UAAA;AAC7C,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AAChB,IAAA,iBAAA,IAAA,UAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA,EAA0G,IAAA,UAAA;AAC9F,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AACd,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACD;;;;;AAhID,IAAA,oBAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,YAAA;AAME,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,kBAAA,uBAAA,GAAA;AASc,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,OAAA,aAAA,IAAA,MAAA,MAAA,OAAA,OAAA,QAAA,SAAA,UAAA,CAAA;AA2Ce,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,WAAA,QAAA;AAmDvB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,WAAA,WAAA,CAAA;AAc8C,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,aAAA,WAAA,OAAA,YAAA;AAEtD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,oBAAA,mBAAA,GAAA;;;AA8GR,IAAO,4BAAP,MAAO,2BAAyB;EAUjB;EACA;EACA;EACA;EACA;EAbF,WAAW,IAAI,QAAO;EAEvC;EACA,UAAU;EACV,eAAe;EACf,aAAa;EACb;EAEA,YACmB,IACA,OACA,QACA,iBACA,UAAqB;AAJrB,SAAA,KAAA;AACA,SAAA,QAAA;AACA,SAAA,SAAA;AACA,SAAA,kBAAA;AACA,SAAA,WAAA;AAEjB,SAAK,SAAQ;EACf;EAEA,WAAQ;AACN,SAAK,aAAa,KAAK,MAAM,SAAS,OAAO,IAAI;AACjD,SAAK,aAAa,CAAC,CAAC,KAAK;AAEzB,QAAI,KAAK,YAAY;AACnB,WAAK,aAAY;IACnB,OAAO;AACL,WAAK,QAAO;IACd;EACF;EAEA,cAAW;AACT,SAAK,SAAS,KAAI;AAClB,SAAK,SAAS,SAAQ;EACxB;EAEA,IAAI,aAAU;AACZ,WAAO,KAAK,aAAa,IAAI,OAAO;EACtC;EAEA,WAAQ;AACN,SAAK,eAAe,KAAK,GAAG,MAAM;MAChC,MAAM,CAAC,IAAI,WAAW,QAAQ;MAC9B,aAAa,CAAC,EAAE;MAChB,SAAS,CAAC,GAAG,CAAC,WAAW,UAAU,WAAW,IAAI,CAAC,CAAC,CAAC;MACrD,UAAU,CAAC,IAAI;MACf,OAAO,KAAK,GAAG,MAAM,CAAA,CAAE;KACxB;EACH;EAEA,oBAAoB,MAAU;AAC5B,WAAO,KAAK,GAAG,MAAM;MACnB,UAAU,CAAC,MAAM,YAAY,IAAI,WAAW,QAAQ;MACpD,OAAO,CAAC,MAAM,SAAS,KAAK,WAAW,SAAS,CAAC;MACjD,iBAAiB,CAAC,MAAM,mBAAmB,IAAI,WAAW,QAAQ;MAClE,YAAY,CAAC,MAAM,cAAc,IAAI;KACtC;EACH;EAEA,UAAO;AACL,UAAM,YAAY,KAAK,oBAAmB;AAC1C,SAAK,WAAW,KAAK,SAAS;AAC9B,SAAK,iBAAgB;EACvB;EAEA,WAAW,OAAa;AACtB,QAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,WAAK,WAAW,SAAS,KAAK;AAC9B,WAAK,iBAAgB;IACvB;EACF;EAEA,mBAAgB;AACd,SAAK,WAAW,SAAS,QAAQ,CAAC,SAAS,UAAS;AAClD,cAAQ,IAAI,OAAO,GAAG,SAAS,QAAQ,CAAC;IAC1C,CAAC;EACH;EAEA,eAAY;AACV,QAAI,CAAC,KAAK;AAAY;AAEtB,SAAK,UAAU;AACf,SAAK,gBAAgB,qBAAqB,KAAK,UAAU,EAAE,KACzD,UAAU,KAAK,QAAQ,CAAC,EACxB,UAAU;MACV,MAAM,CAAC,aAAY;AACjB,aAAK,aAAa,QAAQ;AAC1B,aAAK,UAAU;MACjB;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,2BAA2B,KAAK;AAC9C,aAAK,SAAS,KAAK,0BAA0B,SAAS,EAAE,UAAU,IAAI,CAAE;AACxE,aAAK,UAAU;MACjB;KACD;EACH;EAEA,aAAa,UAAqB;AAChC,SAAK,aAAa,WAAW;MAC3B,MAAM,SAAS;MACf,aAAa,SAAS;MACtB,SAAS,SAAS;MAClB,UAAU,SAAS;KACpB;AAGD,WAAO,KAAK,WAAW,WAAW,GAAG;AACnC,WAAK,WAAW,SAAS,CAAC;IAC5B;AAGA,QAAI,SAAS,SAAS,SAAS,MAAM,SAAS,GAAG;AAC/C,YAAM,cAAc,CAAC,GAAG,SAAS,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AACxE,kBAAY,QAAQ,UAAO;AACzB,aAAK,WAAW,KAAK,KAAK,oBAAoB,IAAI,CAAC;MACrD,CAAC;IACH,OAAO;AACL,WAAK,QAAO;IACd;EACF;EAEA,WAAQ;AACN,QAAI,KAAK,aAAa,SAAS;AAC7B,WAAK,qBAAqB,KAAK,YAAY;AAC3C;IACF;AAEA,SAAK,eAAe;AACpB,UAAM,YAAY,KAAK,aAAa;AAEpC,UAAM,QAAiC,UAAU,MAAM,IAAI,CAAC,MAAW,WAAmB;MACxF,UAAU,KAAK;MACf,OAAO,QAAQ;MACf,iBAAiB,KAAK;MACtB,YAAY,KAAK,cAAc;MAC/B;AAEF,QAAI,KAAK,cAAc,KAAK,YAAY;AACtC,YAAM,aAAgC;QACpC,MAAM,UAAU;QAChB,aAAa,UAAU;QACvB,SAAS,UAAU;QACnB,UAAU,UAAU;;AAGtB,WAAK,gBAAgB,eAAe,KAAK,YAAY,UAAU,EAAE,KAC/D,UAAU,KAAK,QAAQ,CAAC,EACxB,UAAU;QACV,MAAM,MAAK;AACT,eAAK,SAAS,KAAK,kCAAkC,SAAS,EAAE,UAAU,IAAI,CAAE;AAChF,eAAK,OAAO,SAAS,CAAC,YAAY,CAAC;QACrC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,4BAA4B,KAAK;AAC/C,eAAK,SAAS,KAAK,2BAA2B,SAAS,EAAE,UAAU,IAAI,CAAE;AACzE,eAAK,eAAe;QACtB;OACD;IACH,OAAO;AACL,YAAM,aAAgC;QACpC,MAAM,UAAU;QAChB,aAAa,UAAU;QACvB,SAAS,UAAU;QACnB,UAAU,UAAU;QACpB;;AAGF,WAAK,gBAAgB,eAAe,UAAU,EAAE,KAC9C,UAAU,KAAK,QAAQ,CAAC,EACxB,UAAU;QACV,MAAM,MAAK;AACT,eAAK,SAAS,KAAK,kCAAkC,SAAS,EAAE,UAAU,IAAI,CAAE;AAChF,eAAK,OAAO,SAAS,CAAC,YAAY,CAAC;QACrC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,4BAA4B,KAAK;AAC/C,eAAK,SAAS,KAAK,2BAA2B,SAAS,EAAE,UAAU,IAAI,CAAE;AACzE,eAAK,eAAe;QACtB;OACD;IACH;EACF;EAEQ,qBAAqB,WAAoB;AAC/C,WAAO,KAAK,UAAU,QAAQ,EAAE,QAAQ,SAAM;AAC5C,YAAM,UAAU,UAAU,IAAI,GAAG;AACjC,UAAI,mBAAmB,WAAW;AAChC,aAAK,qBAAqB,OAAO;MACnC,WAAW,mBAAmB,WAAW;AACvC,gBAAQ,SAAS,QAAQ,kBAAe;AACtC,cAAI,wBAAwB,WAAW;AACrC,iBAAK,qBAAqB,YAAY;UACxC;QACF,CAAC;MACH,OAAO;AACL,iBAAS,cAAa;MACxB;IACF,CAAC;EACH;;qCArMW,4BAAyB,4BAAA,WAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAzB,4BAAyB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,6BAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,cAAA,WAAA,GAAA,YAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,QAAA,eAAA,qBAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,eAAA,QAAA,KAAA,eAAA,wBAAA,GAAA,CAAA,cAAA,SAAA,GAAA,CAAA,YAAA,IAAA,QAAA,UAAA,mBAAA,WAAA,OAAA,GAAA,GAAA,CAAA,mBAAA,UAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,UAAA,qBAAA,IAAA,SAAA,UAAA,GAAA,OAAA,GAAA,CAAA,iBAAA,OAAA,GAAA,CAAA,SAAA,aAAA,GAAA,iBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,YAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,UAAA,cAAA,IAAA,cAAA,YAAA,GAAA,CAAA,QAAA,UAAA,qBAAA,IAAA,SAAA,WAAA,GAAA,UAAA,GAAA,CAAA,GAAA,aAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,UAAA,mBAAA,IAAA,SAAA,QAAA,GAAA,SAAA,UAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,YAAA,eAAA,iBAAA,GAAA,CAAA,YAAA,IAAA,QAAA,UAAA,mBAAA,SAAA,OAAA,KAAA,YAAA,IAAA,GAAA,OAAA,GAAA,CAAA,mBAAA,iBAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,IAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,YAAA,IAAA,QAAA,UAAA,mBAAA,cAAA,OAAA,KAAA,eAAA,IAAA,GAAA,CAAA,GAAA,UAAA,CAAA,GAAA,UAAA,SAAA,mCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAnPlC,MAAA,yBAAA,GAAA,OAAA,CAAA;AAEE,MAAA,qBAAA,GAAA,0CAAA,GAAA,GAAA,OAAA,CAAA,EAA+C,GAAA,0CAAA,IAAA,GAAA,OAAA,CAAA;AAwIjD,MAAA,uBAAA;;;AAxIQ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,OAAA;;;IAxBR;IAAY;IAAA;IACZ;IAAY;IACZ;IAAW;IAAA;IAAA;IAAA;IAAA;IAAA;IACX;IAAmB;IAAA;IAAA;IAAA;IACnB;IAAa;IAAA;IAAA;IAAA;IACb;IAAe;IAAA;IACf;IAAa;IACb;IAAkB;IAAA;IAAA;IAClB;IAAc;IACd;IAAe;IAAA;IACf;IAAiB;IACjB;IAAgB;IAChB;IAAwB;IACxB;IACA;EAAe,GAAA,QAAA,CAAA,+4DAAA,EAAA,CAAA;;;sEAsPN,2BAAyB,CAAA;UAxQrC;uBACW,yBAAuB,YACrB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4IT,QAAA,CAAA,2wDAAA,EAAA,CAAA;;;;6EAwGU,2BAAyB,EAAA,WAAA,6BAAA,UAAA,0FAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}