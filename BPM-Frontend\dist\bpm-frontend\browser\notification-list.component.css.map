{"version": 3, "sources": ["src/app/shared/components/notification-list/notification-list.component.scss"], "sourcesContent": [".notification-list-container {\n  max-width: 500px;\n  margin: 20px auto;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  font-family: Arial, sans-serif;\n\n  h2 {\n    text-align: center;\n    color: #333;\n    margin-bottom: 20px;\n  }\n\n  .no-notifications {\n    text-align: center;\n    color: #777;\n    padding: 20px;\n    border: 1px dashed #ccc;\n    border-radius: 5px;\n  }\n\n  .notification-items {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n\n    li {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 15px;\n      margin-bottom: 10px;\n      background-color: #f9f9f9;\n      border: 1px solid #eee;\n      border-radius: 5px;\n      transition: background-color 0.3s ease;\n\n      &.read {\n        background-color: #e9e9e9;\n        color: #999;\n\n        .notification-content p {\n          text-decoration: line-through;\n        }\n      }\n\n      .notification-content {\n        flex-grow: 1;\n\n        p {\n          margin: 0;\n          font-size: 1em;\n          line-height: 1.4;\n        }\n\n        .timestamp {\n          font-size: 0.8em;\n          color: #a0a0a0;\n        }\n      }\n\n      button {\n        background-color: #007bff;\n        color: white;\n        border: none;\n        padding: 8px 12px;\n        border-radius: 4px;\n        cursor: pointer;\n        transition: background-color 0.3s ease;\n\n        &:hover {\n          background-color: #0056b3;\n        }\n      }\n    }\n  }\n\n  .clear-all-button {\n    display: block;\n    width: 100%;\n    padding: 10px;\n    background-color: #dc3545;\n    color: white;\n    border: none;\n    border-radius: 4px;\n    font-size: 1em;\n    cursor: pointer;\n    margin-top: 20px;\n    transition: background-color 0.3s ease;\n\n    &:hover {\n      background-color: #c82333;\n    }\n  }\n}\n"], "mappings": ";AAAA,CAAA;AACE,aAAA;AACA,UAAA,KAAA;AACA,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,eAAA,KAAA,EAAA;;AAEA,CATF,4BASE;AACE,cAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAfF,4BAeE,CAAA;AACE,cAAA;AACA,SAAA;AACA,WAAA;AACA,UAAA,IAAA,OAAA;AACA,iBAAA;;AAGF,CAvBF,4BAuBE,CAAA;AACE,cAAA;AACA,WAAA;AACA,UAAA;;AAEA,CA5BJ,4BA4BI,CALF,mBAKE;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,iBAAA;AACA,oBAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,cAAA,iBAAA,KAAA;;AAEA,CAvCN,4BAuCM,CAhBJ,mBAgBI,EAAA,CAAA;AACE,oBAAA;AACA,SAAA;;AAEA,CA3CR,4BA2CQ,CApBN,mBAoBM,EAAA,CAJF,KAIE,CAAA,qBAAA;AACE,mBAAA;;AAIJ,CAhDN,4BAgDM,CAzBJ,mBAyBI,GAAA,CALE;AAMA,aAAA;;AAEA,CAnDR,4BAmDQ,CA5BN,mBA4BM,GAAA,CARA,qBAQA;AACE,UAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAzDR,4BAyDQ,CAlCN,mBAkCM,GAAA,CAdA,qBAcA,CAAA;AACE,aAAA;AACA,SAAA;;AAIJ,CA/DN,4BA+DM,CAxCJ,mBAwCI,GAAA;AACE,oBAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,UAAA;AACA,cAAA,iBAAA,KAAA;;AAEA,CAxER,4BAwEQ,CAjDN,mBAiDM,GAAA,MAAA;AACE,oBAAA;;AAMR,CA/EF,4BA+EE,CAAA;AACE,WAAA;AACA,SAAA;AACA,WAAA;AACA,oBAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,aAAA;AACA,UAAA;AACA,cAAA;AACA,cAAA,iBAAA,KAAA;;AAEA,CA5FJ,4BA4FI,CAbF,gBAaE;AACE,oBAAA;;", "names": []}