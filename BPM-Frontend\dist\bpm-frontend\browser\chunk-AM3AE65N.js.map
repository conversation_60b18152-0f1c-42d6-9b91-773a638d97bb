{"version": 3, "sources": ["src/app/core/services/request.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { \n  RequestDto, \n  CreateRequestDto, \n  UpdateRequestDto,\n  ApproveRejectStepDto,\n  RequestSummary,\n  PaginatedResponse,\n  PaginationParams,\n  RequestStatus,\n  RequestType\n} from '../models';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RequestService {\n  private readonly API_URL = `${environment.apiUrl}/api/Request`;\n\n  constructor(private http: HttpClient) {}\n\n  getRequests(params?: PaginationParams): Observable<PaginatedResponse<RequestDto>> {\n    let httpParams = new HttpParams();\n    \n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<PaginatedResponse<RequestDto>>(this.API_URL, { params: httpParams });\n  }\n\n  getMyRequests(params?: PaginationParams): Observable<PaginatedResponse<RequestDto>> {\n    let httpParams = new HttpParams();\n    \n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<PaginatedResponse<RequestDto>>(`${this.API_URL}/my-requests`, { params: httpParams });\n  }\n\n  getPendingRequests(params?: PaginationParams): Observable<PaginatedResponse<RequestDto>> {\n    let httpParams = new HttpParams();\n    \n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<PaginatedResponse<RequestDto>>(`${this.API_URL}/pending-approvals`, { params: httpParams });\n  }\n\n  getRequestById(id: string): Observable<RequestDto> {\n    return this.http.get<RequestDto>(`${this.API_URL}/${id}`);\n  }\n\n  createRequest(request: CreateRequestDto): Observable<RequestDto> {\n    return this.http.post<RequestDto>(this.API_URL, request);\n  }\n\n  updateRequest(id: string, request: UpdateRequestDto): Observable<RequestDto> {\n    return this.http.put<RequestDto>(`${this.API_URL}/${id}`, request);\n  }\n\n  deleteRequest(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  // Request Steps\n  getRequestSteps(requestId: string): Observable<any[]> {\n    return this.http.get<any[]>(`${this.API_URL}/${requestId}/steps`);\n  }\n\n  // Request Status Filtering\n  getRequestsByStatus(status: string, params?: PaginationParams): Observable<RequestDto[]> {\n    let httpParams = new HttpParams();\n\n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<RequestDto[]>(`${this.API_URL}/status/${status}`, { params: httpParams });\n  }\n\n  approveStep(requestId: string, stepId: string, data: ApproveRejectStepDto): Observable<void> {\n    return this.http.post<void>(`${this.API_URL}/${requestId}/steps/${stepId}/approve`, data);\n  }\n\n  rejectStep(requestId: string, stepId: string, data: ApproveRejectStepDto): Observable<void> {\n    return this.http.post<void>(`${this.API_URL}/${requestId}/steps/${stepId}/reject`, data);\n  }\n\n  getRequestSummary(): Observable<RequestSummary> {\n    return this.http.get<RequestSummary>(`${this.API_URL}/summary`);\n  }\n\n\n\n  getRequestsByType(type: RequestType, params?: PaginationParams): Observable<PaginatedResponse<RequestDto>> {\n    let httpParams = new HttpParams().set('type', type.toString());\n\n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<PaginatedResponse<RequestDto>>(`${this.API_URL}/by-type`, { params: httpParams });\n  }\n\n  getPendingApprovals(params?: PaginationParams): Observable<PaginatedResponse<RequestDto>> {\n    let httpParams = new HttpParams();\n\n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<PaginatedResponse<RequestDto>>(`${this.API_URL}/pending-approvals`, { params: httpParams });\n  }\n\n  getRequestsForRole(role: string, params?: PaginationParams): Observable<PaginatedResponse<RequestDto>> {\n    let httpParams = new HttpParams().set('role', role);\n\n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<PaginatedResponse<RequestDto>>(`${this.API_URL}/for-role`, { params: httpParams });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAmBM,IAAO,iBAAP,MAAO,gBAAc;EAGL;EAFH,UAAU,GAAG,YAAY,MAAM;EAEhD,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;EAEvC,YAAY,QAAyB;AACnC,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAmC,KAAK,SAAS,EAAE,QAAQ,WAAU,CAAE;EAC1F;EAEA,cAAc,QAAyB;AACrC,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAmC,GAAG,KAAK,OAAO,gBAAgB,EAAE,QAAQ,WAAU,CAAE;EAC3G;EAEA,mBAAmB,QAAyB;AAC1C,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAmC,GAAG,KAAK,OAAO,sBAAsB,EAAE,QAAQ,WAAU,CAAE;EACjH;EAEA,eAAe,IAAU;AACvB,WAAO,KAAK,KAAK,IAAgB,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE;EAC1D;EAEA,cAAc,SAAyB;AACrC,WAAO,KAAK,KAAK,KAAiB,KAAK,SAAS,OAAO;EACzD;EAEA,cAAc,IAAY,SAAyB;AACjD,WAAO,KAAK,KAAK,IAAgB,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI,OAAO;EACnE;EAEA,cAAc,IAAU;AACtB,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE;EACvD;;EAGA,gBAAgB,WAAiB;AAC/B,WAAO,KAAK,KAAK,IAAW,GAAG,KAAK,OAAO,IAAI,SAAS,QAAQ;EAClE;;EAGA,oBAAoB,QAAgB,QAAyB;AAC3D,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAkB,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI,EAAE,QAAQ,WAAU,CAAE;EAC/F;EAEA,YAAY,WAAmB,QAAgB,MAA0B;AACvE,WAAO,KAAK,KAAK,KAAW,GAAG,KAAK,OAAO,IAAI,SAAS,UAAU,MAAM,YAAY,IAAI;EAC1F;EAEA,WAAW,WAAmB,QAAgB,MAA0B;AACtE,WAAO,KAAK,KAAK,KAAW,GAAG,KAAK,OAAO,IAAI,SAAS,UAAU,MAAM,WAAW,IAAI;EACzF;EAEA,oBAAiB;AACf,WAAO,KAAK,KAAK,IAAoB,GAAG,KAAK,OAAO,UAAU;EAChE;EAIA,kBAAkB,MAAmB,QAAyB;AAC5D,QAAI,aAAa,IAAI,WAAU,EAAG,IAAI,QAAQ,KAAK,SAAQ,CAAE;AAE7D,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAmC,GAAG,KAAK,OAAO,YAAY,EAAE,QAAQ,WAAU,CAAE;EACvG;EAEA,oBAAoB,QAAyB;AAC3C,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAmC,GAAG,KAAK,OAAO,sBAAsB,EAAE,QAAQ,WAAU,CAAE;EACjH;EAEA,mBAAmB,MAAc,QAAyB;AACxD,QAAI,aAAa,IAAI,WAAU,EAAG,IAAI,QAAQ,IAAI;AAElD,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAmC,GAAG,KAAK,OAAO,aAAa,EAAE,QAAQ,WAAU,CAAE;EACxG;;qCAzIW,iBAAc,mBAAA,UAAA,CAAA;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;", "names": []}