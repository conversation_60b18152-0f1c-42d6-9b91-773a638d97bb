import {
  RoleGuard
} from "./chunk-MI7J4LQF.js";
import "./chunk-EP3FZZM6.js";
import "./chunk-AKJJBQK4.js";
import {
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-XLTWONBW.js";

// src/app/features/admin/admin-routing.module.ts
var routes = [
  {
    path: "",
    canActivate: [RoleGuard],
    data: { roles: ["Admin"] },
    loadComponent: () => import("./chunk-XPSIJLSR.js").then((c) => c.AdminLayoutComponent),
    children: [
      {
        path: "",
        redirectTo: "users",
        pathMatch: "full"
      },
      {
        path: "users",
        loadComponent: () => import("./chunk-Y747MAQ7.js").then((c) => c.UserManagementComponent)
      },
      {
        path: "workflow-designer",
        loadComponent: () => import("./chunk-BHPM5ISZ.js").then((c) => c.WorkflowDesignerComponent)
      },
      {
        path: "roles",
        loadComponent: () => import("./chunk-6ECMYQ5A.js").then((c) => c.RoleManagementComponent)
      },
      {
        path: "settings",
        loadComponent: () => import("./chunk-S4IYX4OV.js").then((c) => c.SystemSettingsComponent)
      },
      {
        path: "reports",
        loadComponent: () => import("./chunk-MKS7ZL5M.js").then((c) => c.AdminReportsComponent)
      },
      {
        path: "**",
        loadComponent: () => import("./chunk-EMOJM7RY.js").then((c) => c.AdminNotFoundComponent)
      }
    ]
  }
];
var AdminRoutingModule = class _AdminRoutingModule {
  static \u0275fac = function AdminRoutingModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminRoutingModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _AdminRoutingModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminRoutingModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();

// src/app/features/admin/admin.module.ts
var AdminModule = class _AdminModule {
  static \u0275fac = function AdminModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _AdminModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [
    CommonModule,
    RouterModule,
    AdminRoutingModule
  ] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [
        CommonModule,
        RouterModule,
        AdminRoutingModule
      ]
    }]
  }], null, null);
})();
export {
  AdminModule
};
//# sourceMappingURL=chunk-DDY7WGSS.js.map
