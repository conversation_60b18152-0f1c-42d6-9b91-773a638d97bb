import {
  UserService
} from "./chunk-3JNBYCCA.js";
import {
  MatPaginator,
  MatPaginatorModule
} from "./chunk-HF3CCJRO.js";
import {
  MatDialog,
  MatDialogModule
} from "./chunk-4WLZHDJZ.js";
import {
  MatOption,
  MatSelect,
  MatSelectModule
} from "./chunk-2ZKWC7Y6.js";
import "./chunk-H3UX3NVF.js";
import {
  MatTooltip,
  MatTooltipModule
} from "./chunk-F4VZNS7P.js";
import "./chunk-THNYDRFY.js";
import {
  MatFormFieldModule,
  MatInput,
  MatInputModule
} from "./chunk-Z5T6RGZC.js";
import {
  MatSnackBar,
  MatSnackBarModule
} from "./chunk-4HGSRZQD.js";
import "./chunk-DQTTXTF2.js";
import {
  MatChip,
  MatChipsModule
} from "./chunk-KJM35DAP.js";
import {
  MatForm<PERSON>ield,
  Mat<PERSON><PERSON>l,
  MatSuffix
} from "./chunk-PJGOPMTU.js";
import {
  DefaultV<PERSON>ueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel,
  ReactiveFormsModule
} from "./chunk-4WCUDP7B.js";
import {
  MatProgressSpinner,
  MatProgressSpinnerModule
} from "./chunk-PIT7R6CL.js";
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
  MatTableModule
} from "./chunk-Z6DK6RU5.js";
import "./chunk-I7LZP7WV.js";
import "./chunk-FTI6XIW5.js";
import "./chunk-R6R3KTSK.js";
import {
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle
} from "./chunk-QRMDFVVO.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-FSO3A3K2.js";
import {
  MatButton,
  MatButtonModule,
  MatIconButton
} from "./chunk-SVX3GQPM.js";
import {
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  Subject,
  debounceTime,
  distinctUntilChanged,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-XLTWONBW.js";

// src/app/features/admin/components/user-management/user-management.component.ts
var _c0 = () => [5, 10, 25, 50];
function UserManagementComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 16);
    \u0275\u0275element(1, "mat-spinner");
    \u0275\u0275elementEnd();
  }
}
function UserManagementComponent_div_35_th_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "th", 30);
  }
}
function UserManagementComponent_div_35_td_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 31)(1, "div", 32);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const user_r1 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r1.getInitials(user_r1.firstName, user_r1.lastName), " ");
  }
}
function UserManagementComponent_div_35_th_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 30);
    \u0275\u0275text(1, "Name");
    \u0275\u0275elementEnd();
  }
}
function UserManagementComponent_div_35_td_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 31)(1, "div", 33)(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "small");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const user_r3 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r1.getFullName(user_r3));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.getUserName(user_r3));
  }
}
function UserManagementComponent_div_35_th_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 30);
    \u0275\u0275text(1, "Email");
    \u0275\u0275elementEnd();
  }
}
function UserManagementComponent_div_35_td_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 31);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const user_r4 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r1.getUserEmail(user_r4));
  }
}
function UserManagementComponent_div_35_th_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 30);
    \u0275\u0275text(1, "Phone");
    \u0275\u0275elementEnd();
  }
}
function UserManagementComponent_div_35_td_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 31);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const user_r5 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r1.getUserPhone(user_r5) || "N/A");
  }
}
function UserManagementComponent_div_35_th_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 30);
    \u0275\u0275text(1, "Roles");
    \u0275\u0275elementEnd();
  }
}
function UserManagementComponent_div_35_td_16_mat_chip_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-chip");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const role_r6 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(3);
    \u0275\u0275classMap(ctx_r1.getRoleClass(role_r6));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", role_r6, " ");
  }
}
function UserManagementComponent_div_35_td_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 31)(1, "div", 34);
    \u0275\u0275template(2, UserManagementComponent_div_35_td_16_mat_chip_2_Template, 2, 3, "mat-chip", 35);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const user_r7 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.getUserRoles(user_r7));
  }
}
function UserManagementComponent_div_35_th_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 30);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function UserManagementComponent_div_35_td_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "td", 31)(1, "div", 36)(2, "button", 37);
    \u0275\u0275listener("click", function UserManagementComponent_div_35_td_19_Template_button_click_2_listener() {
      const user_r9 = \u0275\u0275restoreView(_r8).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.editUser(user_r9));
    });
    \u0275\u0275elementStart(3, "mat-icon");
    \u0275\u0275text(4, "edit");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "button", 38);
    \u0275\u0275listener("click", function UserManagementComponent_div_35_td_19_Template_button_click_5_listener() {
      const user_r9 = \u0275\u0275restoreView(_r8).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.manageRoles(user_r9));
    });
    \u0275\u0275elementStart(6, "mat-icon");
    \u0275\u0275text(7, "admin_panel_settings");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "button", 39);
    \u0275\u0275listener("click", function UserManagementComponent_div_35_td_19_Template_button_click_8_listener() {
      const user_r9 = \u0275\u0275restoreView(_r8).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.deleteUser(user_r9));
    });
    \u0275\u0275elementStart(9, "mat-icon");
    \u0275\u0275text(10, "delete");
    \u0275\u0275elementEnd()()()();
  }
}
function UserManagementComponent_div_35_tr_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 40);
  }
}
function UserManagementComponent_div_35_tr_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 41);
  }
}
function UserManagementComponent_div_35_div_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 42)(1, "mat-icon");
    \u0275\u0275text(2, "people_outline");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h3");
    \u0275\u0275text(4, "No users found");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "No users match your current search criteria.");
    \u0275\u0275elementEnd()();
  }
}
function UserManagementComponent_div_35_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17)(1, "table", 18);
    \u0275\u0275elementContainerStart(2, 19);
    \u0275\u0275template(3, UserManagementComponent_div_35_th_3_Template, 1, 0, "th", 20)(4, UserManagementComponent_div_35_td_4_Template, 3, 1, "td", 21);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(5, 22);
    \u0275\u0275template(6, UserManagementComponent_div_35_th_6_Template, 2, 0, "th", 20)(7, UserManagementComponent_div_35_td_7_Template, 6, 2, "td", 21);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(8, 23);
    \u0275\u0275template(9, UserManagementComponent_div_35_th_9_Template, 2, 0, "th", 20)(10, UserManagementComponent_div_35_td_10_Template, 2, 1, "td", 21);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(11, 24);
    \u0275\u0275template(12, UserManagementComponent_div_35_th_12_Template, 2, 0, "th", 20)(13, UserManagementComponent_div_35_td_13_Template, 2, 1, "td", 21);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(14, 25);
    \u0275\u0275template(15, UserManagementComponent_div_35_th_15_Template, 2, 0, "th", 20)(16, UserManagementComponent_div_35_td_16_Template, 3, 1, "td", 21);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(17, 26);
    \u0275\u0275template(18, UserManagementComponent_div_35_th_18_Template, 2, 0, "th", 20)(19, UserManagementComponent_div_35_td_19_Template, 11, 0, "td", 21);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(20, UserManagementComponent_div_35_tr_20_Template, 1, 0, "tr", 27)(21, UserManagementComponent_div_35_tr_21_Template, 1, 0, "tr", 28);
    \u0275\u0275elementEnd();
    \u0275\u0275template(22, UserManagementComponent_div_35_div_22_Template, 7, 0, "div", 29);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("dataSource", ctx_r1.users);
    \u0275\u0275advance(19);
    \u0275\u0275property("matHeaderRowDef", ctx_r1.displayedColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r1.displayedColumns);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.users.length === 0);
  }
}
function UserManagementComponent_mat_paginator_36_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-paginator", 43);
    \u0275\u0275listener("page", function UserManagementComponent_mat_paginator_36_Template_mat_paginator_page_0_listener($event) {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPageChange($event));
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275property("length", ctx_r1.totalCount)("pageSize", ctx_r1.pageSize)("pageSizeOptions", \u0275\u0275pureFunction0(4, _c0))("pageIndex", ctx_r1.currentPage - 1);
  }
}
var UserManagementComponent = class _UserManagementComponent {
  dialog;
  snackBar;
  userService;
  destroy$ = new Subject();
  searchSubject = new Subject();
  users = [];
  displayedColumns = ["avatar", "name", "email", "phone", "roles", "actions"];
  loading = false;
  // Pagination
  totalCount = 0;
  currentPage = 1;
  pageSize = 10;
  // Filters
  searchTerm = "";
  selectedRole = "";
  constructor(dialog, snackBar, userService) {
    this.dialog = dialog;
    this.snackBar = snackBar;
    this.userService = userService;
    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(() => {
      this.currentPage = 1;
      this.loadUsers();
    });
  }
  ngOnInit() {
    this.loadUsers();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadUsers() {
    this.loading = true;
    const params = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm || void 0,
      sortBy: "userName",
      // Default sort by username
      sortDirection: "asc"
    };
    if (this.selectedRole) {
      this.userService.getUsersByRole(this.selectedRole, params).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response) => {
          this.users = response.data;
          this.totalCount = response.totalCount;
          this.loading = false;
        },
        error: (error) => {
          console.error("Error loading users by role:", error);
          this.handleLoadError();
        }
      });
    } else {
      this.userService.getUsers(params).pipe(takeUntil(this.destroy$)).subscribe({
        next: (users) => {
          this.users = users;
          this.totalCount = users.length;
          this.loading = false;
        },
        error: (error) => {
          console.error("Error loading users:", error);
          this.handleLoadError();
        }
      });
    }
  }
  handleLoadError() {
    this.snackBar.open("Error loading users. Please try again.", "Close", {
      duration: 5e3,
      panelClass: ["error-snackbar"]
    });
    this.users = this.getMockUsers();
    this.totalCount = this.users.length;
    this.loading = false;
  }
  getMockUsers() {
    return [
      {
        Id: "1",
        UserName: "john.doe",
        Email: "<EMAIL>",
        FirstName: "John",
        LastName: "Doe",
        PhoneNumber: "******-0123",
        Roles: ["Employee"]
      },
      {
        Id: "2",
        UserName: "jane.smith",
        Email: "<EMAIL>",
        FirstName: "Jane",
        LastName: "Smith",
        PhoneNumber: "******-0124",
        Roles: ["Manager"]
      },
      {
        Id: "3",
        UserName: "admin",
        Email: "<EMAIL>",
        FirstName: "System",
        LastName: "Administrator",
        PhoneNumber: "******-0100",
        Roles: ["Admin"]
      }
    ];
  }
  onSearchChange() {
    this.searchSubject.next(this.searchTerm);
  }
  onFilterChange() {
    this.currentPage = 1;
    this.loadUsers();
  }
  onPageChange(event) {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadUsers();
  }
  getInitials(firstName, lastName) {
    const first = firstName?.charAt(0) || "";
    const last = lastName?.charAt(0) || "";
    return (first + last).toUpperCase() || "?";
  }
  getFullName(user) {
    const firstName = user.FirstName || user.firstName || "";
    const lastName = user.LastName || user.lastName || "";
    const userName = user.UserName || user.userName || "";
    return `${firstName} ${lastName}`.trim() || userName;
  }
  // Helper methods to handle both property naming conventions
  getUserName(user) {
    return user.UserName || user.userName || "";
  }
  getUserEmail(user) {
    return user.Email || user.email || "";
  }
  getUserPhone(user) {
    return user.PhoneNumber || user.phoneNumber || "";
  }
  getUserRoles(user) {
    return user.Roles || user.roles || [];
  }
  getUserId(user) {
    return user.Id || user.id || "";
  }
  getRoleClass(role) {
    return `role-${role.toLowerCase()}`;
  }
  openCreateUserDialog() {
    this.snackBar.open("Create user dialog would open here", "Close", { duration: 3e3 });
  }
  editUser(user) {
    this.snackBar.open(`Edit user: ${this.getUserName(user)}`, "Close", { duration: 3e3 });
  }
  manageRoles(user) {
    this.snackBar.open(`Manage roles for: ${this.getUserName(user)}`, "Close", { duration: 3e3 });
  }
  deleteUser(user) {
    const userName = this.getUserName(user);
    const userId = this.getUserId(user);
    const confirmed = confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`);
    if (confirmed) {
      this.userService.deleteUser(userId).pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          this.snackBar.open(`User "${userName}" deleted successfully`, "Close", {
            duration: 3e3,
            panelClass: ["success-snackbar"]
          });
          this.loadUsers();
        },
        error: (error) => {
          console.error("Error deleting user:", error);
          this.snackBar.open(`Error deleting user "${userName}". Please try again.`, "Close", {
            duration: 5e3,
            panelClass: ["error-snackbar"]
          });
        }
      });
    }
  }
  static \u0275fac = function UserManagementComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UserManagementComponent)(\u0275\u0275directiveInject(MatDialog), \u0275\u0275directiveInject(MatSnackBar), \u0275\u0275directiveInject(UserService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _UserManagementComponent, selectors: [["app-user-management"]], decls: 37, vars: 5, consts: [[1, "user-management-container"], [1, "header-actions"], ["mat-raised-button", "", "color", "primary", 3, "click"], [1, "filters"], ["appearance", "outline"], ["matInput", "", "placeholder", "Search users...", 3, "ngModelChange", "ngModel"], ["matSuffix", ""], [3, "ngModelChange", "selectionChange", "ngModel"], ["value", ""], ["value", "Employee"], ["value", "Manager"], ["value", "HR"], ["value", "Admin"], ["class", "loading-container", 4, "ngIf"], ["class", "table-container", 4, "ngIf"], ["showFirstLastButtons", "", 3, "length", "pageSize", "pageSizeOptions", "pageIndex", "page", 4, "ngIf"], [1, "loading-container"], [1, "table-container"], ["mat-table", "", 1, "users-table", 3, "dataSource"], ["matColumnDef", "avatar"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "name"], ["matColumnDef", "email"], ["matColumnDef", "phone"], ["matColumnDef", "roles"], ["matColumnDef", "actions"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", "class", "user-row", 4, "matRowDef", "matRowDefColumns"], ["class", "no-data", 4, "ngIf"], ["mat-header-cell", ""], ["mat-cell", ""], [1, "user-avatar"], [1, "user-info"], [1, "roles-container"], [3, "class", 4, "ngFor", "ngForOf"], [1, "action-buttons"], ["mat-icon-button", "", "matTooltip", "Edit User", 3, "click"], ["mat-icon-button", "", "matTooltip", "Manage Roles", 3, "click"], ["mat-icon-button", "", "color", "warn", "matTooltip", "Delete User", 3, "click"], ["mat-header-row", ""], ["mat-row", "", 1, "user-row"], [1, "no-data"], ["showFirstLastButtons", "", 3, "page", "length", "pageSize", "pageSizeOptions", "pageIndex"]], template: function UserManagementComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title")(4, "mat-icon");
      \u0275\u0275text(5, "people");
      \u0275\u0275elementEnd();
      \u0275\u0275text(6, " User Management ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "div", 1)(8, "button", 2);
      \u0275\u0275listener("click", function UserManagementComponent_Template_button_click_8_listener() {
        return ctx.openCreateUserDialog();
      });
      \u0275\u0275elementStart(9, "mat-icon");
      \u0275\u0275text(10, "person_add");
      \u0275\u0275elementEnd();
      \u0275\u0275text(11, " Add User ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(12, "mat-card-content")(13, "div", 3)(14, "mat-form-field", 4)(15, "mat-label");
      \u0275\u0275text(16, "Search");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "input", 5);
      \u0275\u0275twoWayListener("ngModelChange", function UserManagementComponent_Template_input_ngModelChange_17_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);
        return $event;
      });
      \u0275\u0275listener("ngModelChange", function UserManagementComponent_Template_input_ngModelChange_17_listener() {
        return ctx.onSearchChange();
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "mat-icon", 6);
      \u0275\u0275text(19, "search");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(20, "mat-form-field", 4)(21, "mat-label");
      \u0275\u0275text(22, "Role");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "mat-select", 7);
      \u0275\u0275twoWayListener("ngModelChange", function UserManagementComponent_Template_mat_select_ngModelChange_23_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedRole, $event) || (ctx.selectedRole = $event);
        return $event;
      });
      \u0275\u0275listener("selectionChange", function UserManagementComponent_Template_mat_select_selectionChange_23_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(24, "mat-option", 8);
      \u0275\u0275text(25, "All Roles");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "mat-option", 9);
      \u0275\u0275text(27, "Employee");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "mat-option", 10);
      \u0275\u0275text(29, "Manager");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "mat-option", 11);
      \u0275\u0275text(31, "HR");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "mat-option", 12);
      \u0275\u0275text(33, "Admin");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(34, UserManagementComponent_div_34_Template, 2, 0, "div", 13)(35, UserManagementComponent_div_35_Template, 23, 4, "div", 14)(36, UserManagementComponent_mat_paginator_36_Template, 1, 5, "mat-paginator", 15);
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(17);
      \u0275\u0275twoWayProperty("ngModel", ctx.searchTerm);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedRole);
      \u0275\u0275advance(11);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.totalCount > 0);
    }
  }, dependencies: [
    CommonModule,
    NgForOf,
    NgIf,
    RouterModule,
    FormsModule,
    DefaultValueAccessor,
    NgControlStatus,
    NgModel,
    ReactiveFormsModule,
    MatTableModule,
    MatTable,
    MatHeaderCellDef,
    MatHeaderRowDef,
    MatColumnDef,
    MatCellDef,
    MatRowDef,
    MatHeaderCell,
    MatCell,
    MatHeaderRow,
    MatRow,
    MatButtonModule,
    MatButton,
    MatIconButton,
    MatIconModule,
    MatIcon,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatChipsModule,
    MatChip,
    MatPaginatorModule,
    MatPaginator,
    MatFormFieldModule,
    MatFormField,
    MatLabel,
    MatSuffix,
    MatInputModule,
    MatInput,
    MatSelectModule,
    MatSelect,
    MatOption,
    MatProgressSpinnerModule,
    MatProgressSpinner,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatTooltip
  ], styles: ["\n\n.user-management-container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\nmat-card-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.filters[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  flex-wrap: wrap;\n}\n.filters[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  min-width: 200px;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.table-container[_ngcontent-%COMP%] {\n  overflow-x: auto;\n}\n.users-table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.user-row[_ngcontent-%COMP%]:hover {\n  background-color: #f5f5f5;\n}\n.user-avatar[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: #2196f3;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 0.9rem;\n}\n.user-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.user-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  font-size: 0.95rem;\n}\n.user-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.8rem;\n}\n.roles-container[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.25rem;\n  flex-wrap: wrap;\n}\n.role-employee[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n.role-manager[_ngcontent-%COMP%] {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n}\n.role-hr[_ngcontent-%COMP%] {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.role-admin[_ngcontent-%COMP%] {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.25rem;\n}\n.no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #ccc;\n}\n@media (max-width: 768px) {\n  .filters[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .filters[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n    min-width: 100%;\n  }\n  .action-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=user-management.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UserManagementComponent, [{
    type: Component,
    args: [{ selector: "app-user-management", standalone: true, imports: [
      CommonModule,
      RouterModule,
      FormsModule,
      ReactiveFormsModule,
      MatTableModule,
      MatButtonModule,
      MatIconModule,
      MatCardModule,
      MatChipsModule,
      MatPaginatorModule,
      MatFormFieldModule,
      MatInputModule,
      MatSelectModule,
      MatProgressSpinnerModule,
      MatDialogModule,
      MatSnackBarModule,
      MatTooltipModule
    ], template: `
    <div class="user-management-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>people</mat-icon>
            User Management
          </mat-card-title>
          <div class="header-actions">
            <button mat-raised-button color="primary" (click)="openCreateUserDialog()">
              <mat-icon>person_add</mat-icon>
              Add User
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <!-- Filters -->
          <div class="filters">
            <mat-form-field appearance="outline">
              <mat-label>Search</mat-label>
              <input matInput [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()" placeholder="Search users...">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Role</mat-label>
              <mat-select [(ngModel)]="selectedRole" (selectionChange)="onFilterChange()">
                <mat-option value="">All Roles</mat-option>
                <mat-option value="Employee">Employee</mat-option>
                <mat-option value="Manager">Manager</mat-option>
                <mat-option value="HR">HR</mat-option>
                <mat-option value="Admin">Admin</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Loading Spinner -->
          <div *ngIf="loading" class="loading-container">
            <mat-spinner></mat-spinner>
          </div>

          <!-- Users Table -->
          <div *ngIf="!loading" class="table-container">
            <table mat-table [dataSource]="users" class="users-table">
              <!-- Avatar Column -->
              <ng-container matColumnDef="avatar">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let user">
                  <div class="user-avatar">
                    {{getInitials(user.firstName, user.lastName)}}
                  </div>
                </td>
              </ng-container>

              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let user">
                  <div class="user-info">
                    <strong>{{getFullName(user)}}</strong>
                    <small>{{getUserName(user)}}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Email Column -->
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let user">{{getUserEmail(user)}}</td>
              </ng-container>

              <!-- Phone Column -->
              <ng-container matColumnDef="phone">
                <th mat-header-cell *matHeaderCellDef>Phone</th>
                <td mat-cell *matCellDef="let user">{{getUserPhone(user) || 'N/A'}}</td>
              </ng-container>

              <!-- Roles Column -->
              <ng-container matColumnDef="roles">
                <th mat-header-cell *matHeaderCellDef>Roles</th>
                <td mat-cell *matCellDef="let user">
                  <div class="roles-container">
                    <mat-chip *ngFor="let role of getUserRoles(user)" [class]="getRoleClass(role)">
                      {{role}}
                    </mat-chip>
                  </div>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let user">
                  <div class="action-buttons">
                    <button mat-icon-button (click)="editUser(user)" matTooltip="Edit User">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button (click)="manageRoles(user)" matTooltip="Manage Roles">
                      <mat-icon>admin_panel_settings</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="deleteUser(user)" matTooltip="Delete User">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="user-row"></tr>
            </table>

            <!-- No Data Message -->
            <div *ngIf="users.length === 0" class="no-data">
              <mat-icon>people_outline</mat-icon>
              <h3>No users found</h3>
              <p>No users match your current search criteria.</p>
            </div>
          </div>

          <!-- Pagination -->
          <mat-paginator
            *ngIf="!loading && totalCount > 0"
            [length]="totalCount"
            [pageSize]="pageSize"
            [pageSizeOptions]="[5, 10, 25, 50]"
            [pageIndex]="currentPage - 1"
            (page)="onPageChange($event)"
            showFirstLastButtons>
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;b2997d95a7a693057f9da8de21302e2c3b0ea59259b495a29673880452cf75e1;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/admin/components/user-management/user-management.component.ts */\n.user-management-container {\n  padding: 1rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\nmat-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.filters {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  flex-wrap: wrap;\n}\n.filters mat-form-field {\n  min-width: 200px;\n}\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.table-container {\n  overflow-x: auto;\n}\n.users-table {\n  width: 100%;\n}\n.user-row:hover {\n  background-color: #f5f5f5;\n}\n.user-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: #2196f3;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 0.9rem;\n}\n.user-info {\n  display: flex;\n  flex-direction: column;\n}\n.user-info strong {\n  font-size: 0.95rem;\n}\n.user-info small {\n  color: #666;\n  font-size: 0.8rem;\n}\n.roles-container {\n  display: flex;\n  gap: 0.25rem;\n  flex-wrap: wrap;\n}\n.role-employee {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n.role-manager {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n}\n.role-hr {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.role-admin {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.action-buttons {\n  display: flex;\n  gap: 0.25rem;\n}\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data mat-icon {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #ccc;\n}\n@media (max-width: 768px) {\n  .filters {\n    flex-direction: column;\n  }\n  .filters mat-form-field {\n    min-width: 100%;\n  }\n  .action-buttons {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=user-management.component.css.map */\n"] }]
  }], () => [{ type: MatDialog }, { type: MatSnackBar }, { type: UserService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(UserManagementComponent, { className: "UserManagementComponent", filePath: "src/app/features/admin/components/user-management/user-management.component.ts", lineNumber: 315 });
})();
export {
  UserManagementComponent
};
//# sourceMappingURL=chunk-Y747MAQ7.js.map
