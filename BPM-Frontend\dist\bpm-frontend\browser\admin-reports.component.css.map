{"version": 3, "sources": ["src/app/features/admin/components/admin-reports/admin-reports.component.ts"], "sourcesContent": ["\n    .admin-reports-container {\n      padding: 1rem;\n      max-width: 1400px;\n      margin: 0 auto;\n    }\n\n    .filters-card {\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .filters-row {\n      display: flex;\n      gap: 1rem;\n      align-items: flex-end;\n      flex-wrap: wrap;\n    }\n\n    .date-range {\n      display: flex;\n      gap: 1rem;\n    }\n\n    .overview-cards {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .metric-card {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n    }\n\n    .metric {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .metric-icon {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: rgba(255, 255, 255, 0.2);\n    }\n\n    .metric-icon mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n    }\n\n    .metric-info h3 {\n      margin: 0;\n      font-size: 2rem;\n      font-weight: bold;\n    }\n\n    .metric-info p {\n      margin: 0.25rem 0;\n      font-size: 1rem;\n    }\n\n    .metric-info small {\n      font-size: 0.8rem;\n      opacity: 0.8;\n    }\n\n    .report-details {\n      margin-top: 1rem;\n    }\n\n    .report-details mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .export-actions {\n      display: flex;\n      gap: 0.5rem;\n    }\n\n    .report-content {\n      padding: 1rem 0;\n    }\n\n    .summary-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 2rem;\n    }\n\n    .summary-item h4 {\n      margin-bottom: 1rem;\n      color: #333;\n    }\n\n    .status-breakdown,\n    .health-indicators {\n      background-color: #f8f9fa;\n      padding: 1rem;\n      border-radius: 8px;\n    }\n\n    .status-item,\n    .health-item {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 0.5rem;\n    }\n\n    .status-label,\n    .health-label {\n      font-weight: 500;\n    }\n\n    .health-status {\n      padding: 2px 8px;\n      border-radius: 4px;\n      font-size: 0.8rem;\n      font-weight: bold;\n    }\n\n    .health-status.healthy {\n      background-color: #d4edda;\n      color: #155724;\n    }\n\n    .health-status.warning {\n      background-color: #fff3cd;\n      color: #856404;\n    }\n\n    @media (max-width: 768px) {\n      .filters-row {\n        flex-direction: column;\n        align-items: stretch;\n      }\n\n      .date-range {\n        flex-direction: column;\n      }\n\n      .overview-cards {\n        grid-template-columns: 1fr;\n      }\n\n      .export-actions {\n        flex-direction: column;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,eAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CAVA,YAUA;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA,YAAA;AACE,UAAA;AACA,aAAA;AACA,eAAA;;AAGF,CANA,YAMA;AACE,UAAA,QAAA;AACA,aAAA;;AAGF,CAXA,YAWA;AACE,aAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAJA,eAIA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA,KAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAGF,CAAA,aAAA;AACE,iBAAA;AACA,SAAA;;AAGF,CAAA;AAAA,CAAA;AAEE,oBAAA;AACA,WAAA;AACA,iBAAA;;AAGF,CAAA;AAAA,CAAA;AAEE,WAAA;AACA,mBAAA;AACA,iBAAA;;AAGF,CAAA;AAAA,CAAA;AAEE,eAAA;;AAGF,CAAA;AACE,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAPA,aAOA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAZA,aAYA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GAjIF;AAkII,oBAAA;AACA,iBAAA;;AAGF,GA/HF;AAgII,oBAAA;;AAGF,GA9HF;AA+HI,2BAAA;;AAGF,GAtEF;AAuEI,oBAAA;;;", "names": []}