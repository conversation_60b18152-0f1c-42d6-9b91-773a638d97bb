{"version": 3, "sources": ["src/app/features/auth/components/login/login.component.scss"], "sourcesContent": [".login-card {\n  width: 100%;\n  border: none;\n  box-shadow: none;\n  background: transparent;\n  \n  mat-card-header {\n    text-align: center;\n    margin-bottom: 1.5rem;\n    \n    mat-card-title {\n      font-size: 1.5rem;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 0.5rem;\n    }\n    \n    mat-card-subtitle {\n      color: #666;\n      font-size: 0.9rem;\n    }\n  }\n}\n\n.login-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  \n  .full-width {\n    width: 100%;\n  }\n  \n  mat-form-field {\n    .mat-mdc-form-field-subscript-wrapper {\n      margin-top: 0.5rem;\n    }\n  }\n}\n\n.form-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0.5rem 0 1.5rem 0;\n  \n  .forgot-password {\n    color: #667eea;\n    text-decoration: none;\n    font-size: 0.875rem;\n    transition: color 0.3s ease;\n    \n    &:hover {\n      color: #764ba2;\n      text-decoration: underline;\n    }\n  }\n}\n\n.login-button {\n  height: 48px;\n  font-size: 1rem;\n  font-weight: 500;\n  border-radius: 8px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  transition: all 0.3s ease;\n  \n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n  }\n  \n  &:disabled {\n    opacity: 0.7;\n    cursor: not-allowed;\n  }\n  \n  mat-spinner {\n    margin-right: 8px;\n  }\n}\n\n.card-actions {\n  padding: 1rem 0 0 0;\n  justify-content: center;\n  \n  .signup-text {\n    text-align: center;\n    color: #666;\n    font-size: 0.9rem;\n    margin: 0;\n    \n    .signup-link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n      transition: color 0.3s ease;\n      \n      &:hover {\n        color: #764ba2;\n        text-decoration: underline;\n      }\n    }\n  }\n}\n\n// Custom snackbar styles\n:host ::ng-deep {\n  .success-snackbar {\n    background-color: #4caf50;\n    color: white;\n  }\n  \n  .error-snackbar {\n    background-color: #f44336;\n    color: white;\n  }\n}\n\n// Responsive Design\n@media (max-width: 480px) {\n  .form-options {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n    \n    .forgot-password {\n      align-self: flex-end;\n    }\n  }\n  \n  .login-button {\n    height: 44px;\n    font-size: 0.9rem;\n  }\n}\n\n// Animation for form elements\n.login-form {\n  animation: fadeInUp 0.6s ease-out 0.2s both;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}"], "mappings": ";AAAA,CAAA;AACE,SAAA;AACA,UAAA;AACA,cAAA;AACA,cAAA;;AAEA,CANF,WAME;AACE,cAAA;AACA,iBAAA;;AAEA,CAVJ,WAUI,gBAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAjBJ,WAiBI,gBAAA;AACE,SAAA;AACA,aAAA;;AAKN,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAEA,CALF,WAKE,CAAA;AACE,SAAA;;AAIA,CAVJ,WAUI,eAAA,CAAA;AACE,cAAA;;AAKN,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,UAAA,OAAA,EAAA,OAAA;;AAEA,CANF,aAME,CAAA;AACE,SAAA;AACA,mBAAA;AACA,aAAA;AACA,cAAA,MAAA,KAAA;;AAEA,CAZJ,aAYI,CANF,eAME;AACE,SAAA;AACA,mBAAA;;AAKN,CAAA;AACE,UAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CARF,YAQE,MAAA,KAAA;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CAbF,YAaE;AACE,WAAA;AACA,UAAA;;AAGF,CAlBF,aAkBE;AACE,gBAAA;;AAIJ,CAAA;AACE,WAAA,KAAA,EAAA,EAAA;AACA,mBAAA;;AAEA,CAJF,aAIE,CAAA;AACE,cAAA;AACA,SAAA;AACA,aAAA;AACA,UAAA;;AAEA,CAVJ,aAUI,CANF,YAME,CAAA;AACE,SAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA,MAAA,KAAA;;AAEA,CAhBN,aAgBM,CAZJ,YAYI,CANF,WAME;AACE,SAAA;AACA,mBAAA;;AAQN,MAAA,UAAA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,MAAA,UAAA,CAAA;AACE,oBAAA;AACA,SAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GAjFF;AAkFI,oBAAA;AACA,iBAAA;AACA,SAAA;;AAEA,GAtFJ,aAsFI,CAhFF;AAiFI,gBAAA;;AAIJ,GAxEF;AAyEI,YAAA;AACA,eAAA;;;AAKJ,CAlHA;AAmHE,aAAA,SAAA,KAAA,SAAA,KAAA;;AAGF,WAHE;AAIA;AACE,aAAA;AACA,eAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA;;;", "names": []}