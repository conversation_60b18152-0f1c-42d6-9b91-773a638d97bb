{"version": 3, "sources": ["src/app/features/admin/components/user-management/user-management.component.ts"], "sourcesContent": ["\n    .user-management-container {\n      padding: 1rem;\n      max-width: 1400px;\n      margin: 0 auto;\n    }\n\n    mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .filters {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n      flex-wrap: wrap;\n    }\n\n    .filters mat-form-field {\n      min-width: 200px;\n    }\n\n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .users-table {\n      width: 100%;\n    }\n\n    .user-row:hover {\n      background-color: #f5f5f5;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background-color: #2196f3;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n      font-size: 0.9rem;\n    }\n\n    .user-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .user-info strong {\n      font-size: 0.95rem;\n    }\n\n    .user-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .roles-container {\n      display: flex;\n      gap: 0.25rem;\n      flex-wrap: wrap;\n    }\n\n    .role-employee {\n      background-color: #e3f2fd;\n      color: #1976d2;\n    }\n\n    .role-manager {\n      background-color: #f3e5f5;\n      color: #7b1fa2;\n    }\n\n    .role-hr {\n      background-color: #e8f5e8;\n      color: #2e7d32;\n    }\n\n    .role-admin {\n      background-color: #fff3e0;\n      color: #ef6c00;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 0.25rem;\n    }\n\n    .no-data {\n      text-align: center;\n      padding: 3rem;\n      color: #666;\n    }\n\n    .no-data mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #ccc;\n    }\n\n    @media (max-width: 768px) {\n      .filters {\n        flex-direction: column;\n      }\n\n      .filters mat-form-field {\n        min-width: 100%;\n      }\n\n      .action-buttons {\n        flex-direction: column;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAPA,QAOA;AACE,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA,QAAA;AACE,oBAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;AACA,oBAAA;AACA,SAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CALA,UAKA;AACE,aAAA;;AAGF,CATA,UASA;AACE,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,aAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;;AAGF,CANA,QAMA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GAtGF;AAuGI,oBAAA;;AAGF,GA1GF,QA0GE;AACE,eAAA;;AAGF,GA5BF;AA6BI,oBAAA;;;", "names": []}