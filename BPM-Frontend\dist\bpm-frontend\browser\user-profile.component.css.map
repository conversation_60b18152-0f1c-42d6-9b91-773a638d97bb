{"version": 3, "sources": ["src/app/features/profile/components/user-profile/user-profile.component.ts"], "sourcesContent": ["\n    .profile-container {\n      padding: 2rem;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .profile-header {\n      margin-bottom: 2rem;\n      text-align: center;\n      \n      h1 {\n        color: #1976d2;\n        margin-bottom: 0.5rem;\n      }\n      \n      p {\n        color: #666;\n        font-size: 1.1rem;\n      }\n    }\n\n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 4rem;\n      \n      p {\n        margin-top: 1rem;\n        color: #666;\n      }\n    }\n\n    .profile-content {\n      .mat-tab-group {\n        .mat-tab-body-content {\n          padding: 0;\n        }\n      }\n    }\n\n    .tab-content {\n      padding: 2rem;\n    }\n\n    .form-row {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n      \n      mat-form-field {\n        flex: 1;\n      }\n    }\n\n    .user-roles {\n      margin: 2rem 0;\n      \n      h3 {\n        color: #333;\n        margin-bottom: 1rem;\n      }\n      \n      mat-chip {\n        margin-right: 0.5rem;\n      }\n    }\n\n    .account-info {\n      .info-item {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 0;\n        \n        strong {\n          color: #333;\n        }\n        \n        span {\n          color: #666;\n        }\n        \n        .status-active {\n          color: #4caf50;\n          font-weight: 500;\n        }\n      }\n    }\n\n    mat-card-actions {\n      padding-top: 1rem;\n      \n      button {\n        margin-right: 1rem;\n        \n        mat-icon {\n          margin-right: 0.5rem;\n        }\n      }\n    }\n\n    @media (max-width: 768px) {\n      .profile-container {\n        padding: 1rem;\n      }\n      \n      .form-row {\n        flex-direction: column;\n        gap: 0;\n      }\n      \n      .tab-content {\n        padding: 1rem;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,iBAAA;AACA,cAAA;;AAEA,CAJF,eAIE;AACE,SAAA;AACA,iBAAA;;AAGF,CATF,eASE;AACE,SAAA;AACA,aAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;AAEA,CAPF,kBAOE;AACE,cAAA;AACA,SAAA;;AAMA,CAAA,gBAAA,CAAA,cAAA,CAAA;AACE,WAAA;;AAKN,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;;AAEA,CALF,SAKE;AACE,QAAA;;AAIJ,CAAA;AACE,UAAA,KAAA;;AAEA,CAHF,WAGE;AACE,SAAA;AACA,iBAAA;;AAGF,CARF,WAQE;AACE,gBAAA;;AAKF,CAAA,aAAA,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA,KAAA;;AAEA,CANF,aAME,CANF,UAME;AACE,SAAA;;AAGF,CAVF,aAUE,CAVF,UAUE;AACE,SAAA;;AAGF,CAdF,aAcE,CAdF,UAcE,CAAA;AACE,SAAA;AACA,eAAA;;AAKN;AACE,eAAA;;AAEA,iBAAA;AACE,gBAAA;;AAEA,iBAAA,OAAA;AACE,gBAAA;;AAKN,OAAA,CAAA,SAAA,EAAA;AACE,GAxGF;AAyGI,aAAA;;AAGF,GA9DF;AA+DI,oBAAA;AACA,SAAA;;AAGF,GAvEF;AAwEI,aAAA;;;", "names": []}