{"version": 3, "sources": ["src/app/core/services/user.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { \n  UserDto, \n  CreateUserDto,\n  UpdateUserDto,\n  PaginatedResponse,\n  PaginationParams\n} from '../models';\nimport { environment } from '../../../environments/environment';\n\n\n\nexport interface UserProfileDto {\n  id: string;\n  userName: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  phoneNumber?: string;\n  profilePicture?: string;\n  department?: string;\n  manager?: string;\n  roles: string[];\n  lastLoginAt?: Date;\n  createdAt: Date;\n}\n\nexport interface ChangePasswordDto {\n  currentPassword: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\nexport interface ResetPasswordDto {\n  userId: string;\n  newPassword: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserService {\n  private readonly API_URL = `${environment.apiUrl}/api/Authentication`;\n\n  constructor(private readonly http: HttpClient) {}\n\n  // User CRUD operations\n  getUsers(params?: PaginationParams): Observable<UserDto[]> {\n    let httpParams = new HttpParams();\n\n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<UserDto[]>(`${this.API_URL}/users`, { params: httpParams });\n  }\n\n  getUserById(id: string): Observable<UserDto> {\n    return this.http.get<UserDto>(`${this.API_URL}/users/${id}`);\n  }\n\n  getUserProfile(): Observable<UserProfileDto> {\n    return this.http.get<UserProfileDto>(`${this.API_URL}/profile`);\n  }\n\n  // Note: User creation, update, and deletion might need to be implemented in your backend\n  createUser(user: CreateUserDto): Observable<UserDto> {\n    return this.http.post<UserDto>(`${this.API_URL}/register`, user);\n  }\n\n  updateUser(id: string, user: UpdateUserDto): Observable<UserDto> {\n    return this.http.put<UserDto>(`${this.API_URL}/users/${id}`, user);\n  }\n\n  // Update current user's profile\n  updateProfile(profileData: UpdateUserDto): Observable<void> {\n    return this.http.put<void>(`${this.API_URL}/profile`, profileData);\n  }\n\n  deleteUser(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/users/${id}`);\n  }\n\n  // User management operations\n  activateUser(id: string): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/${id}/activate`, {});\n  }\n\n  deactivateUser(id: string): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/${id}/deactivate`, {});\n  }\n\n  // Role management - based on your backend endpoints\n  assignRole(userId: string, roleName: string): Observable<void> {\n    return this.http.post<void>(`${this.API_URL}/users/${userId}/roles/${roleName}`, {});\n  }\n\n  removeRole(userId: string, roleName: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/users/${userId}/roles/${roleName}`);\n  }\n\n  getUserRoles(userId: string): Observable<string[]> {\n    return this.http.get<string[]>(`${this.API_URL}/users/${userId}/roles`);\n  }\n\n  // Password management\n  changePassword(userId: string, passwordData: ChangePasswordDto): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/${userId}/change-password`, passwordData);\n  }\n\n  resetPassword(passwordData: ResetPasswordDto): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/reset-password`, passwordData);\n  }\n\n  // User search and filtering\n  getUsersByRole(role: string, params?: PaginationParams): Observable<PaginatedResponse<UserDto>> {\n    let httpParams = new HttpParams().set('role', role);\n    \n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<PaginatedResponse<UserDto>>(`${this.API_URL}/by-role`, { params: httpParams });\n  }\n\n  getUsersByDepartment(departmentId: string, params?: PaginationParams): Observable<PaginatedResponse<UserDto>> {\n    let httpParams = new HttpParams().set('departmentId', departmentId);\n    \n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<PaginatedResponse<UserDto>>(`${this.API_URL}/by-department`, { params: httpParams });\n  }\n\n  getManagers(): Observable<UserDto[]> {\n    return this.http.get<UserDto[]>(`${this.API_URL}/managers`);\n  }\n\n  getTeamMembers(managerId: string): Observable<UserDto[]> {\n    return this.http.get<UserDto[]>(`${this.API_URL}/team/${managerId}`);\n  }\n\n  // User statistics\n  getUserStats(): Observable<{\n    totalUsers: number;\n    activeUsers: number;\n    usersByRole: { [role: string]: number };\n    usersByDepartment: { [department: string]: number };\n    recentLogins: number;\n  }> {\n    return this.http.get<any>(`${this.API_URL}/stats`);\n  }\n\n  // Profile picture upload\n  uploadProfilePicture(userId: string, file: File): Observable<{ profilePictureUrl: string }> {\n    const formData = new FormData();\n    formData.append('file', file);\n    \n    return this.http.post<{ profilePictureUrl: string }>(`${this.API_URL}/${userId}/profile-picture`, formData);\n  }\n\n  // Bulk operations\n  bulkUpdateUsers(userIds: string[], updateData: Partial<UpdateUserDto>): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/bulk-update`, { userIds, updateData });\n  }\n\n  bulkDeleteUsers(userIds: string[]): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/bulk-delete`, { body: { userIds } });\n  }\n\n  // Import/Export\n  exportUsers(format: 'csv' | 'excel' = 'excel'): Observable<Blob> {\n    return this.http.get(`${this.API_URL}/export`, {\n      params: { format },\n      responseType: 'blob'\n    });\n  }\n\n  importUsers(file: File): Observable<{\n    successCount: number;\n    errorCount: number;\n    errors: string[];\n  }> {\n    const formData = new FormData();\n    formData.append('file', file);\n    \n    return this.http.post<any>(`${this.API_URL}/import`, formData);\n  }\n\n  // User activity\n  getUserActivity(userId: string, days: number = 30): Observable<{\n    loginHistory: { date: Date; ipAddress: string }[];\n    requestActivity: { date: Date; requestCount: number }[];\n    lastActivity: Date;\n  }> {\n    return this.http.get<any>(`${this.API_URL}/${userId}/activity`, {\n      params: { days: days.toString() }\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AA2CM,IAAO,cAAP,MAAO,aAAW;EAGO;EAFZ,UAAU,GAAG,YAAY,MAAM;EAEhD,YAA6B,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;EAGhD,SAAS,QAAyB;AAChC,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,UAAU,EAAE,QAAQ,WAAU,CAAE;EACjF;EAEA,YAAY,IAAU;AACpB,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,UAAU,EAAE,EAAE;EAC7D;EAEA,iBAAc;AACZ,WAAO,KAAK,KAAK,IAAoB,GAAG,KAAK,OAAO,UAAU;EAChE;;EAGA,WAAW,MAAmB;AAC5B,WAAO,KAAK,KAAK,KAAc,GAAG,KAAK,OAAO,aAAa,IAAI;EACjE;EAEA,WAAW,IAAY,MAAmB;AACxC,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,UAAU,EAAE,IAAI,IAAI;EACnE;;EAGA,cAAc,aAA0B;AACtC,WAAO,KAAK,KAAK,IAAU,GAAG,KAAK,OAAO,YAAY,WAAW;EACnE;EAEA,WAAW,IAAU;AACnB,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,UAAU,EAAE,EAAE;EAC7D;;EAGA,aAAa,IAAU;AACrB,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,IAAI,EAAE,aAAa,CAAA,CAAE;EACnE;EAEA,eAAe,IAAU;AACvB,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,IAAI,EAAE,eAAe,CAAA,CAAE;EACrE;;EAGA,WAAW,QAAgB,UAAgB;AACzC,WAAO,KAAK,KAAK,KAAW,GAAG,KAAK,OAAO,UAAU,MAAM,UAAU,QAAQ,IAAI,CAAA,CAAE;EACrF;EAEA,WAAW,QAAgB,UAAgB;AACzC,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,UAAU,MAAM,UAAU,QAAQ,EAAE;EACnF;EAEA,aAAa,QAAc;AACzB,WAAO,KAAK,KAAK,IAAc,GAAG,KAAK,OAAO,UAAU,MAAM,QAAQ;EACxE;;EAGA,eAAe,QAAgB,cAA+B;AAC5D,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,IAAI,MAAM,oBAAoB,YAAY;EACxF;EAEA,cAAc,cAA8B;AAC1C,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,mBAAmB,YAAY;EAC7E;;EAGA,eAAe,MAAc,QAAyB;AACpD,QAAI,aAAa,IAAI,WAAU,EAAG,IAAI,QAAQ,IAAI;AAElD,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAgC,GAAG,KAAK,OAAO,YAAY,EAAE,QAAQ,WAAU,CAAE;EACpG;EAEA,qBAAqB,cAAsB,QAAyB;AAClE,QAAI,aAAa,IAAI,WAAU,EAAG,IAAI,gBAAgB,YAAY;AAElE,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAgC,GAAG,KAAK,OAAO,kBAAkB,EAAE,QAAQ,WAAU,CAAE;EAC1G;EAEA,cAAW;AACT,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,WAAW;EAC5D;EAEA,eAAe,WAAiB;AAC9B,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,SAAS,SAAS,EAAE;EACrE;;EAGA,eAAY;AAOV,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,QAAQ;EACnD;;EAGA,qBAAqB,QAAgB,MAAU;AAC7C,UAAM,WAAW,IAAI,SAAQ;AAC7B,aAAS,OAAO,QAAQ,IAAI;AAE5B,WAAO,KAAK,KAAK,KAAoC,GAAG,KAAK,OAAO,IAAI,MAAM,oBAAoB,QAAQ;EAC5G;;EAGA,gBAAgB,SAAmB,YAAkC;AACnE,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,gBAAgB,EAAE,SAAS,WAAU,CAAE;EACrF;EAEA,gBAAgB,SAAiB;AAC/B,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,gBAAgB,EAAE,MAAM,EAAE,QAAO,EAAE,CAAE;EACpF;;EAGA,YAAY,SAA0B,SAAO;AAC3C,WAAO,KAAK,KAAK,IAAI,GAAG,KAAK,OAAO,WAAW;MAC7C,QAAQ,EAAE,OAAM;MAChB,cAAc;KACf;EACH;EAEA,YAAY,MAAU;AAKpB,UAAM,WAAW,IAAI,SAAQ;AAC7B,aAAS,OAAO,QAAQ,IAAI;AAE5B,WAAO,KAAK,KAAK,KAAU,GAAG,KAAK,OAAO,WAAW,QAAQ;EAC/D;;EAGA,gBAAgB,QAAgB,OAAe,IAAE;AAK/C,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,IAAI,MAAM,aAAa;MAC9D,QAAQ,EAAE,MAAM,KAAK,SAAQ,EAAE;KAChC;EACH;;qCA1KW,cAAW,mBAAA,UAAA,CAAA;EAAA;4EAAX,cAAW,SAAX,aAAW,WAAA,YAFV,OAAM,CAAA;;;sEAEP,aAAW,CAAA;UAHvB;WAAW;MACV,YAAY;KACb;;;", "names": []}