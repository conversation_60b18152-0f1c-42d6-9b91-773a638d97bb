import {
  environment
} from "./chunk-AKJJBQK4.js";
import {
  HttpClient,
  HttpParams,
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-XLTWONBW.js";

// src/app/core/services/user.service.ts
var UserService = class _UserService {
  http;
  API_URL = `${environment.apiUrl}/api/Authentication`;
  constructor(http) {
    this.http = http;
  }
  // User CRUD operations
  getUsers(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(`${this.API_URL}/users`, { params: httpParams });
  }
  getUserById(id) {
    return this.http.get(`${this.API_URL}/users/${id}`);
  }
  getUserProfile() {
    return this.http.get(`${this.API_URL}/profile`);
  }
  // Note: User creation, update, and deletion might need to be implemented in your backend
  createUser(user) {
    return this.http.post(`${this.API_URL}/register`, user);
  }
  updateUser(id, user) {
    return this.http.put(`${this.API_URL}/users/${id}`, user);
  }
  // Update current user's profile
  updateProfile(profileData) {
    return this.http.put(`${this.API_URL}/profile`, profileData);
  }
  deleteUser(id) {
    return this.http.delete(`${this.API_URL}/users/${id}`);
  }
  // User management operations
  activateUser(id) {
    return this.http.patch(`${this.API_URL}/${id}/activate`, {});
  }
  deactivateUser(id) {
    return this.http.patch(`${this.API_URL}/${id}/deactivate`, {});
  }
  // Role management - based on your backend endpoints
  assignRole(userId, roleName) {
    return this.http.post(`${this.API_URL}/users/${userId}/roles/${roleName}`, {});
  }
  removeRole(userId, roleName) {
    return this.http.delete(`${this.API_URL}/users/${userId}/roles/${roleName}`);
  }
  getUserRoles(userId) {
    return this.http.get(`${this.API_URL}/users/${userId}/roles`);
  }
  // Password management
  changePassword(userId, passwordData) {
    return this.http.patch(`${this.API_URL}/${userId}/change-password`, passwordData);
  }
  resetPassword(passwordData) {
    return this.http.patch(`${this.API_URL}/reset-password`, passwordData);
  }
  // User search and filtering
  getUsersByRole(role, params) {
    let httpParams = new HttpParams().set("role", role);
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(`${this.API_URL}/by-role`, { params: httpParams });
  }
  getUsersByDepartment(departmentId, params) {
    let httpParams = new HttpParams().set("departmentId", departmentId);
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(`${this.API_URL}/by-department`, { params: httpParams });
  }
  getManagers() {
    return this.http.get(`${this.API_URL}/managers`);
  }
  getTeamMembers(managerId) {
    return this.http.get(`${this.API_URL}/team/${managerId}`);
  }
  // User statistics
  getUserStats() {
    return this.http.get(`${this.API_URL}/stats`);
  }
  // Profile picture upload
  uploadProfilePicture(userId, file) {
    const formData = new FormData();
    formData.append("file", file);
    return this.http.post(`${this.API_URL}/${userId}/profile-picture`, formData);
  }
  // Bulk operations
  bulkUpdateUsers(userIds, updateData) {
    return this.http.patch(`${this.API_URL}/bulk-update`, { userIds, updateData });
  }
  bulkDeleteUsers(userIds) {
    return this.http.delete(`${this.API_URL}/bulk-delete`, { body: { userIds } });
  }
  // Import/Export
  exportUsers(format = "excel") {
    return this.http.get(`${this.API_URL}/export`, {
      params: { format },
      responseType: "blob"
    });
  }
  importUsers(file) {
    const formData = new FormData();
    formData.append("file", file);
    return this.http.post(`${this.API_URL}/import`, formData);
  }
  // User activity
  getUserActivity(userId, days = 30) {
    return this.http.get(`${this.API_URL}/${userId}/activity`, {
      params: { days: days.toString() }
    });
  }
  static \u0275fac = function UserService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UserService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _UserService, factory: _UserService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UserService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  UserService
};
//# sourceMappingURL=chunk-3JNBYCCA.js.map
