{"version": 3, "sources": ["src/app/features/workflows/components/workflow-details/workflow-details.component.ts"], "sourcesContent": ["\n    .workflow-details-container {\n      padding: 1rem;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 3rem;\n    }\n\n    .header-card {\n      margin-bottom: 1rem;\n    }\n\n    .title-section {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .title-section h2 {\n      margin: 0;\n      font-size: 1.5rem;\n    }\n\n    .workflow-version {\n      color: #666;\n      font-size: 0.9rem;\n      font-weight: normal;\n    }\n\n    mat-card-title {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      width: 100%;\n    }\n\n    .workflow-info {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 1rem;\n      margin: 1rem 0;\n    }\n\n    .info-item {\n      display: flex;\n      flex-direction: column;\n      gap: 0.25rem;\n    }\n\n    .workflow-id {\n      font-family: monospace;\n      background-color: #f5f5f5;\n      padding: 4px 8px;\n      border-radius: 4px;\n      font-size: 0.9rem;\n    }\n\n    .description-section {\n      margin-top: 1rem;\n    }\n\n    .description-section h3 {\n      margin-bottom: 0.5rem;\n      color: #333;\n    }\n\n    .description-section p {\n      line-height: 1.6;\n      color: #666;\n    }\n\n    .steps-card {\n      margin-top: 1rem;\n    }\n\n    .workflow-stepper {\n      margin: 1rem 0;\n    }\n\n    .step-label {\n      display: flex;\n      flex-direction: column;\n      align-items: flex-start;\n    }\n\n    .step-name {\n      font-weight: 500;\n    }\n\n    .step-order {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .step-content {\n      padding: 1rem 0;\n    }\n\n    .step-details {\n      background-color: #f8f9fa;\n      padding: 1rem;\n      border-radius: 4px;\n    }\n\n    .step-detail {\n      margin-bottom: 0.5rem;\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .role-chip {\n      background-color: #e3f2fd;\n      color: #1976d2;\n      font-size: 0.75rem;\n    }\n\n    .steps-table-section {\n      margin-top: 2rem;\n    }\n\n    .steps-table-section h4 {\n      margin-bottom: 1rem;\n      color: #333;\n    }\n\n    .steps-table {\n      width: 100%;\n    }\n\n    .status-active {\n      background-color: #d4edda;\n      color: #155724;\n    }\n\n    .status-inactive {\n      background-color: #f8d7da;\n      color: #721c24;\n    }\n\n    .no-steps {\n      text-align: center;\n      padding: 2rem;\n      color: #666;\n    }\n\n    .no-steps mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n      color: #ff9800;\n      margin-bottom: 1rem;\n    }\n\n    .error-container {\n      display: flex;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .error-content {\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .error-content mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      color: #f44336;\n      margin-bottom: 1rem;\n    }\n\n    @media (max-width: 768px) {\n      mat-card-title {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 1rem;\n      }\n\n      .workflow-info {\n        grid-template-columns: 1fr;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CANA,cAMA;AACE,UAAA;AACA,aAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;;AAGF;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,UAAA,KAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,eAAA;AACA,oBAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAJA,oBAIA;AACE,iBAAA;AACA,SAAA;;AAGF,CATA,oBASA;AACE,eAAA;AACA,SAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,UAAA,KAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;;AAGF,CAAA;AACE,eAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA,KAAA;;AAGF,CAAA;AACE,oBAAA;AACA,WAAA;AACA,iBAAA;;AAGF,CAAA;AACE,iBAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAJA,oBAIA;AACE,iBAAA;AACA,SAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;;AAGF,CANA,SAMA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;;AAGF,CALA,cAKA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;AACA,iBAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE;AACE,oBAAA;AACA,iBAAA;AACA,SAAA;;AAGF,GAhJF;AAiJI,2BAAA;;;", "names": []}