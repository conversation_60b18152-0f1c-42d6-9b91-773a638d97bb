{"version": 3, "sources": ["src/app/features/admin/components/workflow-designer/workflow-designer.component.scss"], "sourcesContent": [".workflow-designer-container {\r\n  padding: 2rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n\r\n  mat-card {\r\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n    border-radius: 12px;\r\n  }\r\n\r\n  mat-card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-bottom: 1rem;\r\n    border-bottom: 1px solid #e0e0e0;\r\n\r\n    mat-card-title {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n      font-size: 1.5rem;\r\n      font-weight: 600;\r\n      color: #333;\r\n\r\n      mat-icon {\r\n        color: #1976d2;\r\n      }\r\n    }\r\n\r\n    .header-actions {\r\n      display: flex;\r\n      gap: 1rem;\r\n    }\r\n  }\r\n\r\n  .designer-placeholder {\r\n    text-align: center;\r\n    padding: 3rem 2rem;\r\n\r\n    .placeholder-icon {\r\n      margin-bottom: 1rem;\r\n\r\n      mat-icon {\r\n        font-size: 64px;\r\n        width: 64px;\r\n        height: 64px;\r\n        color: #1976d2;\r\n        opacity: 0.7;\r\n      }\r\n    }\r\n\r\n    h2 {\r\n      margin: 1rem 0;\r\n      color: #333;\r\n      font-weight: 500;\r\n    }\r\n\r\n    p {\r\n      color: #666;\r\n      font-size: 1.1rem;\r\n      margin-bottom: 2rem;\r\n      max-width: 600px;\r\n      margin-left: auto;\r\n      margin-right: auto;\r\n    }\r\n\r\n    .feature-list {\r\n      display: grid;\r\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n      gap: 1.5rem;\r\n      margin: 2rem 0;\r\n      max-width: 800px;\r\n      margin-left: auto;\r\n      margin-right: auto;\r\n\r\n      .feature-item {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 0.5rem;\r\n        padding: 1rem;\r\n        background: #f8f9fa;\r\n        border-radius: 8px;\r\n        border-left: 4px solid #1976d2;\r\n\r\n        mat-icon {\r\n          color: #1976d2;\r\n        }\r\n\r\n        span {\r\n          font-weight: 500;\r\n          color: #333;\r\n        }\r\n      }\r\n    }\r\n\r\n    .action-buttons {\r\n      display: flex;\r\n      gap: 1rem;\r\n      justify-content: center;\r\n      margin-top: 2rem;\r\n      flex-wrap: wrap;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .workflow-designer-container {\r\n    padding: 1rem;\r\n\r\n    mat-card-header {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      gap: 1rem;\r\n\r\n      .header-actions {\r\n        width: 100%;\r\n        justify-content: flex-start;\r\n      }\r\n    }\r\n\r\n    .designer-placeholder {\r\n      padding: 2rem 1rem;\r\n\r\n      .feature-list {\r\n        grid-template-columns: 1fr;\r\n      }\r\n\r\n      .action-buttons {\r\n        flex-direction: column;\r\n        align-items: center;\r\n\r\n        button {\r\n          width: 100%;\r\n          max-width: 300px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAEA,CALF,4BAKE;AACE,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,iBAAA;;AAGF,CAVF,4BAUE;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,kBAAA;AACA,iBAAA,IAAA,MAAA;;AAEA,CAjBJ,4BAiBI,gBAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAEA,CAzBN,4BAyBM,gBAAA,eAAA;AACE,SAAA;;AAIJ,CA9BJ,4BA8BI,gBAAA,CAAA;AACE,WAAA;AACA,OAAA;;AAIJ,CApCF,4BAoCE,CAAA;AACE,cAAA;AACA,WAAA,KAAA;;AAEA,CAxCJ,4BAwCI,CAJF,qBAIE,CAAA;AACE,iBAAA;;AAEA,CA3CN,4BA2CM,CAPJ,qBAOI,CAHF,iBAGE;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;AACA,WAAA;;AAIJ,CApDJ,4BAoDI,CAhBF,qBAgBE;AACE,UAAA,KAAA;AACA,SAAA;AACA,eAAA;;AAGF,CA1DJ,4BA0DI,CAtBF,qBAsBE;AACE,SAAA;AACA,aAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,gBAAA;;AAGF,CAnEJ,4BAmEI,CA/BF,qBA+BE,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,UAAA,KAAA;AACA,aAAA;AACA,eAAA;AACA,gBAAA;;AAEA,CA5EN,4BA4EM,CAxCJ,qBAwCI,CATF,aASE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,eAAA,IAAA,MAAA;;AAEA,CArFR,4BAqFQ,CAjDN,qBAiDM,CAlBJ,aAkBI,CATF,aASE;AACE,SAAA;;AAGF,CAzFR,4BAyFQ,CArDN,qBAqDM,CAtBJ,aAsBI,CAbF,aAaE;AACE,eAAA;AACA,SAAA;;AAKN,CAhGJ,4BAgGI,CA5DF,qBA4DE,CAAA;AACE,WAAA;AACA,OAAA;AACA,mBAAA;AACA,cAAA;AACA,aAAA;;AAKN,OAAA,CAAA,SAAA,EAAA;AACE,GA3GF;AA4GI,aAAA;;AAEA,GA9GJ,4BA8GI;AACE,oBAAA;AACA,iBAAA;AACA,SAAA;;AAEA,GAnHN,4BAmHM,gBAAA,CArFF;AAsFI,WAAA;AACA,qBAAA;;AAIJ,GAzHJ,4BAyHI,CArFF;AAsFI,aAAA,KAAA;;AAEA,GA5HN,4BA4HM,CAxFJ,qBAwFI,CAzDF;AA0DI,2BAAA;;AAGF,GAhIN,4BAgIM,CA5FJ,qBA4FI,CAhCF;AAiCI,oBAAA;AACA,iBAAA;;AAEA,GApIR,4BAoIQ,CAhGN,qBAgGM,CApCJ,eAoCI;AACE,WAAA;AACA,eAAA;;;", "names": []}