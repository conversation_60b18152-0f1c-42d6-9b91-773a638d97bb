{"version": 3, "sources": ["src/app/features/dashboard/components/reporting-dashboard/reporting-dashboard.component.scss"], "sourcesContent": [".reporting-dashboard-container {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.dashboard-header {\n  margin-bottom: 2rem;\n\n  h1 {\n    color: #1976d2;\n    margin-bottom: 0.5rem;\n  }\n\n  p {\n    color: #666;\n    font-size: 1.1rem;\n  }\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem;\n\n  p {\n    margin-top: 1rem;\n    color: #666;\n  }\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.stat-card {\n  .mat-card-header {\n    padding-bottom: 1rem;\n  }\n\n  .stat-number {\n    font-size: 2.5rem;\n    font-weight: bold;\n    color: #1976d2;\n    line-height: 1;\n  }\n\n  .stat-label {\n    color: #666;\n    font-size: 0.9rem;\n    margin-top: 0.5rem;\n  }\n\n  .users-icon {\n    background-color: #4caf50;\n    color: white;\n  }\n\n  .workflows-icon {\n    background-color: #ff9800;\n    color: white;\n  }\n\n  .requests-icon {\n    background-color: #2196f3;\n    color: white;\n  }\n\n  .pending-icon {\n    background-color: #f44336;\n    color: white;\n  }\n}\n\n.admin-actions {\n  h2 {\n    color: #333;\n    margin-bottom: 1.5rem;\n  }\n}\n\n.action-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n}\n\n.action-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  }\n\n  .mat-card-header {\n    padding-bottom: 1rem;\n  }\n\n  .mat-card-title {\n    color: #333;\n    font-weight: 600;\n  }\n\n  .mat-card-subtitle {\n    color: #666;\n    margin-top: 0.5rem;\n  }\n\n  .mat-card-actions {\n    padding-top: 1rem;\n\n    button {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n  }\n}\n\n.quick-reports {\n  h2 {\n    color: #333;\n    margin-bottom: 1.5rem;\n  }\n\n  .report-buttons {\n    display: flex;\n    gap: 1rem;\n    flex-wrap: wrap;\n\n    button {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      min-width: 150px;\n    }\n  }\n}\n\n// Responsive design\n@media (max-width: 768px) {\n  .reporting-dashboard-container {\n    padding: 1rem;\n  }\n\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .action-cards {\n    grid-template-columns: 1fr;\n  }\n\n  .report-buttons {\n    flex-direction: column;\n\n    button {\n      width: 100%;\n    }\n  }\n}\n\n@media (max-width: 480px) {\n  .dashboard-header {\n    text-align: center;\n\n    h1 {\n      font-size: 1.8rem;\n    }\n  }\n\n  .stat-card .stat-number {\n    font-size: 2rem;\n  }\n}\n"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,iBAAA;;AAEA,CAHF,iBAGE;AACE,SAAA;AACA,iBAAA;;AAGF,CARF,iBAQE;AACE,SAAA;AACA,aAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;AAEA,CAPF,kBAOE;AACE,cAAA;AACA,SAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAIA,CAAA,UAAA,CAAA;AACE,kBAAA;;AAGF,CAJA,UAIA,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;;AAGF,CAXA,UAWA,CAAA;AACE,SAAA;AACA,aAAA;AACA,cAAA;;AAGF,CAjBA,UAiBA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAtBA,UAsBA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CA3BA,UA2BA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAhCA,UAgCA,CAAA;AACE,oBAAA;AACA,SAAA;;AAKF,CAAA,cAAA;AACE,SAAA;AACA,iBAAA;;AAIJ,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAGF,CAAA;AACE,UAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAJF,WAIE;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CATF,YASE,CA5DA;AA6DE,kBAAA;;AAGF,CAbF,YAaE,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAlBF,YAkBE,CAAA;AACE,SAAA;AACA,cAAA;;AAGF,CAvBF,YAuBE,CAAA;AACE,eAAA;;AAEA,CA1BJ,YA0BI,CAHF,iBAGE;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAMJ,CAAA,cAAA;AACE,SAAA;AACA,iBAAA;;AAGF,CALA,cAKA,CAAA;AACE,WAAA;AACA,OAAA;AACA,aAAA;;AAEA,CAVF,cAUE,CALF,eAKE;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,aAAA;;AAMN,OAAA,CAAA,SAAA,EAAA;AACE,GA1JF;AA2JI,aAAA;;AAGF,GAvHF;AAwHI,2BAAA;;AAGF,GAtEF;AAuEI,2BAAA;;AAGF,GA5BA;AA6BE,oBAAA;;AAEA,GA/BF,eA+BE;AACE,WAAA;;;AAKN,OAAA,CAAA,SAAA,EAAA;AACE,GA1KF;AA2KI,gBAAA;;AAEA,GA7KJ,iBA6KI;AACE,eAAA;;AAIJ,GAzIA,UAyIA,CArIA;AAsIE,eAAA;;;", "names": []}