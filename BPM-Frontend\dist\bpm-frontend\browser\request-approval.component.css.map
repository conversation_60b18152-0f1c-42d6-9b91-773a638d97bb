{"version": 3, "sources": ["src/app/features/requests/components/request-approval/request-approval.component.ts"], "sourcesContent": ["\n    .approval-container {\n      padding: 1rem;\n      max-width: 1400px;\n      margin: 0 auto;\n    }\n\n    mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .pending-count {\n      background-color: #ff9800;\n      color: white;\n    }\n\n    .filters {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n      flex-wrap: wrap;\n    }\n\n    .filters mat-form-field {\n      min-width: 200px;\n    }\n\n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .approval-table {\n      width: 100%;\n    }\n\n    .approval-row:hover {\n      background-color: #f5f5f5;\n    }\n\n    .requester-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .requester-info strong {\n      font-size: 0.9rem;\n    }\n\n    .requester-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .request-info {\n      max-width: 300px;\n    }\n\n    .request-info strong {\n      display: block;\n      margin-bottom: 0.25rem;\n    }\n\n    .request-type {\n      background-color: #e3f2fd;\n      color: #1976d2;\n      padding: 2px 8px;\n      border-radius: 12px;\n      font-size: 0.75rem;\n      font-weight: 500;\n    }\n\n    .request-description {\n      margin: 0.5rem 0 0 0;\n      color: #666;\n      font-size: 0.8rem;\n      line-height: 1.4;\n    }\n\n    .step-info {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n    }\n\n    .step-name {\n      font-weight: 500;\n      font-size: 0.9rem;\n    }\n\n    .step-role {\n      background-color: #f3e5f5;\n      color: #7b1fa2;\n      font-size: 0.75rem;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 0.5rem;\n      align-items: center;\n    }\n\n    .action-buttons button {\n      min-width: auto;\n    }\n\n    .priority-high {\n      background-color: #ffebee;\n      color: #c62828;\n    }\n\n    .priority-medium {\n      background-color: #fff3e0;\n      color: #ef6c00;\n    }\n\n    .priority-low {\n      background-color: #e8f5e8;\n      color: #2e7d32;\n    }\n\n    .no-data {\n      text-align: center;\n      padding: 3rem;\n      color: #666;\n    }\n\n    .no-data mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #4caf50;\n    }\n\n    @media (max-width: 768px) {\n      .filters {\n        flex-direction: column;\n      }\n      \n      .filters mat-form-field {\n        min-width: 100%;\n      }\n\n      .action-buttons {\n        flex-direction: column;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAPA,QAOA;AACE,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA,YAAA;AACE,oBAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CALA,eAKA;AACE,aAAA;;AAGF,CATA,eASA;AACE,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,aAAA;;AAGF,CAJA,aAIA;AACE,WAAA;AACA,iBAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,UAAA,OAAA,EAAA,EAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,eAAA;AACA,aAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,eAAA;;AAGF,CANA,eAMA;AACE,aAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;;AAGF,CANA,QAMA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GA7HF;AA8HI,oBAAA;;AAGF,GAjIF,QAiIE;AACE,eAAA;;AAGF,GAhDF;AAiDI,oBAAA;;;", "names": []}