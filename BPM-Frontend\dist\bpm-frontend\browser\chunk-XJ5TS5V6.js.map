{"version": 3, "sources": ["src/app/core/models/request.models.ts"], "sourcesContent": ["import { BaseDto } from './workflow.models';\n\nexport enum RequestType {\n  Leave = 1,\n  Expense = 2,\n  Training = 3,\n  ITSupport = 4,\n  ProfileUpdate = 5\n}\n\nexport enum RequestStatus {\n  Pending = 1,\n  Approved = 2,\n  Rejected = 3,\n  Archived = 4\n}\n\nexport enum StepStatus {\n  Pending = 1,\n  Approved = 2,\n  Rejected = 3\n}\n\nexport interface RequestDto extends BaseDto {\n  type: RequestType;\n  initiatorId: string;\n  initiatorName: string;\n  status: RequestStatus;\n  description?: string;\n  title?: string;\n  requestSteps: RequestStepDto[];\n}\n\nexport interface CreateRequestDto {\n  type: RequestType;\n  description?: string;\n  title?: string;\n  workflowId: string;\n}\n\nexport interface UpdateRequestDto {\n  type: RequestType;\n  description?: string;\n  title?: string;\n  status: RequestStatus;\n}\n\nexport interface RequestStepDto extends BaseDto {\n  requestId: string;\n  workflowStepId: string;\n  workflowStepName: string;\n  responsibleRole: string;\n  status: StepStatus;\n  validatedAt?: Date;\n  validatorId?: string;\n  validatorName?: string;\n  comments?: string;\n}\n\nexport interface ApproveRejectStepDto {\n  comments?: string;\n}\n\nexport interface RequestSummary {\n  totalRequests: number;\n  pendingRequests: number;\n  approvedRequests: number;\n  rejectedRequests: number;\n  requestsByType: { [key in RequestType]: number };\n}\n"], "mappings": ";AAEA,IAAY;CAAZ,SAAYA,cAAW;AACrB,EAAAA,aAAAA,aAAA,OAAA,IAAA,CAAA,IAAA;AACA,EAAAA,aAAAA,aAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,aAAAA,aAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,aAAAA,aAAA,WAAA,IAAA,CAAA,IAAA;AACA,EAAAA,aAAAA,aAAA,eAAA,IAAA,CAAA,IAAA;AACF,GANY,gBAAA,cAAW,CAAA,EAAA;AAQvB,IAAY;CAAZ,SAAYC,gBAAa;AACvB,EAAAA,eAAAA,eAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,UAAA,IAAA,CAAA,IAAA;AACF,GALY,kBAAA,gBAAa,CAAA,EAAA;AAOzB,IAAY;CAAZ,SAAYC,aAAU;AACpB,EAAAA,YAAAA,YAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,YAAAA,YAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,YAAAA,YAAA,UAAA,IAAA,CAAA,IAAA;AACF,GAJY,eAAA,aAAU,CAAA,EAAA;", "names": ["RequestType", "RequestStatus", "StepStatus"]}