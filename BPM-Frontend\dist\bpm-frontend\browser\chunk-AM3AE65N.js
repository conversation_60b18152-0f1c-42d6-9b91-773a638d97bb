import {
  environment
} from "./chunk-AKJJBQK4.js";
import {
  HttpClient,
  HttpParams,
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-XLTWONBW.js";

// src/app/core/services/request.service.ts
var RequestService = class _RequestService {
  http;
  API_URL = `${environment.apiUrl}/api/Request`;
  constructor(http) {
    this.http = http;
  }
  getRequests(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(this.API_URL, { params: httpParams });
  }
  getMyRequests(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(`${this.API_URL}/my-requests`, { params: httpParams });
  }
  getPendingRequests(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(`${this.API_URL}/pending-approvals`, { params: httpParams });
  }
  getRequestById(id) {
    return this.http.get(`${this.API_URL}/${id}`);
  }
  createRequest(request) {
    return this.http.post(this.API_URL, request);
  }
  updateRequest(id, request) {
    return this.http.put(`${this.API_URL}/${id}`, request);
  }
  deleteRequest(id) {
    return this.http.delete(`${this.API_URL}/${id}`);
  }
  // Request Steps
  getRequestSteps(requestId) {
    return this.http.get(`${this.API_URL}/${requestId}/steps`);
  }
  // Request Status Filtering
  getRequestsByStatus(status, params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(`${this.API_URL}/status/${status}`, { params: httpParams });
  }
  approveStep(requestId, stepId, data) {
    return this.http.post(`${this.API_URL}/${requestId}/steps/${stepId}/approve`, data);
  }
  rejectStep(requestId, stepId, data) {
    return this.http.post(`${this.API_URL}/${requestId}/steps/${stepId}/reject`, data);
  }
  getRequestSummary() {
    return this.http.get(`${this.API_URL}/summary`);
  }
  getRequestsByType(type, params) {
    let httpParams = new HttpParams().set("type", type.toString());
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(`${this.API_URL}/by-type`, { params: httpParams });
  }
  getPendingApprovals(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(`${this.API_URL}/pending-approvals`, { params: httpParams });
  }
  getRequestsForRole(role, params) {
    let httpParams = new HttpParams().set("role", role);
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(`${this.API_URL}/for-role`, { params: httpParams });
  }
  static \u0275fac = function RequestService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RequestService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _RequestService, factory: _RequestService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RequestService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  RequestService
};
//# sourceMappingURL=chunk-AM3AE65N.js.map
