{"version": 3, "sources": ["src/app/features/dashboard/components/dashboard/dashboard.component.scss"], "sourcesContent": [".dashboard-container {\n  padding: 0;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.welcome-section {\n  margin-bottom: 2rem;\n  text-align: center;\n  \n  h1 {\n    font-size: 2.5rem;\n    font-weight: 600;\n    color: #333;\n    margin-bottom: 0.5rem;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  \n  p {\n    font-size: 1.1rem;\n    color: #666;\n    margin: 0;\n  }\n}\n\n.dashboard-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n\n.dashboard-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n  \n  mat-card-header {\n    padding-bottom: 1rem;\n    \n    mat-icon[mat-card-avatar] {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n      font-size: 24px;\n      width: 40px;\n      height: 40px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n    \n    mat-card-title {\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: #333;\n    }\n    \n    mat-card-subtitle {\n      color: #666;\n      font-size: 0.9rem;\n    }\n  }\n  \n  .card-stats {\n    display: flex;\n    justify-content: space-around;\n    margin: 1rem 0;\n    \n    .stat-item {\n      text-align: center;\n      \n      .stat-number {\n        display: block;\n        font-size: 2rem;\n        font-weight: 700;\n        color: #667eea;\n        line-height: 1;\n      }\n      \n      .stat-label {\n        display: block;\n        font-size: 0.8rem;\n        color: #666;\n        margin-top: 0.25rem;\n      }\n    }\n  }\n  \n  mat-card-actions {\n    padding-top: 1rem;\n    border-top: 1px solid #f0f0f0;\n    \n    button {\n      color: #667eea;\n      font-weight: 500;\n      \n      mat-icon {\n        margin-left: 0.5rem;\n      }\n    }\n  }\n}\n\n.quick-actions-section {\n  margin-bottom: 3rem;\n  \n  h2 {\n    font-size: 1.5rem;\n    font-weight: 600;\n    color: #333;\n    margin-bottom: 1rem;\n  }\n  \n  .actions-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 1rem;\n    \n    button {\n      height: 60px;\n      font-size: 1rem;\n      font-weight: 500;\n      border-radius: 8px;\n      transition: all 0.3s ease;\n      \n      &:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n      }\n      \n      mat-icon {\n        margin-right: 0.5rem;\n      }\n    }\n  }\n}\n\n.recent-activity-section {\n  mat-card {\n    border-radius: 12px;\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    \n    mat-card-header {\n      mat-card-title {\n        font-size: 1.25rem;\n        font-weight: 600;\n        color: #333;\n      }\n    }\n    \n    .activity-list {\n      .activity-item {\n        display: flex;\n        align-items: flex-start;\n        padding: 1rem 0;\n        border-bottom: 1px solid #f0f0f0;\n        \n        &:last-child {\n          border-bottom: none;\n        }\n        \n        mat-icon {\n          margin-right: 1rem;\n          margin-top: 0.25rem;\n          font-size: 20px;\n          width: 20px;\n          height: 20px;\n        }\n        \n        .activity-content {\n          flex: 1;\n          \n          .activity-title {\n            font-weight: 500;\n            color: #333;\n            margin-bottom: 0.25rem;\n          }\n          \n          .activity-description {\n            color: #666;\n            font-size: 0.9rem;\n            margin-bottom: 0.25rem;\n          }\n          \n          .activity-time {\n            color: #999;\n            font-size: 0.8rem;\n          }\n        }\n      }\n    }\n    \n    .no-activity {\n      text-align: center;\n      padding: 2rem;\n      color: #666;\n      \n      mat-icon {\n        font-size: 48px;\n        width: 48px;\n        height: 48px;\n        margin-bottom: 1rem;\n        opacity: 0.5;\n      }\n      \n      p {\n        margin: 0;\n        font-size: 1rem;\n      }\n    }\n  }\n}\n\n// Responsive Design\n@media (max-width: 768px) {\n  .welcome-section {\n    h1 {\n      font-size: 2rem;\n    }\n    \n    p {\n      font-size: 1rem;\n    }\n  }\n  \n  .dashboard-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .actions-grid {\n    grid-template-columns: 1fr;\n    \n    button {\n      height: 50px;\n      font-size: 0.9rem;\n    }\n  }\n}\n\n@media (max-width: 480px) {\n  .dashboard-container {\n    padding: 0 0.5rem;\n  }\n  \n  .welcome-section {\n    h1 {\n      font-size: 1.75rem;\n    }\n  }\n  \n  .dashboard-card {\n    .card-stats {\n      .stat-item {\n        .stat-number {\n          font-size: 1.5rem;\n        }\n        \n        .stat-label {\n          font-size: 0.75rem;\n        }\n      }\n    }\n  }\n}\n\n// Animation for cards\n.dashboard-card {\n  animation: fadeInUp 0.6s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// Stagger animation for cards\n.dashboard-card:nth-child(1) { animation-delay: 0.1s; }\n.dashboard-card:nth-child(2) { animation-delay: 0.2s; }\n.dashboard-card:nth-child(3) { animation-delay: 0.3s; }\n.dashboard-card:nth-child(4) { animation-delay: 0.4s; }"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,iBAAA;AACA,cAAA;;AAEA,CAJF,gBAIE;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,2BAAA;AACA,2BAAA;AACA,mBAAA;;AAGF,CAfF,gBAeE;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,UAAA;AACA,cAAA,IAAA,KAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CANF,cAME;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAXF,eAWE;AACE,kBAAA;;AAEA,CAdJ,eAcI,gBAAA,QAAA,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,SAAA;AACA,aAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;;AAGF,CAzBJ,eAyBI,gBAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CA/BJ,eA+BI,gBAAA;AACE,SAAA;AACA,aAAA;;AAIJ,CArCF,eAqCE,CAAA;AACE,WAAA;AACA,mBAAA;AACA,UAAA,KAAA;;AAEA,CA1CJ,eA0CI,CALF,WAKE,CAAA;AACE,cAAA;;AAEA,CA7CN,eA6CM,CARJ,WAQI,CAHF,UAGE,CAAA;AACE,WAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;;AAGF,CArDN,eAqDM,CAhBJ,WAgBI,CAXF,UAWE,CAAA;AACE,WAAA;AACA,aAAA;AACA,SAAA;AACA,cAAA;;AAKN,CA9DF,eA8DE;AACE,eAAA;AACA,cAAA,IAAA,MAAA;;AAEA,CAlEJ,eAkEI,iBAAA;AACE,SAAA;AACA,eAAA;;AAEA,CAtEN,eAsEM,iBAAA,OAAA;AACE,eAAA;;AAMR,CAAA;AACE,iBAAA;;AAEA,CAHF,sBAGE;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAVF,sBAUE,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAEA,CAfJ,sBAeI,CALF,aAKE;AACE,UAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAtBN,sBAsBM,CAZJ,aAYI,MAAA;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CA3BN,sBA2BM,CAjBJ,aAiBI,OAAA;AACE,gBAAA;;AAON,CAAA,wBAAA;AACE,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGE,CALJ,wBAKI,SAAA,gBAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAKF,CAbJ,wBAaI,SAAA,CAAA,cAAA,CAAA;AACE,WAAA;AACA,eAAA;AACA,WAAA,KAAA;AACA,iBAAA,IAAA,MAAA;;AAEA,CAnBN,wBAmBM,SAAA,CANF,cAME,CANF,aAME;AACE,iBAAA;;AAGF,CAvBN,wBAuBM,SAAA,CAVF,cAUE,CAVF,cAUE;AACE,gBAAA;AACA,cAAA;AACA,aAAA;AACA,SAAA;AACA,UAAA;;AAGF,CA/BN,wBA+BM,SAAA,CAlBF,cAkBE,CAlBF,cAkBE,CAAA;AACE,QAAA;;AAEA,CAlCR,wBAkCQ,SAAA,CArBJ,cAqBI,CArBJ,cAqBI,CAHF,iBAGE,CAAA;AACE,eAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAxCR,wBAwCQ,SAAA,CA3BJ,cA2BI,CA3BJ,cA2BI,CATF,iBASE,CAAA;AACE,SAAA;AACA,aAAA;AACA,iBAAA;;AAGF,CA9CR,wBA8CQ,SAAA,CAjCJ,cAiCI,CAjCJ,cAiCI,CAfF,iBAeE,CAAA;AACE,SAAA;AACA,aAAA;;AAMR,CAtDF,wBAsDE,SAAA,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;;AAEA,CA3DJ,wBA2DI,SAAA,CALF,YAKE;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;;AAGF,CAnEJ,wBAmEI,SAAA,CAbF,YAaE;AACE,UAAA;AACA,aAAA;;AAOR,OAAA,CAAA,SAAA,EAAA;AAEI,GA3NJ,gBA2NI;AACE,eAAA;;AAGF,GA/NJ,gBA+NI;AACE,eAAA;;AAIJ,GA9MF;AA+MI,2BAAA;AACA,SAAA;;AAGF,GArHA;AAsHE,2BAAA;;AAEA,GAxHF,aAwHE;AACE,YAAA;AACA,eAAA;;;AAKN,OAAA,CAAA,SAAA,EAAA;AACE,GA1PF;AA2PI,aAAA,EAAA;;AAIA,GAzPJ,gBAyPI;AACE,eAAA;;AAOE,GApOR,eAoOQ,CA/LN,WA+LM,CA1LJ,UA0LI,CAvLF;AAwLI,eAAA;;AAGF,GAxOR,eAwOQ,CAnMN,WAmMM,CA9LJ,UA8LI,CAnLF;AAoLI,eAAA;;;AAQV,CAjPA;AAkPE,aAAA,SAAA,KAAA;;AAGF,WAHE;AAIA;AACE,aAAA;AACA,eAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA;;;AAKJ,CAjQA,cAiQA;AAA+B,mBAAA;;AAC/B,CAlQA,cAkQA;AAA+B,mBAAA;;AAC/B,CAnQA,cAmQA;AAA+B,mBAAA;;AAC/B,CApQA,cAoQA;AAA+B,mBAAA;;", "names": []}