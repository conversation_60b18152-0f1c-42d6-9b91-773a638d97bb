{"version": 3, "sources": ["src/app/features/auth/components/auth-layout/auth-layout.component.scss"], "sourcesContent": [".auth-container {\n  height: 100vh;\n  display: flex;\n  position: relative;\n  overflow: hidden;\n}\n\n.auth-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-image: \n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);\n  }\n}\n\n.auth-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.1);\n}\n\n.auth-content {\n  position: relative;\n  z-index: 1;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n}\n\n.auth-card-container {\n  width: 100%;\n  max-width: 400px;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 16px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  animation: slideUp 0.6s ease-out;\n}\n\n.brand-section {\n  text-align: center;\n  margin-bottom: 2rem;\n  \n  .brand-logo {\n    margin-bottom: 1rem;\n    \n    .brand-icon {\n      font-size: 48px;\n      width: 48px;\n      height: 48px;\n      color: #667eea;\n    }\n  }\n  \n  .brand-title {\n    font-size: 2rem;\n    font-weight: 700;\n    color: #333;\n    margin-bottom: 0.5rem;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  \n  .brand-subtitle {\n    color: #666;\n    font-size: 0.9rem;\n    margin: 0;\n  }\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// Responsive Design\n@media (max-width: 768px) {\n  .auth-content {\n    padding: 1rem;\n  }\n  \n  .auth-card-container {\n    padding: 1.5rem;\n    max-width: 100%;\n  }\n  \n  .brand-section {\n    .brand-title {\n      font-size: 1.75rem;\n    }\n    \n    .brand-subtitle {\n      font-size: 0.85rem;\n    }\n  }\n}\n\n@media (max-width: 480px) {\n  .auth-card-container {\n    padding: 1rem;\n    border-radius: 12px;\n  }\n  \n  .brand-section {\n    margin-bottom: 1.5rem;\n    \n    .brand-logo .brand-icon {\n      font-size: 40px;\n      width: 40px;\n      height: 40px;\n    }\n    \n    .brand-title {\n      font-size: 1.5rem;\n    }\n  }\n}"], "mappings": ";AAAA,CAAA;AACE,UAAA;AACA,WAAA;AACA,YAAA;AACA,YAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;;AAEA,CARF,eAQE;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA;IACE;MAAA,OAAA,GAAA,IAAA,GAAA;MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,KAAA,EAAA;MAAA,YAAA,IAAA;IAAA;MAAA,OAAA,GAAA,IAAA,GAAA;MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,KAAA,EAAA;MAAA,YAAA,IAAA;IAAA;MAAA,OAAA,GAAA,IAAA,GAAA;MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,KAAA,EAAA;MAAA,YAAA;;AAMN,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,YAAA;AACA,WAAA;AACA,SAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,2BAAA,KAAA;AAAA,mBAAA,KAAA;AACA,iBAAA;AACA,cAAA,EAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,aAAA,QAAA,KAAA;;AAGF,CAAA;AACE,cAAA;AACA,iBAAA;;AAEA,CAJF,cAIE,CAAA;AACE,iBAAA;;AAEA,CAPJ,cAOI,CAHF,WAGE,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;;AAIJ,CAfF,cAeE,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,2BAAA;AACA,2BAAA;AACA,mBAAA;;AAGF,CA1BF,cA0BE,CAAA;AACE,SAAA;AACA,aAAA;AACA,UAAA;;AAIJ,WApCE;AAqCA;AACE,aAAA;AACA,eAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA;;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GAnEF;AAoEI,aAAA;;AAGF,GA7DF;AA8DI,aAAA;AACA,eAAA;;AAIA,GAxDJ,cAwDI,CAzCF;AA0CI,eAAA;;AAGF,GA5DJ,cA4DI,CAlCF;AAmCI,eAAA;;;AAKN,OAAA,CAAA,SAAA,EAAA;AACE,GA9EF;AA+EI,aAAA;AACA,mBAAA;;AAGF,GAxEF;AAyEI,mBAAA;;AAEA,GA3EJ,cA2EI,CAvEF,WAuEE,CApEA;AAqEE,eAAA;AACA,WAAA;AACA,YAAA;;AAGF,GAjFJ,cAiFI,CAlEF;AAmEI,eAAA;;;", "names": []}