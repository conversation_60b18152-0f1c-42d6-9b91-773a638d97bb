{"version": 3, "sources": ["src/app/core/models/notification.models.ts"], "sourcesContent": ["import { BaseDto } from './workflow.models';\n\nexport enum NotificationType {\n  Info = 1,\n  Warning = 2,\n  Error = 3,\n  Success = 4,\n  RequestUpdate = 5,\n  WorkflowUpdate = 6\n}\n\nexport interface NotificationDto extends BaseDto {\n  userId: string;\n  title: string;\n  message: string;\n  isRead: boolean;\n  type: NotificationType;\n  actionUrl?: string;\n  relatedEntityId?: string;\n  relatedEntityType?: string;\n}\n\nexport interface CreateNotificationDto {\n  userId: string;\n  title: string;\n  message: string;\n  type: NotificationType;\n  actionUrl?: string;\n  relatedEntityId?: string;\n  relatedEntityType?: string;\n}\n\nexport interface MarkNotificationReadDto {\n  notificationId: string;\n}\n\nexport interface NotificationSummary {\n  totalNotifications: number;\n  unreadNotifications: number;\n  notificationsByType: { [key in NotificationType]: number };\n}"], "mappings": ";AAEA,IAAY;CAAZ,SAAYA,mBAAgB;AAC1B,EAAAA,kBAAAA,kBAAA,MAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,OAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,eAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,gBAAA,IAAA,CAAA,IAAA;AACF,GAPY,qBAAA,mBAAgB,CAAA,EAAA;", "names": ["NotificationType"]}