import {
  Mat<PERSON><PERSON><PERSON>oggle,
  MatSlideToggleModule
} from "./chunk-NAADBCNA.js";
import {
  MatPaginator,
  MatPaginatorModule
} from "./chunk-HF3CCJRO.js";
import {
  WorkflowService
} from "./chunk-VQU43TNE.js";
import {
  MatO<PERSON>,
  MatSelect,
  MatSelectModule
} from "./chunk-2ZKWC7Y6.js";
import "./chunk-H3UX3NVF.js";
import {
  MatTooltip,
  MatTooltipModule
} from "./chunk-F4VZNS7P.js";
import "./chunk-THNYDRFY.js";
import "./chunk-T4TA645T.js";
import {
  MatFormFieldModule,
  MatInput,
  MatInputModule
} from "./chunk-Z5T6RGZC.js";
import "./chunk-DQTTXTF2.js";
import {
  MatChip,
  MatChipsModule
} from "./chunk-KJM35DAP.js";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Mat<PERSON>uffix
} from "./chunk-PJGOPMTU.js";
import {
  <PERSON><PERSON><PERSON>V<PERSON>ueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel
} from "./chunk-4WCUDP7B.js";
import {
  MatProgressSpinner,
  MatProgressSpinnerModule
} from "./chunk-PIT7R6CL.js";
import {
  AuthService
} from "./chunk-EP3FZZM6.js";
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
  MatTableModule
} from "./chunk-Z6DK6RU5.js";
import "./chunk-I7LZP7WV.js";
import "./chunk-FTI6XIW5.js";
import "./chunk-R6R3KTSK.js";
import {
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle
} from "./chunk-QRMDFVVO.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-FSO3A3K2.js";
import {
  MatButton,
  MatButtonModule,
  MatIconButton
} from "./chunk-SVX3GQPM.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  Component,
  DatePipe,
  NgIf,
  Subject,
  __spreadProps,
  __spreadValues,
  debounceTime,
  distinctUntilChanged,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-XLTWONBW.js";

// src/app/features/workflows/components/workflow-list/workflow-list.component.ts
var _c0 = (a0) => ["/workflows/details", a0];
var _c1 = (a0) => ["/workflows/designer", a0];
var _c2 = () => [5, 10, 25, 50];
function WorkflowListComponent_div_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14);
    \u0275\u0275element(1, "mat-spinner");
    \u0275\u0275elementEnd();
  }
}
function WorkflowListComponent_div_31_th_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 28);
    \u0275\u0275text(1, "Name");
    \u0275\u0275elementEnd();
  }
}
function WorkflowListComponent_div_31_td_4_small_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "small");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const workflow_r1 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(workflow_r1.description);
  }
}
function WorkflowListComponent_div_31_td_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 29)(1, "div", 30)(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, WorkflowListComponent_div_31_td_4_small_4_Template, 2, 1, "small", 31);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const workflow_r1 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(workflow_r1.name);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", workflow_r1.description);
  }
}
function WorkflowListComponent_div_31_th_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 28);
    \u0275\u0275text(1, "Version");
    \u0275\u0275elementEnd();
  }
}
function WorkflowListComponent_div_31_td_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 29)(1, "mat-chip", 32);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const workflow_r2 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("v", workflow_r2.version, "");
  }
}
function WorkflowListComponent_div_31_th_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 28);
    \u0275\u0275text(1, "Steps");
    \u0275\u0275elementEnd();
  }
}
function WorkflowListComponent_div_31_td_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 29)(1, "span", 33);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const workflow_r3 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("", (workflow_r3.steps == null ? null : workflow_r3.steps.length) || 0, " steps");
  }
}
function WorkflowListComponent_div_31_th_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 28);
    \u0275\u0275text(1, "Status");
    \u0275\u0275elementEnd();
  }
}
function WorkflowListComponent_div_31_td_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 29)(1, "mat-chip");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const workflow_r4 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275classMap(workflow_r4.isActive ? "status-active" : "status-inactive");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", workflow_r4.isActive ? "Active" : "Inactive", " ");
  }
}
function WorkflowListComponent_div_31_th_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 28);
    \u0275\u0275text(1, "Created");
    \u0275\u0275elementEnd();
  }
}
function WorkflowListComponent_div_31_td_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 29);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "date");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const workflow_r5 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(2, 1, workflow_r5.createdAt, "short"), " ");
  }
}
function WorkflowListComponent_div_31_th_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 28);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function WorkflowListComponent_div_31_td_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "td", 29)(1, "div", 34)(2, "button", 35)(3, "mat-icon");
    \u0275\u0275text(4, "visibility");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "button", 36)(6, "mat-icon");
    \u0275\u0275text(7, "edit");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "mat-slide-toggle", 37);
    \u0275\u0275listener("change", function WorkflowListComponent_div_31_td_19_Template_mat_slide_toggle_change_8_listener() {
      const workflow_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r7 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r7.toggleWorkflowStatus(workflow_r7));
    });
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const workflow_r7 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(3, _c0, workflow_r7.id));
    \u0275\u0275advance(3);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(5, _c1, workflow_r7.id));
    \u0275\u0275advance(3);
    \u0275\u0275property("checked", workflow_r7.isActive);
  }
}
function WorkflowListComponent_div_31_tr_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 38);
  }
}
function WorkflowListComponent_div_31_tr_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 39);
  }
}
function WorkflowListComponent_div_31_div_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 40)(1, "mat-icon");
    \u0275\u0275text(2, "account_tree");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h3");
    \u0275\u0275text(4, "No workflows found");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "Create your first workflow to get started with process automation.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "button", 2);
    \u0275\u0275text(8, " Create Your First Workflow ");
    \u0275\u0275elementEnd()();
  }
}
function WorkflowListComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 15)(1, "table", 16);
    \u0275\u0275elementContainerStart(2, 17);
    \u0275\u0275template(3, WorkflowListComponent_div_31_th_3_Template, 2, 0, "th", 18)(4, WorkflowListComponent_div_31_td_4_Template, 5, 2, "td", 19);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(5, 20);
    \u0275\u0275template(6, WorkflowListComponent_div_31_th_6_Template, 2, 0, "th", 18)(7, WorkflowListComponent_div_31_td_7_Template, 3, 1, "td", 19);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(8, 21);
    \u0275\u0275template(9, WorkflowListComponent_div_31_th_9_Template, 2, 0, "th", 18)(10, WorkflowListComponent_div_31_td_10_Template, 3, 1, "td", 19);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(11, 22);
    \u0275\u0275template(12, WorkflowListComponent_div_31_th_12_Template, 2, 0, "th", 18)(13, WorkflowListComponent_div_31_td_13_Template, 3, 3, "td", 19);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(14, 23);
    \u0275\u0275template(15, WorkflowListComponent_div_31_th_15_Template, 2, 0, "th", 18)(16, WorkflowListComponent_div_31_td_16_Template, 3, 4, "td", 19);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(17, 24);
    \u0275\u0275template(18, WorkflowListComponent_div_31_th_18_Template, 2, 0, "th", 18)(19, WorkflowListComponent_div_31_td_19_Template, 9, 7, "td", 19);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(20, WorkflowListComponent_div_31_tr_20_Template, 1, 0, "tr", 25)(21, WorkflowListComponent_div_31_tr_21_Template, 1, 0, "tr", 26);
    \u0275\u0275elementEnd();
    \u0275\u0275template(22, WorkflowListComponent_div_31_div_22_Template, 9, 0, "div", 27);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r7 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("dataSource", ctx_r7.workflows);
    \u0275\u0275advance(19);
    \u0275\u0275property("matHeaderRowDef", ctx_r7.displayedColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r7.displayedColumns);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r7.workflows.length === 0);
  }
}
function WorkflowListComponent_mat_paginator_32_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-paginator", 41);
    \u0275\u0275listener("page", function WorkflowListComponent_mat_paginator_32_Template_mat_paginator_page_0_listener($event) {
      \u0275\u0275restoreView(_r9);
      const ctx_r7 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r7.onPageChange($event));
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r7 = \u0275\u0275nextContext();
    \u0275\u0275property("length", ctx_r7.totalCount)("pageSize", ctx_r7.pageSize)("pageSizeOptions", \u0275\u0275pureFunction0(4, _c2))("pageIndex", ctx_r7.currentPage - 1);
  }
}
var WorkflowListComponent = class _WorkflowListComponent {
  workflowService;
  authService;
  destroy$ = new Subject();
  searchSubject = new Subject();
  workflows = [];
  displayedColumns = ["name", "version", "steps", "status", "createdAt", "actions"];
  loading = false;
  // Pagination
  totalCount = 0;
  currentPage = 1;
  pageSize = 10;
  // Filters
  searchTerm = "";
  selectedStatus = "";
  constructor(workflowService, authService) {
    this.workflowService = workflowService;
    this.authService = authService;
    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(() => {
      this.currentPage = 1;
      this.loadWorkflows();
    });
  }
  ngOnInit() {
    this.loadWorkflows();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadWorkflows() {
    this.loading = true;
    const params = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm || void 0,
      sortBy: "createdAt",
      sortDirection: "desc"
    };
    this.workflowService.getWorkflows(params).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response) => {
        this.workflows = response.data;
        this.totalCount = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading workflows:", error);
        this.loading = false;
      }
    });
  }
  onSearchChange() {
    this.searchSubject.next(this.searchTerm);
  }
  onFilterChange() {
    this.currentPage = 1;
    this.loadWorkflows();
  }
  onPageChange(event) {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadWorkflows();
  }
  toggleWorkflowStatus(workflow) {
    const updatedWorkflow = __spreadProps(__spreadValues({}, workflow), {
      isActive: !workflow.isActive
    });
    this.workflowService.updateWorkflow(workflow.id, updatedWorkflow).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        workflow.isActive = !workflow.isActive;
      },
      error: (error) => {
        console.error("Error updating workflow status:", error);
      }
    });
  }
  static \u0275fac = function WorkflowListComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _WorkflowListComponent)(\u0275\u0275directiveInject(WorkflowService), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _WorkflowListComponent, selectors: [["app-workflow-list"]], decls: 33, vars: 5, consts: [[1, "workflow-list-container"], [1, "header-actions"], ["mat-raised-button", "", "color", "primary", "routerLink", "/workflows/designer"], [1, "filters"], ["appearance", "outline"], ["matInput", "", "placeholder", "Search workflows...", 3, "ngModelChange", "ngModel"], ["matSuffix", ""], [3, "ngModelChange", "selectionChange", "ngModel"], ["value", ""], ["value", "active"], ["value", "inactive"], ["class", "loading-container", 4, "ngIf"], ["class", "table-container", 4, "ngIf"], ["showFirstLastButtons", "", 3, "length", "pageSize", "pageSizeOptions", "pageIndex", "page", 4, "ngIf"], [1, "loading-container"], [1, "table-container"], ["mat-table", "", 1, "workflows-table", 3, "dataSource"], ["matColumnDef", "name"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "version"], ["matColumnDef", "steps"], ["matColumnDef", "status"], ["matColumnDef", "createdAt"], ["matColumnDef", "actions"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", "class", "workflow-row", 4, "matRowDef", "matRowDefColumns"], ["class", "no-data", 4, "ngIf"], ["mat-header-cell", ""], ["mat-cell", ""], [1, "workflow-name"], [4, "ngIf"], [1, "version-chip"], [1, "steps-count"], [1, "action-buttons"], ["mat-icon-button", "", "matTooltip", "View Details", 3, "routerLink"], ["mat-icon-button", "", "matTooltip", "Edit", 3, "routerLink"], ["matTooltip", "Toggle Active Status", 3, "change", "checked"], ["mat-header-row", ""], ["mat-row", "", 1, "workflow-row"], [1, "no-data"], ["showFirstLastButtons", "", 3, "page", "length", "pageSize", "pageSizeOptions", "pageIndex"]], template: function WorkflowListComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title")(4, "mat-icon");
      \u0275\u0275text(5, "account_tree");
      \u0275\u0275elementEnd();
      \u0275\u0275text(6, " Workflow Management ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "div", 1)(8, "button", 2)(9, "mat-icon");
      \u0275\u0275text(10, "add");
      \u0275\u0275elementEnd();
      \u0275\u0275text(11, " Create Workflow ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(12, "mat-card-content")(13, "div", 3)(14, "mat-form-field", 4)(15, "mat-label");
      \u0275\u0275text(16, "Search");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "input", 5);
      \u0275\u0275twoWayListener("ngModelChange", function WorkflowListComponent_Template_input_ngModelChange_17_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);
        return $event;
      });
      \u0275\u0275listener("ngModelChange", function WorkflowListComponent_Template_input_ngModelChange_17_listener() {
        return ctx.onSearchChange();
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "mat-icon", 6);
      \u0275\u0275text(19, "search");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(20, "mat-form-field", 4)(21, "mat-label");
      \u0275\u0275text(22, "Status");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "mat-select", 7);
      \u0275\u0275twoWayListener("ngModelChange", function WorkflowListComponent_Template_mat_select_ngModelChange_23_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedStatus, $event) || (ctx.selectedStatus = $event);
        return $event;
      });
      \u0275\u0275listener("selectionChange", function WorkflowListComponent_Template_mat_select_selectionChange_23_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(24, "mat-option", 8);
      \u0275\u0275text(25, "All");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "mat-option", 9);
      \u0275\u0275text(27, "Active");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "mat-option", 10);
      \u0275\u0275text(29, "Inactive");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(30, WorkflowListComponent_div_30_Template, 2, 0, "div", 11)(31, WorkflowListComponent_div_31_Template, 23, 4, "div", 12)(32, WorkflowListComponent_mat_paginator_32_Template, 1, 5, "mat-paginator", 13);
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(17);
      \u0275\u0275twoWayProperty("ngModel", ctx.searchTerm);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedStatus);
      \u0275\u0275advance(7);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.totalCount > 0);
    }
  }, dependencies: [CommonModule, NgIf, DatePipe, RouterModule, RouterLink, MatTableModule, MatTable, MatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatHeaderCell, MatCell, MatHeaderRow, MatRow, MatButtonModule, MatButton, MatIconButton, MatIconModule, MatIcon, MatCardModule, MatCard, MatCardContent, MatCardHeader, MatCardTitle, MatChipsModule, MatChip, MatPaginatorModule, MatPaginator, MatFormFieldModule, MatFormField, MatLabel, MatSuffix, MatInputModule, MatInput, MatSelectModule, MatSelect, MatOption, MatProgressSpinnerModule, MatProgressSpinner, MatTooltipModule, MatTooltip, MatSlideToggleModule, MatSlideToggle, FormsModule, DefaultValueAccessor, NgControlStatus, NgModel], styles: ["\n\n.workflow-list-container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\nmat-card-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.filters[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  flex-wrap: wrap;\n}\n.filters[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  min-width: 200px;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.table-container[_ngcontent-%COMP%] {\n  overflow-x: auto;\n}\n.workflows-table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.workflow-row[_ngcontent-%COMP%]:hover {\n  background-color: #f5f5f5;\n}\n.workflow-name[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  display: block;\n  font-size: 1rem;\n}\n.workflow-name[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.8rem;\n  display: block;\n  margin-top: 0.25rem;\n}\n.version-chip[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  font-size: 0.75rem;\n}\n.steps-count[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n}\n.status-active[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n}\n.status-inactive[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #4caf50;\n}\n@media (max-width: 768px) {\n  .filters[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .filters[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n    min-width: 100%;\n  }\n  .action-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 0.25rem;\n  }\n}\n/*# sourceMappingURL=workflow-list.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WorkflowListComponent, [{
    type: Component,
    args: [{ selector: "app-workflow-list", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatTableModule,
      MatButtonModule,
      MatIconModule,
      MatCardModule,
      MatChipsModule,
      MatPaginatorModule,
      MatFormFieldModule,
      MatInputModule,
      MatSelectModule,
      MatProgressSpinnerModule,
      MatTooltipModule,
      MatSlideToggleModule,
      FormsModule
    ], template: `
    <div class="workflow-list-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>account_tree</mat-icon>
            Workflow Management
          </mat-card-title>
          <div class="header-actions">
            <button mat-raised-button color="primary" routerLink="/workflows/designer">
              <mat-icon>add</mat-icon>
              Create Workflow
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <!-- Filters -->
          <div class="filters">
            <mat-form-field appearance="outline">
              <mat-label>Search</mat-label>
              <input matInput [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()" placeholder="Search workflows...">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select [(ngModel)]="selectedStatus" (selectionChange)="onFilterChange()">
                <mat-option value="">All</mat-option>
                <mat-option value="active">Active</mat-option>
                <mat-option value="inactive">Inactive</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Loading Spinner -->
          <div *ngIf="loading" class="loading-container">
            <mat-spinner></mat-spinner>
          </div>

          <!-- Workflows Table -->
          <div *ngIf="!loading" class="table-container">
            <table mat-table [dataSource]="workflows" class="workflows-table">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let workflow">
                  <div class="workflow-name">
                    <strong>{{workflow.name}}</strong>
                    <small *ngIf="workflow.description">{{workflow.description}}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Version Column -->
              <ng-container matColumnDef="version">
                <th mat-header-cell *matHeaderCellDef>Version</th>
                <td mat-cell *matCellDef="let workflow">
                  <mat-chip class="version-chip">v{{workflow.version}}</mat-chip>
                </td>
              </ng-container>

              <!-- Steps Column -->
              <ng-container matColumnDef="steps">
                <th mat-header-cell *matHeaderCellDef>Steps</th>
                <td mat-cell *matCellDef="let workflow">
                  <span class="steps-count">{{workflow.steps?.length || 0}} steps</span>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let workflow">
                  <mat-chip [class]="workflow.isActive ? 'status-active' : 'status-inactive'">
                    {{workflow.isActive ? 'Active' : 'Inactive'}}
                  </mat-chip>
                </td>
              </ng-container>

              <!-- Created Date Column -->
              <ng-container matColumnDef="createdAt">
                <th mat-header-cell *matHeaderCellDef>Created</th>
                <td mat-cell *matCellDef="let workflow">
                  {{workflow.createdAt | date:'short'}}
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let workflow">
                  <div class="action-buttons">
                    <button mat-icon-button [routerLink]="['/workflows/details', workflow.id]" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button [routerLink]="['/workflows/designer', workflow.id]" matTooltip="Edit">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <mat-slide-toggle 
                      [checked]="workflow.isActive" 
                      (change)="toggleWorkflowStatus(workflow)"
                      matTooltip="Toggle Active Status">
                    </mat-slide-toggle>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="workflow-row"></tr>
            </table>

            <!-- No Data Message -->
            <div *ngIf="workflows.length === 0" class="no-data">
              <mat-icon>account_tree</mat-icon>
              <h3>No workflows found</h3>
              <p>Create your first workflow to get started with process automation.</p>
              <button mat-raised-button color="primary" routerLink="/workflows/designer">
                Create Your First Workflow
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <mat-paginator
            *ngIf="!loading && totalCount > 0"
            [length]="totalCount"
            [pageSize]="pageSize"
            [pageSizeOptions]="[5, 10, 25, 50]"
            [pageIndex]="currentPage - 1"
            (page)="onPageChange($event)"
            showFirstLastButtons>
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;d3f140de93bc4deeb2f6922e0c4ad86f3bafea5d167076276b2e42accde8e6f9;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/workflows/components/workflow-list/workflow-list.component.ts */\n.workflow-list-container {\n  padding: 1rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\nmat-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.filters {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  flex-wrap: wrap;\n}\n.filters mat-form-field {\n  min-width: 200px;\n}\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.table-container {\n  overflow-x: auto;\n}\n.workflows-table {\n  width: 100%;\n}\n.workflow-row:hover {\n  background-color: #f5f5f5;\n}\n.workflow-name strong {\n  display: block;\n  font-size: 1rem;\n}\n.workflow-name small {\n  color: #666;\n  font-size: 0.8rem;\n  display: block;\n  margin-top: 0.25rem;\n}\n.version-chip {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  font-size: 0.75rem;\n}\n.steps-count {\n  color: #666;\n  font-size: 0.9rem;\n}\n.status-active {\n  background-color: #d4edda;\n  color: #155724;\n}\n.status-inactive {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data mat-icon {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #4caf50;\n}\n@media (max-width: 768px) {\n  .filters {\n    flex-direction: column;\n  }\n  .filters mat-form-field {\n    min-width: 100%;\n  }\n  .action-buttons {\n    flex-direction: column;\n    gap: 0.25rem;\n  }\n}\n/*# sourceMappingURL=workflow-list.component.css.map */\n"] }]
  }], () => [{ type: WorkflowService }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(WorkflowListComponent, { className: "WorkflowListComponent", filePath: "src/app/features/workflows/components/workflow-list/workflow-list.component.ts", lineNumber: 298 });
})();
export {
  WorkflowListComponent
};
//# sourceMappingURL=chunk-UHGNX2TT.js.map
