// src/app/core/models/request.models.ts
var RequestType;
(function(RequestType2) {
  RequestType2[RequestType2["Leave"] = 1] = "Leave";
  RequestType2[RequestType2["Expense"] = 2] = "Expense";
  RequestType2[RequestType2["Training"] = 3] = "Training";
  RequestType2[RequestType2["ITSupport"] = 4] = "ITSupport";
  RequestType2[RequestType2["ProfileUpdate"] = 5] = "ProfileUpdate";
})(RequestType || (RequestType = {}));
var RequestStatus;
(function(RequestStatus2) {
  RequestStatus2[RequestStatus2["Pending"] = 1] = "Pending";
  RequestStatus2[RequestStatus2["Approved"] = 2] = "Approved";
  RequestStatus2[RequestStatus2["Rejected"] = 3] = "Rejected";
  RequestStatus2[RequestStatus2["Archived"] = 4] = "Archived";
})(RequestStatus || (RequestStatus = {}));
var StepStatus;
(function(StepStatus2) {
  StepStatus2[StepStatus2["Pending"] = 1] = "Pending";
  StepStatus2[StepStatus2["Approved"] = 2] = "Approved";
  StepStatus2[StepStatus2["Rejected"] = 3] = "Rejected";
})(StepStatus || (StepStatus = {}));

export {
  RequestType,
  RequestStatus,
  StepStatus
};
//# sourceMappingURL=chunk-XJ5TS5V6.js.map
