{"version": 3, "sources": ["src/app/features/admin/components/system-settings/system-settings.component.ts"], "sourcesContent": ["\n    .system-settings-container {\n      padding: 1rem;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .tab-content {\n      padding: 1rem 0;\n    }\n\n    .settings-section {\n      margin-bottom: 2rem;\n    }\n\n    .settings-section h3 {\n      margin-bottom: 1rem;\n      color: #333;\n    }\n\n    .form-row {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .full-width {\n      width: 100%;\n    }\n\n    .toggle-section {\n      margin-bottom: 1rem;\n      padding: 1rem;\n      background-color: #f8f9fa;\n      border-radius: 8px;\n    }\n\n    .toggle-description {\n      margin: 0.5rem 0 0 0;\n      color: #666;\n      font-size: 0.9rem;\n    }\n\n    .actions {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 2rem;\n    }\n\n    .backup-info {\n      background-color: #f8f9fa;\n      padding: 1rem;\n      border-radius: 8px;\n      margin-bottom: 1rem;\n    }\n\n    .backup-info p {\n      margin: 0.5rem 0;\n    }\n\n    .backup-status {\n      padding: 2px 8px;\n      border-radius: 4px;\n      font-size: 0.8rem;\n      font-weight: bold;\n    }\n\n    .backup-status.success {\n      background-color: #d4edda;\n      color: #155724;\n    }\n\n    .backup-actions,\n    .maintenance-actions {\n      display: flex;\n      gap: 1rem;\n      flex-wrap: wrap;\n    }\n\n    @media (max-width: 768px) {\n      .form-row {\n        flex-direction: column;\n      }\n\n      .backup-actions,\n      .maintenance-actions {\n        flex-direction: column;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA,KAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAJA,iBAIA;AACE,iBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,iBAAA;AACA,WAAA;AACA,oBAAA;AACA,iBAAA;;AAGF,CAAA;AACE,UAAA,OAAA,EAAA,EAAA;AACA,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,cAAA;;AAGF,CAAA;AACE,oBAAA;AACA,WAAA;AACA,iBAAA;AACA,iBAAA;;AAGF,CAPA,YAOA;AACE,UAAA,OAAA;;AAGF,CAAA;AACE,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAPA,aAOA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AAAA,CAAA;AAEE,WAAA;AACA,OAAA;AACA,aAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GA5DF;AA6DI,oBAAA;;AAGF,GAZF;EAYE,CAZF;AAcI,oBAAA;;;", "names": []}