{"version": 3, "sources": ["src/app/features/dashboard/components/employee-dashboard/employee-dashboard.component.scss"], "sourcesContent": [".employee-dashboard {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.dashboard-header {\n  text-align: center;\n  margin-bottom: 2rem;\n  \n  h1 {\n    font-size: 2.5rem;\n    font-weight: 600;\n    color: #333;\n    margin-bottom: 0.5rem;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  \n  p {\n    font-size: 1.1rem;\n    color: #666;\n    margin: 0;\n  }\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n\n.stat-card {\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n  \n  mat-card-content {\n    display: flex;\n    align-items: center;\n    padding: 1.5rem;\n  }\n  \n  .stat-icon {\n    margin-right: 1rem;\n    \n    mat-icon {\n      font-size: 32px;\n      width: 32px;\n      height: 32px;\n    }\n  }\n  \n  .stat-info {\n    .stat-number {\n      font-size: 2rem;\n      font-weight: 700;\n      line-height: 1;\n      margin-bottom: 0.25rem;\n    }\n    \n    .stat-label {\n      font-size: 0.9rem;\n      color: #666;\n    }\n  }\n  \n  &.pending {\n    .stat-icon mat-icon {\n      color: #ff9800;\n    }\n    .stat-number {\n      color: #ff9800;\n    }\n  }\n  \n  &.approved {\n    .stat-icon mat-icon {\n      color: #4caf50;\n    }\n    .stat-number {\n      color: #4caf50;\n    }\n  }\n  \n  &.rejected {\n    .stat-icon mat-icon {\n      color: #f44336;\n    }\n    .stat-number {\n      color: #f44336;\n    }\n  }\n  \n  &.total {\n    .stat-icon mat-icon {\n      color: #667eea;\n    }\n    .stat-number {\n      color: #667eea;\n    }\n  }\n}\n\n.quick-actions {\n  margin-bottom: 3rem;\n  \n  h2 {\n    font-size: 1.5rem;\n    font-weight: 600;\n    color: #333;\n    margin-bottom: 1rem;\n  }\n  \n  .actions-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 1rem;\n    \n    button {\n      height: 60px;\n      font-size: 1rem;\n      font-weight: 500;\n      border-radius: 8px;\n      transition: all 0.3s ease;\n      \n      &:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n      }\n      \n      mat-icon {\n        margin-right: 0.5rem;\n      }\n    }\n  }\n}\n\n.recent-requests {\n  margin-bottom: 3rem;\n  \n  mat-card {\n    border-radius: 12px;\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    \n    mat-card-header {\n      mat-card-title {\n        font-size: 1.25rem;\n        font-weight: 600;\n        color: #333;\n      }\n      \n      mat-card-subtitle {\n        color: #666;\n      }\n    }\n    \n    .requests-table {\n      width: 100%;\n      \n      mat-table {\n        width: 100%;\n        \n        mat-header-cell {\n          font-weight: 600;\n          color: #333;\n        }\n        \n        mat-cell {\n          color: #666;\n        }\n        \n        mat-chip {\n          font-size: 0.75rem;\n          height: 24px;\n          line-height: 24px;\n        }\n      }\n    }\n    \n    .no-requests {\n      text-align: center;\n      padding: 3rem 1rem;\n      color: #666;\n      \n      mat-icon {\n        font-size: 64px;\n        width: 64px;\n        height: 64px;\n        margin-bottom: 1rem;\n        opacity: 0.5;\n      }\n      \n      h3 {\n        margin-bottom: 0.5rem;\n        color: #333;\n      }\n      \n      p {\n        margin-bottom: 1.5rem;\n      }\n    }\n  }\n}\n\n.charts-section {\n  mat-card {\n    border-radius: 12px;\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    \n    mat-card-header {\n      mat-card-title {\n        font-size: 1.25rem;\n        font-weight: 600;\n        color: #333;\n      }\n      \n      mat-card-subtitle {\n        color: #666;\n      }\n    }\n    \n    mat-card-content {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 300px;\n    }\n  }\n}\n\n// Responsive Design\n@media (max-width: 768px) {\n  .dashboard-header {\n    h1 {\n      font-size: 2rem;\n    }\n    \n    p {\n      font-size: 1rem;\n    }\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 1rem;\n  }\n  \n  .stat-card {\n    mat-card-content {\n      padding: 1rem;\n    }\n    \n    .stat-icon mat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n    \n    .stat-info {\n      .stat-number {\n        font-size: 1.5rem;\n      }\n      \n      .stat-label {\n        font-size: 0.8rem;\n      }\n    }\n  }\n  \n  .actions-grid {\n    grid-template-columns: 1fr;\n    \n    button {\n      height: 50px;\n      font-size: 0.9rem;\n    }\n  }\n}\n\n@media (max-width: 480px) {\n  .employee-dashboard {\n    padding: 0 0.5rem;\n  }\n  \n  .dashboard-header {\n    h1 {\n      font-size: 1.75rem;\n    }\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .recent-requests {\n    mat-card-content {\n      padding: 1rem;\n    }\n    \n    .requests-table {\n      overflow-x: auto;\n    }\n  }\n}\n\n// Animation for stats cards\n.stat-card {\n  animation: fadeInUp 0.6s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// Stagger animation for stats cards\n.stat-card:nth-child(1) { animation-delay: 0.1s; }\n.stat-card:nth-child(2) { animation-delay: 0.2s; }\n.stat-card:nth-child(3) { animation-delay: 0.3s; }\n.stat-card:nth-child(4) { animation-delay: 0.4s; }\n\n// Loading state\n.loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  \n  mat-spinner {\n    margin-right: 1rem;\n  }\n}"], "mappings": ";AAAA,CAAA;AACE,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,cAAA;AACA,iBAAA;;AAEA,CAJF,iBAIE;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,2BAAA;AACA,2BAAA;AACA,mBAAA;;AAGF,CAfF,iBAeE;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CALF,SAKE;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAVF,UAUE;AACE,WAAA;AACA,eAAA;AACA,WAAA;;AAGF,CAhBF,UAgBE,CAAA;AACE,gBAAA;;AAEA,CAnBJ,UAmBI,CAHF,UAGE;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAKF,CA3BJ,UA2BI,CAAA,UAAA,CAAA;AACE,aAAA;AACA,eAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAlCJ,UAkCI,CAPA,UAOA,CAAA;AACE,aAAA;AACA,SAAA;;AAKF,CAzCJ,SAyCI,CAAA,QAAA,CAzBF,UAyBE;AACE,SAAA;;AAEF,CA5CJ,SA4CI,CAHA,QAGA,CAjBA;AAkBE,SAAA;;AAKF,CAlDJ,SAkDI,CAAA,SAAA,CAlCF,UAkCE;AACE,SAAA;;AAEF,CArDJ,SAqDI,CAHA,SAGA,CA1BA;AA2BE,SAAA;;AAKF,CA3DJ,SA2DI,CAAA,SAAA,CA3CF,UA2CE;AACE,SAAA;;AAEF,CA9DJ,SA8DI,CAHA,SAGA,CAnCA;AAoCE,SAAA;;AAKF,CApEJ,SAoEI,CAAA,MAAA,CApDF,UAoDE;AACE,SAAA;;AAEF,CAvEJ,SAuEI,CAHA,MAGA,CA5CA;AA6CE,SAAA;;AAKN,CAAA;AACE,iBAAA;;AAEA,CAHF,cAGE;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAVF,cAUE,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAEA,CAfJ,cAeI,CALF,aAKE;AACE,UAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAtBN,cAsBM,CAZJ,aAYI,MAAA;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CA3BN,cA2BM,CAjBJ,aAiBI,OAAA;AACE,gBAAA;;AAMR,CAAA;AACE,iBAAA;;AAEA,CAHF,gBAGE;AACE,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGE,CARN,gBAQM,SAAA,gBAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAdN,gBAcM,SAAA,gBAAA;AACE,SAAA;;AAIJ,CAnBJ,gBAmBI,SAAA,CAAA;AACE,SAAA;;AAEA,CAtBN,gBAsBM,SAAA,CAHF,eAGE;AACE,SAAA;;AAEA,CAzBR,gBAyBQ,SAAA,CANJ,eAMI,UAAA;AACE,eAAA;AACA,SAAA;;AAGF,CA9BR,gBA8BQ,SAAA,CAXJ,eAWI,UAAA;AACE,SAAA;;AAGF,CAlCR,gBAkCQ,SAAA,CAfJ,eAeI,UAAA;AACE,aAAA;AACA,UAAA;AACA,eAAA;;AAKN,CA1CJ,gBA0CI,SAAA,CAAA;AACE,cAAA;AACA,WAAA,KAAA;AACA,SAAA;;AAEA,CA/CN,gBA+CM,SAAA,CALF,YAKE;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;;AAGF,CAvDN,gBAuDM,SAAA,CAbF,YAaE;AACE,iBAAA;AACA,SAAA;;AAGF,CA5DN,gBA4DM,SAAA,CAlBF,YAkBE;AACE,iBAAA;;AAON,CAAA,eAAA;AACE,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGE,CALJ,eAKI,SAAA,gBAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAXJ,eAWI,SAAA,gBAAA;AACE,SAAA;;AAIJ,CAhBF,eAgBE,SAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;;AAMN,OAAA,CAAA,SAAA,EAAA;AAEI,GA5OJ,iBA4OI;AACE,eAAA;;AAGF,GAhPJ,iBAgPI;AACE,eAAA;;AAIJ,GA/NF;AAgOI,2BAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,SAAA;;AAIA,GA9NJ,UA8NI;AACE,aAAA;;AAGF,GAlOJ,UAkOI,CAlNF,UAkNE;AACE,eAAA;AACA,WAAA;AACA,YAAA;;AAIA,GAzON,UAyOM,CA9MF,UA8ME,CA9MF;AA+MI,eAAA;;AAGF,GA7ON,UA6OM,CAlNF,UAkNE,CA3MF;AA4MI,eAAA;;AAKN,GA5JA;AA6JE,2BAAA;;AAEA,GA/JF,aA+JE;AACE,YAAA;AACA,eAAA;;;AAKN,OAAA,CAAA,SAAA,EAAA;AACE,GAhSF;AAiSI,aAAA,EAAA;;AAIA,GAhSJ,iBAgSI;AACE,eAAA;;AAIJ,GA/QF;AAgRI,2BAAA;;AAIA,GA9JJ,gBA8JI;AACE,aAAA;;AAGF,GAlKJ,gBAkKI,CA/IA;AAgJE,gBAAA;;;AAMN,CAxRA;AAyRE,aAAA,SAAA,KAAA;;AAGF,WAHE;AAIA;AACE,aAAA;AACA,eAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA;;;AAKJ,CAxSA,SAwSA;AAA0B,mBAAA;;AAC1B,CAzSA,SAySA;AAA0B,mBAAA;;AAC1B,CA1SA,SA0SA;AAA0B,mBAAA;;AAC1B,CA3SA,SA2SA;AAA0B,mBAAA;;AAG1B,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,UAAA;;AAEA,CANF,QAME;AACE,gBAAA;;", "names": []}