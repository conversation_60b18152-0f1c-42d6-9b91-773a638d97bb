{"version": 3, "sources": ["src/app/features/requests/components/request-form/request-form.component.scss"], "sourcesContent": [".request-form-container {\n  max-width: 600px;\n  margin: 20px auto;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n\n  h2 {\n    text-align: center;\n    color: #333;\n    margin-bottom: 20px;\n  }\n\n  .form-group {\n    margin-bottom: 15px;\n\n    label {\n      display: block;\n      margin-bottom: 5px;\n      font-weight: bold;\n      color: #555;\n    }\n\n    input[type=\"text\"],\n    input[type=\"number\"],\n    input[type=\"date\"],\n    textarea,\n    select {\n      width: 100%;\n      padding: 10px;\n      border: 1px solid #ccc;\n      border-radius: 4px;\n      box-sizing: border-box; /* Include padding and border in the element's total width and height */\n    }\n\n    textarea {\n      resize: vertical;\n      min-height: 80px;\n    }\n\n    .error-message {\n      color: #dc3545;\n      font-size: 0.875em;\n      margin-top: 5px;\n    }\n  }\n\n  button[type=\"submit\"] {\n    display: block;\n    width: 100%;\n    padding: 10px;\n    background-color: #007bff;\n    color: white;\n    border: none;\n    border-radius: 4px;\n    font-size: 16px;\n    cursor: pointer;\n    transition: background-color 0.3s ease;\n\n    &:disabled {\n      background-color: #cccccc;\n      cursor: not-allowed;\n    }\n\n    &:hover:not(:disabled) {\n      background-color: #0056b3;\n    }\n  }\n}\n"], "mappings": ";AAAA,CAAA;AACE,aAAA;AACA,UAAA,KAAA;AACA,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CARF,uBAQE;AACE,cAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAdF,uBAcE,CAAA;AACE,iBAAA;;AAEA,CAjBJ,uBAiBI,CAHF,WAGE;AACE,WAAA;AACA,iBAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAxBJ,uBAwBI,CAVF,WAUE,KAAA,CAAA;AAAA,CAxBJ,uBAwBI,CAVF,WAUE,KAAA,CAAA;AAAA,CAxBJ,uBAwBI,CAVF,WAUE,KAAA,CAAA;AAAA,CAxBJ,uBAwBI,CAVF,WAUE;AAAA,CAxBJ,uBAwBI,CAVF,WAUE;AAKE,SAAA;AACA,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,cAAA;;AAGF,CApCJ,uBAoCI,CAtBF,WAsBE;AACE,UAAA;AACA,cAAA;;AAGF,CAzCJ,uBAyCI,CA3BF,WA2BE,CAAA;AACE,SAAA;AACA,aAAA;AACA,cAAA;;AAIJ,CAhDF,uBAgDE,MAAA,CAAA;AACE,WAAA;AACA,SAAA;AACA,WAAA;AACA,oBAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,aAAA;AACA,UAAA;AACA,cAAA,iBAAA,KAAA;;AAEA,CA5DJ,uBA4DI,MAAA,CAAA,YAAA;AACE,oBAAA;AACA,UAAA;;AAGF,CAjEJ,uBAiEI,MAAA,CAAA,YAAA,MAAA,KAAA;AACE,oBAAA;;", "names": []}