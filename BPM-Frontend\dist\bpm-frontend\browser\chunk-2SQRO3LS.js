import {
  UserService
} from "./chunk-3JNBYCCA.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  MatDividerModule
} from "./chunk-M6SWN32G.js";
import {
  MatFormFieldModule,
  MatInput,
  MatInputModule
} from "./chunk-Z5T6RGZC.js";
import {
  MatSnackBar,
  MatSnackBarModule
} from "./chunk-4HGSRZQD.js";
import "./chunk-DQTTXTF2.js";
import {
  MatChip,
  MatChipSet,
  MatChipsModule
} from "./chunk-KJM35DAP.js";
import {
  MatError,
  MatFormField,
  MatLabel
} from "./chunk-PJGOPMTU.js";
import {
  Default<PERSON><PERSON>ue<PERSON>ccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-4WCUDP7B.js";
import {
  Mat<PERSON>rogress<PERSON>pinner,
  MatProgressSpinnerModule
} from "./chunk-PIT7R6CL.js";
import {
  AuthService
} from "./chunk-EP3FZZM6.js";
import {
  MatTab,
  MatTabGroup,
  MatTabsModule
} from "./chunk-ZR5MXJ33.js";
import "./chunk-FTI6XIW5.js";
import "./chunk-R6R3KTSK.js";
import {
  MatCard,
  MatCardActions,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardSubtitle,
  MatCardTitle
} from "./chunk-QRMDFVVO.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-FSO3A3K2.js";
import {
  MatButton,
  MatButtonModule
} from "./chunk-SVX3GQPM.js";
import "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  Subject,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-XLTWONBW.js";

// src/app/features/profile/components/user-profile/user-profile.component.ts
var _c0 = () => [];
function UserProfileComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4);
    \u0275\u0275element(1, "mat-spinner");
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Loading profile...");
    \u0275\u0275elementEnd()();
  }
}
function UserProfileComponent_div_7_mat_error_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " First name is required ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_error_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Last name is required ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_error_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Username is required ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_error_33_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Email is required ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_error_34_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Please enter a valid email ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_chip_44_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-chip");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const role_r3 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", role_r3, " ");
  }
}
function UserProfileComponent_div_7_span_49_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Update Profile");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_span_50_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Updating...");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_error_69_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Current password is required ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_error_74_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " New password is required ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_error_75_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Password must be at least 6 characters ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_error_80_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Please confirm your password ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_mat_error_81_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Passwords do not match ");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_span_86_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Change Password");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_span_87_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Changing...");
    \u0275\u0275elementEnd();
  }
}
function UserProfileComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 5)(1, "mat-tab-group")(2, "mat-tab", 6)(3, "div", 7)(4, "mat-card")(5, "mat-card-header")(6, "mat-card-title");
    \u0275\u0275text(7, "Personal Details");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "mat-card-subtitle");
    \u0275\u0275text(9, "Update your personal information");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(10, "mat-card-content")(11, "form", 8);
    \u0275\u0275listener("ngSubmit", function UserProfileComponent_div_7_Template_form_ngSubmit_11_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.updateProfile());
    });
    \u0275\u0275elementStart(12, "div", 9)(13, "mat-form-field", 10)(14, "mat-label");
    \u0275\u0275text(15, "First Name");
    \u0275\u0275elementEnd();
    \u0275\u0275element(16, "input", 11);
    \u0275\u0275template(17, UserProfileComponent_div_7_mat_error_17_Template, 2, 0, "mat-error", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "mat-form-field", 10)(19, "mat-label");
    \u0275\u0275text(20, "Last Name");
    \u0275\u0275elementEnd();
    \u0275\u0275element(21, "input", 13);
    \u0275\u0275template(22, UserProfileComponent_div_7_mat_error_22_Template, 2, 0, "mat-error", 12);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(23, "div", 9)(24, "mat-form-field", 10)(25, "mat-label");
    \u0275\u0275text(26, "Username");
    \u0275\u0275elementEnd();
    \u0275\u0275element(27, "input", 14);
    \u0275\u0275template(28, UserProfileComponent_div_7_mat_error_28_Template, 2, 0, "mat-error", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(29, "mat-form-field", 10)(30, "mat-label");
    \u0275\u0275text(31, "Email");
    \u0275\u0275elementEnd();
    \u0275\u0275element(32, "input", 15);
    \u0275\u0275template(33, UserProfileComponent_div_7_mat_error_33_Template, 2, 0, "mat-error", 12)(34, UserProfileComponent_div_7_mat_error_34_Template, 2, 0, "mat-error", 12);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(35, "div", 9)(36, "mat-form-field", 10)(37, "mat-label");
    \u0275\u0275text(38, "Phone Number");
    \u0275\u0275elementEnd();
    \u0275\u0275element(39, "input", 16);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(40, "div", 17)(41, "h3");
    \u0275\u0275text(42, "Your Roles");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(43, "mat-chip-set");
    \u0275\u0275template(44, UserProfileComponent_div_7_mat_chip_44_Template, 2, 1, "mat-chip", 18);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(45, "mat-card-actions")(46, "button", 19)(47, "mat-icon");
    \u0275\u0275text(48, "save");
    \u0275\u0275elementEnd();
    \u0275\u0275template(49, UserProfileComponent_div_7_span_49_Template, 2, 0, "span", 12)(50, UserProfileComponent_div_7_span_50_Template, 2, 0, "span", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(51, "button", 20);
    \u0275\u0275listener("click", function UserProfileComponent_div_7_Template_button_click_51_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.resetForm());
    });
    \u0275\u0275elementStart(52, "mat-icon");
    \u0275\u0275text(53, "refresh");
    \u0275\u0275elementEnd();
    \u0275\u0275text(54, " Reset ");
    \u0275\u0275elementEnd()()()()()()();
    \u0275\u0275elementStart(55, "mat-tab", 21)(56, "div", 7)(57, "mat-card")(58, "mat-card-header")(59, "mat-card-title");
    \u0275\u0275text(60, "Change Password");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(61, "mat-card-subtitle");
    \u0275\u0275text(62, "Update your account password");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(63, "mat-card-content")(64, "form", 8);
    \u0275\u0275listener("ngSubmit", function UserProfileComponent_div_7_Template_form_ngSubmit_64_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.changePassword());
    });
    \u0275\u0275elementStart(65, "mat-form-field", 10)(66, "mat-label");
    \u0275\u0275text(67, "Current Password");
    \u0275\u0275elementEnd();
    \u0275\u0275element(68, "input", 22);
    \u0275\u0275template(69, UserProfileComponent_div_7_mat_error_69_Template, 2, 0, "mat-error", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(70, "mat-form-field", 10)(71, "mat-label");
    \u0275\u0275text(72, "New Password");
    \u0275\u0275elementEnd();
    \u0275\u0275element(73, "input", 23);
    \u0275\u0275template(74, UserProfileComponent_div_7_mat_error_74_Template, 2, 0, "mat-error", 12)(75, UserProfileComponent_div_7_mat_error_75_Template, 2, 0, "mat-error", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(76, "mat-form-field", 10)(77, "mat-label");
    \u0275\u0275text(78, "Confirm New Password");
    \u0275\u0275elementEnd();
    \u0275\u0275element(79, "input", 24);
    \u0275\u0275template(80, UserProfileComponent_div_7_mat_error_80_Template, 2, 0, "mat-error", 12)(81, UserProfileComponent_div_7_mat_error_81_Template, 2, 0, "mat-error", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(82, "mat-card-actions")(83, "button", 19)(84, "mat-icon");
    \u0275\u0275text(85, "lock");
    \u0275\u0275elementEnd();
    \u0275\u0275template(86, UserProfileComponent_div_7_span_86_Template, 2, 0, "span", 12)(87, UserProfileComponent_div_7_span_87_Template, 2, 0, "span", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(88, "button", 20);
    \u0275\u0275listener("click", function UserProfileComponent_div_7_Template_button_click_88_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.resetPasswordForm());
    });
    \u0275\u0275elementStart(89, "mat-icon");
    \u0275\u0275text(90, "refresh");
    \u0275\u0275elementEnd();
    \u0275\u0275text(91, " Reset ");
    \u0275\u0275elementEnd()()()()()()();
    \u0275\u0275elementStart(92, "mat-tab", 25)(93, "div", 7)(94, "mat-card")(95, "mat-card-header")(96, "mat-card-title");
    \u0275\u0275text(97, "Account Details");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(98, "mat-card-subtitle");
    \u0275\u0275text(99, "View your account information");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(100, "mat-card-content")(101, "div", 26)(102, "div", 27)(103, "strong");
    \u0275\u0275text(104, "User ID:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(105, "span");
    \u0275\u0275text(106);
    \u0275\u0275elementEnd()();
    \u0275\u0275element(107, "mat-divider");
    \u0275\u0275elementStart(108, "div", 27)(109, "strong");
    \u0275\u0275text(110, "Username:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(111, "span");
    \u0275\u0275text(112);
    \u0275\u0275elementEnd()();
    \u0275\u0275element(113, "mat-divider");
    \u0275\u0275elementStart(114, "div", 27)(115, "strong");
    \u0275\u0275text(116, "Email:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(117, "span");
    \u0275\u0275text(118);
    \u0275\u0275elementEnd()();
    \u0275\u0275element(119, "mat-divider");
    \u0275\u0275elementStart(120, "div", 27)(121, "strong");
    \u0275\u0275text(122, "Roles:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(123, "span");
    \u0275\u0275text(124);
    \u0275\u0275elementEnd()();
    \u0275\u0275element(125, "mat-divider");
    \u0275\u0275elementStart(126, "div", 27)(127, "strong");
    \u0275\u0275text(128, "Account Status:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(129, "span", 28);
    \u0275\u0275text(130, "Active");
    \u0275\u0275elementEnd()()()()()()()()();
  }
  if (rf & 2) {
    let tmp_2_0;
    let tmp_3_0;
    let tmp_4_0;
    let tmp_5_0;
    let tmp_6_0;
    let tmp_12_0;
    let tmp_13_0;
    let tmp_14_0;
    let tmp_15_0;
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(11);
    \u0275\u0275property("formGroup", ctx_r1.profileForm);
    \u0275\u0275advance(6);
    \u0275\u0275property("ngIf", (tmp_2_0 = ctx_r1.profileForm.get("firstName")) == null ? null : tmp_2_0.hasError("required"));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", (tmp_3_0 = ctx_r1.profileForm.get("lastName")) == null ? null : tmp_3_0.hasError("required"));
    \u0275\u0275advance(6);
    \u0275\u0275property("ngIf", (tmp_4_0 = ctx_r1.profileForm.get("userName")) == null ? null : tmp_4_0.hasError("required"));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", (tmp_5_0 = ctx_r1.profileForm.get("email")) == null ? null : tmp_5_0.hasError("required"));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_6_0 = ctx_r1.profileForm.get("email")) == null ? null : tmp_6_0.hasError("email"));
    \u0275\u0275advance(10);
    \u0275\u0275property("ngForOf", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.Roles) || (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.roles) || \u0275\u0275pureFunction0(23, _c0));
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r1.profileForm.invalid || ctx_r1.isUpdating);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", !ctx_r1.isUpdating);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isUpdating);
    \u0275\u0275advance(14);
    \u0275\u0275property("formGroup", ctx_r1.passwordForm);
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", (tmp_12_0 = ctx_r1.passwordForm.get("currentPassword")) == null ? null : tmp_12_0.hasError("required"));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", (tmp_13_0 = ctx_r1.passwordForm.get("newPassword")) == null ? null : tmp_13_0.hasError("required"));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_14_0 = ctx_r1.passwordForm.get("newPassword")) == null ? null : tmp_14_0.hasError("minlength"));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", (tmp_15_0 = ctx_r1.passwordForm.get("confirmPassword")) == null ? null : tmp_15_0.hasError("required"));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.passwordForm.hasError("passwordMismatch"));
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r1.passwordForm.invalid || ctx_r1.isChangingPassword);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", !ctx_r1.isChangingPassword);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isChangingPassword);
    \u0275\u0275advance(19);
    \u0275\u0275textInterpolate((ctx_r1.currentUser == null ? null : ctx_r1.currentUser.Id) || (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.id));
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate((ctx_r1.currentUser == null ? null : ctx_r1.currentUser.UserName) || (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.userName));
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate((ctx_r1.currentUser == null ? null : ctx_r1.currentUser.Email) || (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.email));
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(((ctx_r1.currentUser == null ? null : ctx_r1.currentUser.Roles) || (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.roles) || \u0275\u0275pureFunction0(24, _c0)).join(", "));
  }
}
var UserProfileComponent = class _UserProfileComponent {
  fb;
  authService;
  userService;
  snackBar;
  destroy$ = new Subject();
  profileForm;
  passwordForm;
  currentUser = null;
  isLoading = false;
  isUpdating = false;
  isChangingPassword = false;
  constructor(fb, authService, userService, snackBar) {
    this.fb = fb;
    this.authService = authService;
    this.userService = userService;
    this.snackBar = snackBar;
    this.initializeForms();
  }
  ngOnInit() {
    this.loadUserProfile();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  initializeForms() {
    this.profileForm = this.fb.group({
      firstName: ["", Validators.required],
      lastName: ["", Validators.required],
      userName: ["", Validators.required],
      email: ["", [Validators.required, Validators.email]],
      phoneNumber: [""]
    });
    this.passwordForm = this.fb.group({
      currentPassword: ["", Validators.required],
      newPassword: ["", [Validators.required, Validators.minLength(6)]],
      confirmPassword: ["", Validators.required]
    }, { validators: this.passwordMatchValidator });
  }
  passwordMatchValidator(form) {
    const newPassword = form.get("newPassword");
    const confirmPassword = form.get("confirmPassword");
    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }
  loadUserProfile() {
    this.isLoading = true;
    this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe({
      next: (user) => {
        this.currentUser = user;
        if (user) {
          this.profileForm.patchValue({
            firstName: user.FirstName || user.firstName || "",
            lastName: user.LastName || user.lastName || "",
            userName: user.UserName || user.userName || "",
            email: user.Email || user.email || "",
            phoneNumber: user.PhoneNumber || user.phoneNumber || ""
          });
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error("Error loading user profile:", error);
        this.snackBar.open("Error loading profile", "Close", { duration: 3e3 });
        this.isLoading = false;
      }
    });
  }
  updateProfile() {
    if (this.profileForm.valid && this.currentUser) {
      this.isUpdating = true;
      const updateData = this.profileForm.value;
      this.userService.updateProfile(updateData).pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          this.snackBar.open("Profile updated successfully", "Close", {
            duration: 3e3,
            panelClass: ["success-snackbar"]
          });
          this.isUpdating = false;
        },
        error: (error) => {
          console.error("Error updating profile:", error);
          if (error.status === 405) {
            this.snackBar.open("Profile update is not available yet. Please contact your administrator.", "Close", {
              duration: 5e3,
              panelClass: ["error-snackbar"]
            });
          } else if (error.status === 403) {
            this.snackBar.open("You do not have permission to update this profile.", "Close", {
              duration: 5e3,
              panelClass: ["error-snackbar"]
            });
          } else {
            this.snackBar.open("Error updating profile. Please try again later.", "Close", {
              duration: 5e3,
              panelClass: ["error-snackbar"]
            });
          }
          this.isUpdating = false;
        }
      });
    }
  }
  changePassword() {
    if (this.passwordForm.valid && this.currentUser) {
      this.isChangingPassword = true;
      const passwordData = {
        currentPassword: this.passwordForm.value.currentPassword,
        newPassword: this.passwordForm.value.newPassword,
        confirmPassword: this.passwordForm.value.confirmPassword
      };
      this.authService.changePassword(passwordData).pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          this.snackBar.open("Password changed successfully", "Close", { duration: 3e3 });
          this.resetPasswordForm();
          this.isChangingPassword = false;
        },
        error: (error) => {
          console.error("Error changing password:", error);
          this.snackBar.open("Error changing password", "Close", { duration: 3e3 });
          this.isChangingPassword = false;
        }
      });
    }
  }
  resetForm() {
    this.loadUserProfile();
  }
  resetPasswordForm() {
    this.passwordForm.reset();
  }
  static \u0275fac = function UserProfileComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UserProfileComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(UserService), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _UserProfileComponent, selectors: [["app-user-profile"]], decls: 8, vars: 2, consts: [[1, "profile-container"], [1, "profile-header"], ["class", "loading-container", 4, "ngIf"], ["class", "profile-content", 4, "ngIf"], [1, "loading-container"], [1, "profile-content"], ["label", "Personal Information"], [1, "tab-content"], [3, "ngSubmit", "formGroup"], [1, "form-row"], ["appearance", "outline"], ["matInput", "", "formControlName", "firstName", "placeholder", "Enter your first name"], [4, "ngIf"], ["matInput", "", "formControlName", "lastName", "placeholder", "Enter your last name"], ["matInput", "", "formControlName", "userName", "placeholder", "Enter your username"], ["matInput", "", "type", "email", "formControlName", "email", "placeholder", "Enter your email"], ["matInput", "", "formControlName", "phoneNumber", "placeholder", "Enter your phone number"], [1, "user-roles"], [4, "ngFor", "ngForOf"], ["mat-raised-button", "", "color", "primary", "type", "submit", 3, "disabled"], ["mat-button", "", "type", "button", 3, "click"], ["label", "Change Password"], ["matInput", "", "type", "password", "formControlName", "currentPassword", "placeholder", "Enter current password"], ["matInput", "", "type", "password", "formControlName", "newPassword", "placeholder", "Enter new password"], ["matInput", "", "type", "password", "formControlName", "confirmPassword", "placeholder", "Confirm new password"], ["label", "Account Information"], [1, "account-info"], [1, "info-item"], [1, "status-active"]], template: function UserProfileComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "User Profile");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "Manage your account information and preferences");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(6, UserProfileComponent_div_6_Template, 4, 0, "div", 2)(7, UserProfileComponent_div_7_Template, 131, 25, "div", 3);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(6);
      \u0275\u0275property("ngIf", ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading);
    }
  }, dependencies: [
    CommonModule,
    NgForOf,
    NgIf,
    ReactiveFormsModule,
    \u0275NgNoValidate,
    DefaultValueAccessor,
    NgControlStatus,
    NgControlStatusGroup,
    FormGroupDirective,
    FormControlName,
    MatCardModule,
    MatCard,
    MatCardActions,
    MatCardContent,
    MatCardHeader,
    MatCardSubtitle,
    MatCardTitle,
    MatFormFieldModule,
    MatFormField,
    MatLabel,
    MatError,
    MatInputModule,
    MatInput,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon,
    MatTabsModule,
    MatTab,
    MatTabGroup,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatProgressSpinner,
    MatChipsModule,
    MatChip,
    MatChipSet,
    MatDividerModule,
    MatDivider
  ], styles: ["\n\n.profile-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.profile-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n  text-align: center;\n}\n.profile-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  color: #1976d2;\n  margin-bottom: 0.5rem;\n}\n.profile-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 1.1rem;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem;\n}\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  color: #666;\n}\n.profile-content[_ngcontent-%COMP%]   .mat-tab-group[_ngcontent-%COMP%]   .mat-tab-body-content[_ngcontent-%COMP%] {\n  padding: 0;\n}\n.tab-content[_ngcontent-%COMP%] {\n  padding: 2rem;\n}\n.form-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n.form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.user-roles[_ngcontent-%COMP%] {\n  margin: 2rem 0;\n}\n.user-roles[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1rem;\n}\n.user-roles[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%] {\n  margin-right: 0.5rem;\n}\n.account-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 0;\n}\n.account-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: #333;\n}\n.account-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  color: #666;\n}\n.account-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .status-active[_ngcontent-%COMP%] {\n  color: #4caf50;\n  font-weight: 500;\n}\nmat-card-actions[_ngcontent-%COMP%] {\n  padding-top: 1rem;\n}\nmat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  margin-right: 1rem;\n}\nmat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  margin-right: 0.5rem;\n}\n@media (max-width: 768px) {\n  .profile-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .form-row[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 0;\n  }\n  .tab-content[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n}\n/*# sourceMappingURL=user-profile.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UserProfileComponent, [{
    type: Component,
    args: [{ selector: "app-user-profile", standalone: true, imports: [
      CommonModule,
      ReactiveFormsModule,
      MatCardModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatIconModule,
      MatTabsModule,
      MatSnackBarModule,
      MatProgressSpinnerModule,
      MatChipsModule,
      MatDividerModule
    ], template: `
    <div class="profile-container">
      <div class="profile-header">
        <h1>User Profile</h1>
        <p>Manage your account information and preferences</p>
      </div>

      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading profile...</p>
      </div>

      <div *ngIf="!isLoading" class="profile-content">
        <mat-tab-group>
          <!-- Personal Information Tab -->
          <mat-tab label="Personal Information">
            <div class="tab-content">
              <mat-card>
                <mat-card-header>
                  <mat-card-title>Personal Details</mat-card-title>
                  <mat-card-subtitle>Update your personal information</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <form [formGroup]="profileForm" (ngSubmit)="updateProfile()">
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>First Name</mat-label>
                        <input matInput formControlName="firstName" placeholder="Enter your first name">
                        <mat-error *ngIf="profileForm.get('firstName')?.hasError('required')">
                          First name is required
                        </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Last Name</mat-label>
                        <input matInput formControlName="lastName" placeholder="Enter your last name">
                        <mat-error *ngIf="profileForm.get('lastName')?.hasError('required')">
                          Last name is required
                        </mat-error>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Username</mat-label>
                        <input matInput formControlName="userName" placeholder="Enter your username">
                        <mat-error *ngIf="profileForm.get('userName')?.hasError('required')">
                          Username is required
                        </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Email</mat-label>
                        <input matInput type="email" formControlName="email" placeholder="Enter your email">
                        <mat-error *ngIf="profileForm.get('email')?.hasError('required')">
                          Email is required
                        </mat-error>
                        <mat-error *ngIf="profileForm.get('email')?.hasError('email')">
                          Please enter a valid email
                        </mat-error>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Phone Number</mat-label>
                        <input matInput formControlName="phoneNumber" placeholder="Enter your phone number">
                      </mat-form-field>
                    </div>

                    <div class="user-roles">
                      <h3>Your Roles</h3>
                      <mat-chip-set>
                        <mat-chip *ngFor="let role of currentUser?.Roles || currentUser?.roles || []">
                          {{role}}
                        </mat-chip>
                      </mat-chip-set>
                    </div>

                    <mat-card-actions>
                      <button mat-raised-button color="primary" type="submit" [disabled]="profileForm.invalid || isUpdating">
                        <mat-icon>save</mat-icon>
                        <span *ngIf="!isUpdating">Update Profile</span>
                        <span *ngIf="isUpdating">Updating...</span>
                      </button>
                      <button mat-button type="button" (click)="resetForm()">
                        <mat-icon>refresh</mat-icon>
                        Reset
                      </button>
                    </mat-card-actions>
                  </form>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>

          <!-- Change Password Tab -->
          <mat-tab label="Change Password">
            <div class="tab-content">
              <mat-card>
                <mat-card-header>
                  <mat-card-title>Change Password</mat-card-title>
                  <mat-card-subtitle>Update your account password</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <form [formGroup]="passwordForm" (ngSubmit)="changePassword()">
                    <mat-form-field appearance="outline">
                      <mat-label>Current Password</mat-label>
                      <input matInput type="password" formControlName="currentPassword" placeholder="Enter current password">
                      <mat-error *ngIf="passwordForm.get('currentPassword')?.hasError('required')">
                        Current password is required
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>New Password</mat-label>
                      <input matInput type="password" formControlName="newPassword" placeholder="Enter new password">
                      <mat-error *ngIf="passwordForm.get('newPassword')?.hasError('required')">
                        New password is required
                      </mat-error>
                      <mat-error *ngIf="passwordForm.get('newPassword')?.hasError('minlength')">
                        Password must be at least 6 characters
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Confirm New Password</mat-label>
                      <input matInput type="password" formControlName="confirmPassword" placeholder="Confirm new password">
                      <mat-error *ngIf="passwordForm.get('confirmPassword')?.hasError('required')">
                        Please confirm your password
                      </mat-error>
                      <mat-error *ngIf="passwordForm.hasError('passwordMismatch')">
                        Passwords do not match
                      </mat-error>
                    </mat-form-field>

                    <mat-card-actions>
                      <button mat-raised-button color="primary" type="submit" [disabled]="passwordForm.invalid || isChangingPassword">
                        <mat-icon>lock</mat-icon>
                        <span *ngIf="!isChangingPassword">Change Password</span>
                        <span *ngIf="isChangingPassword">Changing...</span>
                      </button>
                      <button mat-button type="button" (click)="resetPasswordForm()">
                        <mat-icon>refresh</mat-icon>
                        Reset
                      </button>
                    </mat-card-actions>
                  </form>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>

          <!-- Account Information Tab -->
          <mat-tab label="Account Information">
            <div class="tab-content">
              <mat-card>
                <mat-card-header>
                  <mat-card-title>Account Details</mat-card-title>
                  <mat-card-subtitle>View your account information</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="account-info">
                    <div class="info-item">
                      <strong>User ID:</strong>
                      <span>{{currentUser?.Id || currentUser?.id}}</span>
                    </div>
                    <mat-divider></mat-divider>
                    
                    <div class="info-item">
                      <strong>Username:</strong>
                      <span>{{currentUser?.UserName || currentUser?.userName}}</span>
                    </div>
                    <mat-divider></mat-divider>
                    
                    <div class="info-item">
                      <strong>Email:</strong>
                      <span>{{currentUser?.Email || currentUser?.email}}</span>
                    </div>
                    <mat-divider></mat-divider>
                    
                    <div class="info-item">
                      <strong>Roles:</strong>
                      <span>{{(currentUser?.Roles || currentUser?.roles || []).join(', ')}}</span>
                    </div>
                    <mat-divider></mat-divider>
                    
                    <div class="info-item">
                      <strong>Account Status:</strong>
                      <span class="status-active">Active</span>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;67dfd0f669ca30712f702ffb7ffb3b56c898c92a7d036bc1fff195baca60660c;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/profile/components/user-profile/user-profile.component.ts */\n.profile-container {\n  padding: 2rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.profile-header {\n  margin-bottom: 2rem;\n  text-align: center;\n}\n.profile-header h1 {\n  color: #1976d2;\n  margin-bottom: 0.5rem;\n}\n.profile-header p {\n  color: #666;\n  font-size: 1.1rem;\n}\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem;\n}\n.loading-container p {\n  margin-top: 1rem;\n  color: #666;\n}\n.profile-content .mat-tab-group .mat-tab-body-content {\n  padding: 0;\n}\n.tab-content {\n  padding: 2rem;\n}\n.form-row {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n.form-row mat-form-field {\n  flex: 1;\n}\n.user-roles {\n  margin: 2rem 0;\n}\n.user-roles h3 {\n  color: #333;\n  margin-bottom: 1rem;\n}\n.user-roles mat-chip {\n  margin-right: 0.5rem;\n}\n.account-info .info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 0;\n}\n.account-info .info-item strong {\n  color: #333;\n}\n.account-info .info-item span {\n  color: #666;\n}\n.account-info .info-item .status-active {\n  color: #4caf50;\n  font-weight: 500;\n}\nmat-card-actions {\n  padding-top: 1rem;\n}\nmat-card-actions button {\n  margin-right: 1rem;\n}\nmat-card-actions button mat-icon {\n  margin-right: 0.5rem;\n}\n@media (max-width: 768px) {\n  .profile-container {\n    padding: 1rem;\n  }\n  .form-row {\n    flex-direction: column;\n    gap: 0;\n  }\n  .tab-content {\n    padding: 1rem;\n  }\n}\n/*# sourceMappingURL=user-profile.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: AuthService }, { type: UserService }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(UserProfileComponent, { className: "UserProfileComponent", filePath: "src/app/features/profile/components/user-profile/user-profile.component.ts", lineNumber: 357 });
})();
export {
  UserProfileComponent
};
//# sourceMappingURL=chunk-2SQRO3LS.js.map
