{"version": 3, "sources": ["src/app/features/admin/components/role-management/role-management.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\n\ninterface Role {\n  id: string;\n  name: string;\n  description: string;\n  permissions: string[];\n  userCount: number;\n}\n\n@Component({\n  selector: 'app-role-management',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTableModule,\n    MatChipsModule,\n    MatSnackBarModule\n  ],\n  template: `\n    <div class=\"role-management-container\">\n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>admin_panel_settings</mat-icon>\n            Role Management\n          </mat-card-title>\n          <div class=\"header-actions\">\n            <button mat-raised-button color=\"primary\" (click)=\"createRole()\">\n              <mat-icon>add</mat-icon>\n              Create Role\n            </button>\n          </div>\n        </mat-card-header>\n\n        <mat-card-content>\n          <div class=\"table-container\">\n            <table mat-table [dataSource]=\"roles\" class=\"roles-table\">\n              <!-- Name Column -->\n              <ng-container matColumnDef=\"name\">\n                <th mat-header-cell *matHeaderCellDef>Role Name</th>\n                <td mat-cell *matCellDef=\"let role\">\n                  <div class=\"role-info\">\n                    <strong>{{role.name}}</strong>\n                    <small>{{role.description}}</small>\n                  </div>\n                </td>\n              </ng-container>\n\n              <!-- Permissions Column -->\n              <ng-container matColumnDef=\"permissions\">\n                <th mat-header-cell *matHeaderCellDef>Permissions</th>\n                <td mat-cell *matCellDef=\"let role\">\n                  <div class=\"permissions-container\">\n                    <mat-chip *ngFor=\"let permission of role.permissions.slice(0, 3)\" class=\"permission-chip\">\n                      {{permission}}\n                    </mat-chip>\n                    <span *ngIf=\"role.permissions.length > 3\" class=\"more-permissions\">\n                      +{{role.permissions.length - 3}} more\n                    </span>\n                  </div>\n                </td>\n              </ng-container>\n\n              <!-- User Count Column -->\n              <ng-container matColumnDef=\"userCount\">\n                <th mat-header-cell *matHeaderCellDef>Users</th>\n                <td mat-cell *matCellDef=\"let role\">\n                  <span class=\"user-count\">{{role.userCount}} users</span>\n                </td>\n              </ng-container>\n\n              <!-- Actions Column -->\n              <ng-container matColumnDef=\"actions\">\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\n                <td mat-cell *matCellDef=\"let role\">\n                  <div class=\"action-buttons\">\n                    <button mat-icon-button (click)=\"editRole(role)\" matTooltip=\"Edit Role\">\n                      <mat-icon>edit</mat-icon>\n                    </button>\n                    <button mat-icon-button (click)=\"viewPermissions(role)\" matTooltip=\"View Permissions\">\n                      <mat-icon>visibility</mat-icon>\n                    </button>\n                    <button mat-icon-button color=\"warn\" (click)=\"deleteRole(role)\" \n                            [disabled]=\"role.name === 'Admin'\" matTooltip=\"Delete Role\">\n                      <mat-icon>delete</mat-icon>\n                    </button>\n                  </div>\n                </td>\n              </ng-container>\n\n              <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n              <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\" class=\"role-row\"></tr>\n            </table>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Permissions Overview -->\n      <mat-card class=\"permissions-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>security</mat-icon>\n            Available Permissions\n          </mat-card-title>\n        </mat-card-header>\n\n        <mat-card-content>\n          <div class=\"permissions-grid\">\n            <div *ngFor=\"let category of permissionCategories\" class=\"permission-category\">\n              <h4>{{category.name}}</h4>\n              <div class=\"permission-list\">\n                <mat-chip *ngFor=\"let permission of category.permissions\" class=\"permission-chip\">\n                  {{permission}}\n                </mat-chip>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .role-management-container {\n      padding: 1rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    mat-card {\n      margin-bottom: 1rem;\n    }\n\n    mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .roles-table {\n      width: 100%;\n    }\n\n    .role-row:hover {\n      background-color: #f5f5f5;\n    }\n\n    .role-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .role-info strong {\n      font-size: 1rem;\n    }\n\n    .role-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .permissions-container {\n      display: flex;\n      gap: 0.25rem;\n      flex-wrap: wrap;\n      align-items: center;\n    }\n\n    .permission-chip {\n      background-color: #e3f2fd;\n      color: #1976d2;\n      font-size: 0.75rem;\n    }\n\n    .more-permissions {\n      color: #666;\n      font-size: 0.8rem;\n      font-style: italic;\n    }\n\n    .user-count {\n      color: #666;\n      font-size: 0.9rem;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 0.25rem;\n    }\n\n    .permissions-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 1rem;\n    }\n\n    .permission-category {\n      padding: 1rem;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      background-color: #fafafa;\n    }\n\n    .permission-category h4 {\n      margin: 0 0 1rem 0;\n      color: #333;\n    }\n\n    .permission-list {\n      display: flex;\n      gap: 0.25rem;\n      flex-wrap: wrap;\n    }\n\n    @media (max-width: 768px) {\n      .permissions-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .action-buttons {\n        flex-direction: column;\n      }\n    }\n  `]\n})\nexport class RoleManagementComponent implements OnInit {\n  roles: Role[] = [];\n  displayedColumns: string[] = ['name', 'permissions', 'userCount', 'actions'];\n  \n  permissionCategories = [\n    {\n      name: 'User Management',\n      permissions: ['Create User', 'Edit User', 'Delete User', 'View Users']\n    },\n    {\n      name: 'Request Management',\n      permissions: ['Create Request', 'Approve Request', 'Reject Request', 'View All Requests']\n    },\n    {\n      name: 'Workflow Management',\n      permissions: ['Create Workflow', 'Edit Workflow', 'Delete Workflow', 'Manage Steps']\n    },\n    {\n      name: 'System Administration',\n      permissions: ['System Settings', 'View Logs', 'Backup Data', 'Manage Roles']\n    }\n  ];\n\n  constructor(private readonly snackBar: MatSnackBar) {}\n\n  ngOnInit(): void {\n    this.loadRoles();\n  }\n\n  loadRoles(): void {\n    // Mock data for demonstration\n    this.roles = [\n      {\n        id: '1',\n        name: 'Admin',\n        description: 'Full system access',\n        permissions: ['Create User', 'Edit User', 'Delete User', 'View Users', 'System Settings', 'View Logs'],\n        userCount: 2\n      },\n      {\n        id: '2',\n        name: 'Manager',\n        description: 'Team management and approval rights',\n        permissions: ['Approve Request', 'Reject Request', 'View All Requests', 'Create Request'],\n        userCount: 5\n      },\n      {\n        id: '3',\n        name: 'HR',\n        description: 'Human resources management',\n        permissions: ['View Users', 'Create Request', 'Approve Request', 'View All Requests'],\n        userCount: 3\n      },\n      {\n        id: '4',\n        name: 'Employee',\n        description: 'Basic user access',\n        permissions: ['Create Request', 'View Own Requests'],\n        userCount: 25\n      }\n    ];\n  }\n\n  createRole(): void {\n    this.snackBar.open('Create role dialog would open here', 'Close', { duration: 3000 });\n  }\n\n  editRole(role: Role): void {\n    this.snackBar.open(`Edit role: ${role.name}`, 'Close', { duration: 3000 });\n  }\n\n  viewPermissions(role: Role): void {\n    this.snackBar.open(`View permissions for: ${role.name}`, 'Close', { duration: 3000 });\n  }\n\n  deleteRole(role: Role): void {\n    if (role.name === 'Admin') {\n      this.snackBar.open('Cannot delete Admin role', 'Close', { duration: 3000 });\n      return;\n    }\n    this.snackBar.open(`Delete role: ${role.name}`, 'Close', { duration: 3000 });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDgB,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,WAAA;AAAS,IAAA,uBAAA;;;;;AAC/C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,OAAA,EAAA,EACX,GAAA,QAAA;AACb,IAAA,iBAAA,CAAA;AAAa,IAAA,uBAAA;AACrB,IAAA,yBAAA,GAAA,OAAA;AAAO,IAAA,iBAAA,CAAA;AAAoB,IAAA,uBAAA,EAAQ,EAC/B;;;;AAFI,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,IAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,WAAA;;;;;AAOX,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;;;;;AAG7C,IAAA,yBAAA,GAAA,YAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,eAAA,GAAA;;;;;AAEF,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,MAAA,QAAA,YAAA,SAAA,GAAA,QAAA;;;;;AANN,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,OAAA,EAAA;AAEhC,IAAA,qBAAA,GAAA,mDAAA,GAAA,GAAA,YAAA,EAAA,EAA0F,GAAA,+CAAA,GAAA,GAAA,QAAA,EAAA;AAM5F,IAAA,uBAAA,EAAM;;;;AAN6B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,QAAA,YAAA,MAAA,GAAA,CAAA,CAAA;AAG1B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,YAAA,SAAA,CAAA;;;;;AASX,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;;;;;AAC3C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,QAAA,EAAA;AACT,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA,EAAO;;;;AAA/B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,QAAA,WAAA,QAAA;;;;;AAM3B,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;;AAC7C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,OAAA,EAAA,EACN,GAAA,UAAA,EAAA;AACF,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,OAAA,CAAc;IAAA,CAAA;AAC7C,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA,EAAW;AAE3B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAwB,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,OAAA,CAAqB;IAAA,CAAA;AACpD,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAW;AAEjC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAqC,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,OAAA,CAAgB;IAAA,CAAA;AAE5D,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA,EAAW,EACpB,EACL;;;;AAHI,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,QAAA,SAAA,OAAA;;;;;AAOd,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AACA,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AAoBE,IAAA,yBAAA,GAAA,YAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,eAAA,GAAA;;;;;AAJN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA+E,GAAA,IAAA;AACzE,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;AACrB,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,oDAAA,GAAA,GAAA,YAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;;;;AALF,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,IAAA;AAE+B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,YAAA,WAAA;;;AA4H3C,IAAO,0BAAP,MAAO,yBAAuB;EAuBL;EAtB7B,QAAgB,CAAA;EAChB,mBAA6B,CAAC,QAAQ,eAAe,aAAa,SAAS;EAE3E,uBAAuB;IACrB;MACE,MAAM;MACN,aAAa,CAAC,eAAe,aAAa,eAAe,YAAY;;IAEvE;MACE,MAAM;MACN,aAAa,CAAC,kBAAkB,mBAAmB,kBAAkB,mBAAmB;;IAE1F;MACE,MAAM;MACN,aAAa,CAAC,mBAAmB,iBAAiB,mBAAmB,cAAc;;IAErF;MACE,MAAM;MACN,aAAa,CAAC,mBAAmB,aAAa,eAAe,cAAc;;;EAI/E,YAA6B,UAAqB;AAArB,SAAA,WAAA;EAAwB;EAErD,WAAQ;AACN,SAAK,UAAS;EAChB;EAEA,YAAS;AAEP,SAAK,QAAQ;MACX;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa,CAAC,eAAe,aAAa,eAAe,cAAc,mBAAmB,WAAW;QACrG,WAAW;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa,CAAC,mBAAmB,kBAAkB,qBAAqB,gBAAgB;QACxF,WAAW;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa,CAAC,cAAc,kBAAkB,mBAAmB,mBAAmB;QACpF,WAAW;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa,CAAC,kBAAkB,mBAAmB;QACnD,WAAW;;;EAGjB;EAEA,aAAU;AACR,SAAK,SAAS,KAAK,sCAAsC,SAAS,EAAE,UAAU,IAAI,CAAE;EACtF;EAEA,SAAS,MAAU;AACjB,SAAK,SAAS,KAAK,cAAc,KAAK,IAAI,IAAI,SAAS,EAAE,UAAU,IAAI,CAAE;EAC3E;EAEA,gBAAgB,MAAU;AACxB,SAAK,SAAS,KAAK,yBAAyB,KAAK,IAAI,IAAI,SAAS,EAAE,UAAU,IAAI,CAAE;EACtF;EAEA,WAAW,MAAU;AACnB,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,KAAK,4BAA4B,SAAS,EAAE,UAAU,IAAI,CAAE;AAC1E;IACF;AACA,SAAK,SAAS,KAAK,gBAAgB,KAAK,IAAI,IAAI,SAAS,EAAE,UAAU,IAAI,CAAE;EAC7E;;qCAjFW,0BAAuB,4BAAA,WAAA,CAAA;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,2BAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,aAAA,IAAA,GAAA,eAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,MAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,kBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,aAAA,GAAA,CAAA,gBAAA,WAAA,GAAA,CAAA,gBAAA,SAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,iBAAA,GAAA,CAAA,WAAA,IAAA,SAAA,YAAA,GAAA,aAAA,kBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,YAAA,EAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,uBAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,mBAAA,IAAA,cAAA,aAAA,GAAA,OAAA,GAAA,CAAA,mBAAA,IAAA,cAAA,oBAAA,GAAA,OAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,QAAA,cAAA,eAAA,GAAA,SAAA,UAAA,GAAA,CAAA,kBAAA,EAAA,GAAA,CAAA,WAAA,IAAA,GAAA,UAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,iBAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAxNhC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAuC,GAAA,UAAA,EAC3B,GAAA,iBAAA,EACS,GAAA,gBAAA,EACC,GAAA,UAAA;AACJ,MAAA,iBAAA,GAAA,sBAAA;AAAoB,MAAA,uBAAA;AAC9B,MAAA,iBAAA,GAAA,mBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,UAAA,CAAA;AACgB,MAAA,qBAAA,SAAA,SAAA,2DAAA;AAAA,eAAS,IAAA,WAAA;MAAY,CAAA;AAC7D,MAAA,yBAAA,GAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACb,MAAA,iBAAA,IAAA,eAAA;AACF,MAAA,uBAAA,EAAS,EACL;AAGR,MAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,OAAA,CAAA,EACa,IAAA,SAAA,CAAA;AAGzB,MAAA,kCAAA,IAAA,CAAA;AACE,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,wCAAA,GAAA,GAAA,MAAA,CAAA;;AAUxC,MAAA,kCAAA,IAAA,CAAA;AACE,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,wCAAA,GAAA,GAAA,MAAA,CAAA;;AAcxC,MAAA,kCAAA,IAAA,CAAA;AACE,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,wCAAA,GAAA,GAAA,MAAA,CAAA;;AAOxC,MAAA,kCAAA,IAAA,EAAA;AACE,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,wCAAA,IAAA,GAAA,MAAA,CAAA;;AAiBxC,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,MAAA,EAAA,EAAuD,IAAA,wCAAA,GAAA,GAAA,MAAA,EAAA;AAEzD,MAAA,uBAAA,EAAQ,EACJ,EACW;AAIrB,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAmC,IAAA,iBAAA,EAChB,IAAA,gBAAA,EACC,IAAA,UAAA;AACJ,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,iBAAA,IAAA,yBAAA;AACF,MAAA,uBAAA,EAAiB;AAGnB,MAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,OAAA,EAAA;AAEd,MAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAQF,MAAA,uBAAA,EAAM,EACW,EACV;;;AAlFY,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,cAAA,IAAA,KAAA;AAsDK,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,mBAAA,IAAA,gBAAA;AACa,MAAA,oBAAA;AAAA,MAAA,qBAAA,oBAAA,IAAA,gBAAA;AAiBT,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,oBAAA;;;IAnGlC;IAAY;IAAA;IACZ;IACA;IAAa;IAAA;IAAA;IAAA;IACb;IAAe;IAAA;IACf;IAAa;IACb;IAAc;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IACd;IAAc;IACd;EAAiB,GAAA,QAAA,CAAA,ojEAAA,EAAA,CAAA;;;sEA2NR,yBAAuB,CAAA;UAtOnC;uBACW,uBAAqB,YACnB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsGT,QAAA,CAAA,w0DAAA,EAAA,CAAA;;;;6EAmHU,yBAAuB,EAAA,WAAA,2BAAA,UAAA,kFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}