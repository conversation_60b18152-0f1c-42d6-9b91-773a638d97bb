{"version": 3, "sources": ["src/app/features/requests/components/requests-not-found/requests-not-found.component.ts"], "sourcesContent": ["\n    .not-found-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 2rem;\n    }\n\n    .not-found-card {\n      max-width: 600px;\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .error-icon {\n      margin-bottom: 1rem;\n    }\n\n    .error-icon mat-icon {\n      font-size: 64px;\n      width: 64px;\n      height: 64px;\n      color: #ff6b6b;\n    }\n\n    h1 {\n      color: #333;\n      margin-bottom: 1rem;\n      font-size: 2rem;\n    }\n\n    p {\n      color: #666;\n      margin-bottom: 1.5rem;\n      line-height: 1.6;\n      font-size: 1.1rem;\n    }\n\n    .suggestions {\n      text-align: left;\n      margin: 2rem 0;\n      padding: 1rem;\n      background-color: #f8f9fa;\n      border-radius: 8px;\n    }\n\n    .suggestions h3 {\n      color: #333;\n      margin-bottom: 1rem;\n      font-size: 1.2rem;\n    }\n\n    .suggestions ul {\n      margin: 0;\n      padding-left: 1.5rem;\n    }\n\n    .suggestions li {\n      color: #666;\n      margin-bottom: 0.5rem;\n      line-height: 1.4;\n    }\n\n    .actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      flex-wrap: wrap;\n      margin-top: 2rem;\n    }\n\n    @media (max-width: 480px) {\n      .actions {\n        flex-direction: column;\n      }\n      \n      .not-found-card {\n        padding: 1rem;\n      }\n      \n      h1 {\n        font-size: 1.5rem;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,WAAA;;AAGF,CAAA;AACE,aAAA;AACA,cAAA;AACA,WAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAJA,WAIA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;;AAGF;AACE,SAAA;AACA,iBAAA;AACA,aAAA;;AAGF;AACE,SAAA;AACA,iBAAA;AACA,eAAA;AACA,aAAA;;AAGF,CAAA;AACE,cAAA;AACA,UAAA,KAAA;AACA,WAAA;AACA,oBAAA;AACA,iBAAA;;AAGF,CARA,YAQA;AACE,SAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAdA,YAcA;AACE,UAAA;AACA,gBAAA;;AAGF,CAnBA,YAmBA;AACE,SAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,mBAAA;AACA,aAAA;AACA,cAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GATF;AAUI,oBAAA;;AAGF,GApEF;AAqEI,aAAA;;AAGF;AACE,eAAA;;;", "names": []}