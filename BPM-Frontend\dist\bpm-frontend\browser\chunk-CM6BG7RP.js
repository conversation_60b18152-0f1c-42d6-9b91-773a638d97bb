import {
  AuthGuard
} from "./chunk-MI7J4LQF.js";
import "./chunk-EP3FZZM6.js";
import "./chunk-AKJJBQK4.js";
import {
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-XLTWONBW.js";

// src/app/features/profile/profile.module.ts
var routes = [
  {
    path: "",
    loadComponent: () => import("./chunk-2SQRO3LS.js").then((c) => c.UserProfileComponent),
    canActivate: [AuthGuard]
  }
];
var ProfileModule = class _ProfileModule {
  static \u0275fac = function ProfileModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProfileModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _ProfileModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProfileModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();
export {
  ProfileModule
};
//# sourceMappingURL=chunk-CM6BG7RP.js.map
