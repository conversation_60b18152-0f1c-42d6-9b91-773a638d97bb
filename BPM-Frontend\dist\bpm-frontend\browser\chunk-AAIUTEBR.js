import {
  MatProgress<PERSON><PERSON>,
  MatProgressBarModule
} from "./chunk-FSJKBZWH.js";
import "./chunk-CPP3G34D.js";
import {
  RequestService
} from "./chunk-AM3AE65N.js";
import {
  MatChip,
  MatChipsModule
} from "./chunk-KJM35DAP.js";
import {
  RequestStatus,
  RequestType
} from "./chunk-XJ5TS5V6.js";
import "./chunk-PJGOPMTU.js";
import "./chunk-4WCUDP7B.js";
import {
  MatTab,
  MatTabGroup,
  MatTabsModule
} from "./chunk-ZR5MXJ33.js";
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
  MatTableModule
} from "./chunk-Z6DK6RU5.js";
import "./chunk-I7LZP7WV.js";
import "./chunk-FTI6XIW5.js";
import "./chunk-R6R3KTSK.js";
import {
  Mat<PERSON><PERSON>,
  MatCardContent,
  MatCardModule
} from "./chunk-QRMDFVVO.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-FSO3A3K2.js";
import {
  MatButton,
  MatButtonModule,
  MatIconButton
} from "./chunk-SVX3GQPM.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  Component,
  DatePipe,
  NgForOf,
  NgIf,
  Subject,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpropertyInterpolate1,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-XLTWONBW.js";

// src/app/features/dashboard/components/hr-dashboard/hr-dashboard.component.ts
var _c0 = (a0) => ["/requests/details", a0];
function HRDashboardComponent_div_93_th_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 49);
    \u0275\u0275text(1, "Employee");
    \u0275\u0275elementEnd();
  }
}
function HRDashboardComponent_div_93_td_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 50)(1, "div", 51)(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "small");
    \u0275\u0275text(5);
    \u0275\u0275pipe(6, "date");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const request_r1 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(request_r1.initiatorName);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(6, 2, request_r1.createdAt, "short"));
  }
}
function HRDashboardComponent_div_93_th_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 49);
    \u0275\u0275text(1, "Type");
    \u0275\u0275elementEnd();
  }
}
function HRDashboardComponent_div_93_td_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 50)(1, "mat-chip");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275classMap(ctx_r2.getRequestTypeClass(request_r2.type));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r2.getRequestTypeLabel(request_r2.type), " ");
  }
}
function HRDashboardComponent_div_93_th_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 49);
    \u0275\u0275text(1, "Request");
    \u0275\u0275elementEnd();
  }
}
function HRDashboardComponent_div_93_td_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 50)(1, "div", 52);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r4 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", request_r4.title || "No Title", " ");
  }
}
function HRDashboardComponent_div_93_th_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 49);
    \u0275\u0275text(1, "Current Step");
    \u0275\u0275elementEnd();
  }
}
function HRDashboardComponent_div_93_td_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 50)(1, "span", 53);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r5 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r2.getCurrentStepName(request_r5));
  }
}
function HRDashboardComponent_div_93_th_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 49);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function HRDashboardComponent_div_93_td_16_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "td", 50)(1, "div", 54)(2, "button", 55)(3, "mat-icon");
    \u0275\u0275text(4, "visibility");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "button", 56);
    \u0275\u0275listener("click", function HRDashboardComponent_div_93_td_16_Template_button_click_5_listener() {
      const request_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.processRequest(request_r7));
    });
    \u0275\u0275elementStart(6, "mat-icon");
    \u0275\u0275text(7, "check");
    \u0275\u0275elementEnd();
    \u0275\u0275text(8, " Process ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const request_r7 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(1, _c0, request_r7.id));
  }
}
function HRDashboardComponent_div_93_tr_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 57);
  }
}
function HRDashboardComponent_div_93_tr_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 58);
  }
}
function HRDashboardComponent_div_93_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 38)(1, "table", 39);
    \u0275\u0275elementContainerStart(2, 40);
    \u0275\u0275template(3, HRDashboardComponent_div_93_th_3_Template, 2, 0, "th", 41)(4, HRDashboardComponent_div_93_td_4_Template, 7, 5, "td", 42);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(5, 43);
    \u0275\u0275template(6, HRDashboardComponent_div_93_th_6_Template, 2, 0, "th", 41)(7, HRDashboardComponent_div_93_td_7_Template, 3, 3, "td", 42);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(8, 44);
    \u0275\u0275template(9, HRDashboardComponent_div_93_th_9_Template, 2, 0, "th", 41)(10, HRDashboardComponent_div_93_td_10_Template, 3, 1, "td", 42);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(11, 45);
    \u0275\u0275template(12, HRDashboardComponent_div_93_th_12_Template, 2, 0, "th", 41)(13, HRDashboardComponent_div_93_td_13_Template, 3, 1, "td", 42);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(14, 46);
    \u0275\u0275template(15, HRDashboardComponent_div_93_th_15_Template, 2, 0, "th", 41)(16, HRDashboardComponent_div_93_td_16_Template, 9, 3, "td", 42);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(17, HRDashboardComponent_div_93_tr_17_Template, 1, 0, "tr", 47)(18, HRDashboardComponent_div_93_tr_18_Template, 1, 0, "tr", 48);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("dataSource", ctx_r2.pendingHRRequests);
    \u0275\u0275advance(16);
    \u0275\u0275property("matHeaderRowDef", ctx_r2.hrColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r2.hrColumns);
  }
}
function HRDashboardComponent_div_94_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 59)(1, "mat-icon");
    \u0275\u0275text(2, "assignment_turned_in");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h3");
    \u0275\u0275text(4, "No Pending Reviews");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "All requests have been processed or there are no requests requiring HR review.");
    \u0275\u0275elementEnd()();
  }
}
function HRDashboardComponent_div_102_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 60)(1, "span", 61);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 62);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275element(5, "mat-progress-bar", 63);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const dept_r8 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(dept_r8.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("", dept_r8.count, " employees");
    \u0275\u0275advance();
    \u0275\u0275property("value", dept_r8.percentage);
  }
}
function HRDashboardComponent_div_107_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 64)(1, "div", 65);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 66)(4, "strong");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "small");
    \u0275\u0275text(7);
    \u0275\u0275pipe(8, "date");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const hire_r9 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r2.getInitials(hire_r9.name));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(hire_r9.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate2("", hire_r9.department, " - ", \u0275\u0275pipeBind2(8, 4, hire_r9.startDate, "short"), "");
  }
}
var HRDashboardComponent = class _HRDashboardComponent {
  requestService;
  destroy$ = new Subject();
  hrMetrics = {
    totalEmployees: 45,
    activeRequests: 12,
    processedThisMonth: 67,
    averageProcessingTime: 6.5,
    leaveRequests: 8,
    expenseReports: 15,
    trainingRequests: 4
  };
  pendingHRRequests = [];
  hrColumns = ["employee", "type", "title", "currentStep", "actions"];
  departments = [
    { name: "Engineering", count: 18, percentage: 40 },
    { name: "Sales", count: 12, percentage: 27 },
    { name: "Marketing", count: 8, percentage: 18 },
    { name: "HR", count: 4, percentage: 9 },
    { name: "Finance", count: 3, percentage: 6 }
  ];
  recentHires = [
    { name: "Alice Johnson", department: "Engineering", startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1e3) },
    { name: "Bob Smith", department: "Sales", startDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1e3) },
    { name: "Carol Davis", department: "Marketing", startDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1e3) }
  ];
  dailyProcessed = 5;
  efficiencyScore = 92;
  constructor(requestService) {
    this.requestService = requestService;
  }
  ngOnInit() {
    this.loadPendingHRRequests();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadPendingHRRequests() {
    this.pendingHRRequests = [
      {
        id: "1",
        type: RequestType.Leave,
        initiatorId: "user1",
        initiatorName: "John Doe",
        status: RequestStatus.Pending,
        title: "Annual Leave Request",
        description: "Family vacation",
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1e3),
        isDeleted: false,
        requestSteps: [
          {
            id: "1",
            requestId: "1",
            workflowStepId: "step1",
            status: 2,
            workflowStepName: "Manager Approval",
            responsibleRole: "Manager",
            createdAt: new Date(Date.now() - 48 * 60 * 60 * 1e3),
            isDeleted: false
          },
          {
            id: "2",
            requestId: "1",
            workflowStepId: "step2",
            status: 1,
            workflowStepName: "HR Review",
            responsibleRole: "HR",
            createdAt: new Date(Date.now() - 24 * 60 * 60 * 1e3),
            isDeleted: false
          }
        ]
      },
      {
        id: "2",
        type: RequestType.Training,
        initiatorId: "user2",
        initiatorName: "Jane Smith",
        status: RequestStatus.Pending,
        title: "AWS Certification Training",
        description: "Professional development",
        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1e3),
        isDeleted: false,
        requestSteps: [
          {
            id: "1",
            requestId: "2",
            workflowStepId: "step1",
            status: 2,
            workflowStepName: "Manager Approval",
            responsibleRole: "Manager",
            createdAt: new Date(Date.now() - 36 * 60 * 60 * 1e3),
            isDeleted: false
          },
          {
            id: "2",
            requestId: "2",
            workflowStepId: "step2",
            status: 1,
            workflowStepName: "HR Review",
            responsibleRole: "HR",
            createdAt: new Date(Date.now() - 12 * 60 * 60 * 1e3),
            isDeleted: false
          }
        ]
      }
    ];
  }
  getRequestTypeLabel(type) {
    switch (type) {
      case RequestType.Leave:
        return "Leave";
      case RequestType.Expense:
        return "Expense";
      case RequestType.Training:
        return "Training";
      case RequestType.ITSupport:
        return "IT Support";
      case RequestType.ProfileUpdate:
        return "Profile";
      default:
        return "Unknown";
    }
  }
  getRequestTypeClass(type) {
    switch (type) {
      case RequestType.Leave:
        return "type-leave";
      case RequestType.Expense:
        return "type-expense";
      case RequestType.Training:
        return "type-training";
      case RequestType.ITSupport:
        return "type-it";
      case RequestType.ProfileUpdate:
        return "type-profile";
      default:
        return "";
    }
  }
  getCurrentStepName(request) {
    const currentStep = request.requestSteps?.find((step) => step.status === 1);
    return currentStep?.workflowStepName || "Unknown";
  }
  processRequest(request) {
    console.log("Process request:", request.id);
  }
  getInitials(name) {
    return name.split(" ").map((n) => n.charAt(0)).join("").toUpperCase();
  }
  static \u0275fac = function HRDashboardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _HRDashboardComponent)(\u0275\u0275directiveInject(RequestService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _HRDashboardComponent, selectors: [["app-hr-dashboard"]], decls: 138, vars: 16, consts: [[1, "hr-dashboard"], [1, "dashboard-header"], [1, "metrics-grid"], [1, "metric-card", "employees"], [1, "metric"], [1, "metric-icon"], [1, "metric-info"], [1, "metric-card", "active"], [1, "metric-card", "processed"], [1, "metric-card", "time"], [1, "request-types-grid"], [1, "type-card", "leave"], [1, "type-metric"], [1, "type-info"], [1, "type-card", "expense"], [1, "type-card", "training"], [1, "content-card"], [3, "label"], [1, "tab-content"], [1, "tab-header"], ["mat-raised-button", "", "color", "primary", "routerLink", "/requests"], ["class", "requests-table", 4, "ngIf"], ["class", "no-data", 4, "ngIf"], ["label", "Employee Management"], [1, "employee-stats"], [1, "stat-section"], [1, "department-list"], ["class", "department-item", 4, "ngFor", "ngForOf"], [1, "recent-hires"], ["class", "hire-item", 4, "ngFor", "ngForOf"], ["label", "Analytics"], [1, "analytics-grid"], [1, "analytics-card"], [1, "chart-placeholder"], [1, "efficiency-metrics"], [1, "efficiency-item"], [1, "label"], [1, "value"], [1, "requests-table"], ["mat-table", "", 3, "dataSource"], ["matColumnDef", "employee"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "type"], ["matColumnDef", "title"], ["matColumnDef", "currentStep"], ["matColumnDef", "actions"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", ""], ["mat-cell", ""], [1, "employee-info"], [1, "request-title"], [1, "current-step"], [1, "action-buttons"], ["mat-icon-button", "", 3, "routerLink"], ["mat-raised-button", "", "color", "primary", 3, "click"], ["mat-header-row", ""], ["mat-row", ""], [1, "no-data"], [1, "department-item"], [1, "dept-name"], [1, "dept-count"], ["mode", "determinate", 3, "value"], [1, "hire-item"], [1, "hire-avatar"], [1, "hire-info"]], template: function HRDashboardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "HR Dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "Manage employee requests and HR processes");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 2)(7, "mat-card", 3)(8, "mat-card-content")(9, "div", 4)(10, "div", 5)(11, "mat-icon");
      \u0275\u0275text(12, "people");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(13, "div", 6)(14, "h3");
      \u0275\u0275text(15);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "p");
      \u0275\u0275text(17, "Total Employees");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(18, "mat-card", 7)(19, "mat-card-content")(20, "div", 4)(21, "div", 5)(22, "mat-icon");
      \u0275\u0275text(23, "assignment");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(24, "div", 6)(25, "h3");
      \u0275\u0275text(26);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "p");
      \u0275\u0275text(28, "Active Requests");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(29, "mat-card", 8)(30, "mat-card-content")(31, "div", 4)(32, "div", 5)(33, "mat-icon");
      \u0275\u0275text(34, "done_all");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(35, "div", 6)(36, "h3");
      \u0275\u0275text(37);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(38, "p");
      \u0275\u0275text(39, "Processed This Month");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(40, "mat-card", 9)(41, "mat-card-content")(42, "div", 4)(43, "div", 5)(44, "mat-icon");
      \u0275\u0275text(45, "schedule");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(46, "div", 6)(47, "h3");
      \u0275\u0275text(48);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(49, "p");
      \u0275\u0275text(50, "Avg. Processing Time");
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275elementStart(51, "div", 10)(52, "mat-card", 11)(53, "mat-card-content")(54, "div", 12)(55, "mat-icon");
      \u0275\u0275text(56, "event_available");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(57, "div", 13)(58, "h4");
      \u0275\u0275text(59);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(60, "p");
      \u0275\u0275text(61, "Leave Requests");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(62, "mat-card", 14)(63, "mat-card-content")(64, "div", 12)(65, "mat-icon");
      \u0275\u0275text(66, "receipt");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(67, "div", 13)(68, "h4");
      \u0275\u0275text(69);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(70, "p");
      \u0275\u0275text(71, "Expense Reports");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(72, "mat-card", 15)(73, "mat-card-content")(74, "div", 12)(75, "mat-icon");
      \u0275\u0275text(76, "school");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(77, "div", 13)(78, "h4");
      \u0275\u0275text(79);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(80, "p");
      \u0275\u0275text(81, "Training Requests");
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275elementStart(82, "mat-card", 16)(83, "mat-tab-group")(84, "mat-tab", 17)(85, "div", 18)(86, "div", 19)(87, "h3");
      \u0275\u0275text(88, "Requests Awaiting HR Review");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(89, "button", 20)(90, "mat-icon");
      \u0275\u0275text(91, "list");
      \u0275\u0275elementEnd();
      \u0275\u0275text(92, " View All Requests ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(93, HRDashboardComponent_div_93_Template, 19, 3, "div", 21)(94, HRDashboardComponent_div_94_Template, 7, 0, "div", 22);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(95, "mat-tab", 23)(96, "div", 18)(97, "div", 24)(98, "div", 25)(99, "h4");
      \u0275\u0275text(100, "Department Breakdown");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(101, "div", 26);
      \u0275\u0275template(102, HRDashboardComponent_div_102_Template, 6, 3, "div", 27);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(103, "div", 25)(104, "h4");
      \u0275\u0275text(105, "Recent Hires");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(106, "div", 28);
      \u0275\u0275template(107, HRDashboardComponent_div_107_Template, 9, 7, "div", 29);
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(108, "mat-tab", 30)(109, "div", 18)(110, "div", 31)(111, "div", 32)(112, "h4");
      \u0275\u0275text(113, "Request Volume Trends");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(114, "div", 33)(115, "mat-icon");
      \u0275\u0275text(116, "trending_up");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(117, "p");
      \u0275\u0275text(118, "Request volume chart would be displayed here");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(119, "div", 32)(120, "h4");
      \u0275\u0275text(121, "Processing Efficiency");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(122, "div", 34)(123, "div", 35)(124, "span", 36);
      \u0275\u0275text(125, "Average Processing Time:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(126, "span", 37);
      \u0275\u0275text(127);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(128, "div", 35)(129, "span", 36);
      \u0275\u0275text(130, "Requests Processed Today:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(131, "span", 37);
      \u0275\u0275text(132);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(133, "div", 35)(134, "span", 36);
      \u0275\u0275text(135, "Efficiency Score:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(136, "span", 37);
      \u0275\u0275text(137);
      \u0275\u0275elementEnd()()()()()()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(15);
      \u0275\u0275textInterpolate(ctx.hrMetrics.totalEmployees);
      \u0275\u0275advance(11);
      \u0275\u0275textInterpolate(ctx.hrMetrics.activeRequests);
      \u0275\u0275advance(11);
      \u0275\u0275textInterpolate(ctx.hrMetrics.processedThisMonth);
      \u0275\u0275advance(11);
      \u0275\u0275textInterpolate1("", ctx.hrMetrics.averageProcessingTime, "h");
      \u0275\u0275advance(11);
      \u0275\u0275textInterpolate(ctx.hrMetrics.leaveRequests);
      \u0275\u0275advance(10);
      \u0275\u0275textInterpolate(ctx.hrMetrics.expenseReports);
      \u0275\u0275advance(10);
      \u0275\u0275textInterpolate(ctx.hrMetrics.trainingRequests);
      \u0275\u0275advance(5);
      \u0275\u0275propertyInterpolate1("label", "Pending Review (", ctx.pendingHRRequests.length, ")");
      \u0275\u0275advance(9);
      \u0275\u0275property("ngIf", ctx.pendingHRRequests.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.pendingHRRequests.length === 0);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngForOf", ctx.departments);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngForOf", ctx.recentHires);
      \u0275\u0275advance(20);
      \u0275\u0275textInterpolate1("", ctx.hrMetrics.averageProcessingTime, " hours");
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.dailyProcessed);
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate1("", ctx.efficiencyScore, "%");
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, DatePipe, RouterModule, RouterLink, MatCardModule, MatCard, MatCardContent, MatButtonModule, MatButton, MatIconButton, MatIconModule, MatIcon, MatTableModule, MatTable, MatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatHeaderCell, MatCell, MatHeaderRow, MatRow, MatChipsModule, MatChip, MatTabsModule, MatTab, MatTabGroup, MatProgressBarModule, MatProgressBar], styles: ["\n\n.hr-dashboard[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.dashboard-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #333;\n}\n.dashboard-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.5rem 0 0 0;\n  color: #666;\n}\n.metrics-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.metric-card[_ngcontent-%COMP%] {\n  transition: transform 0.2s ease;\n}\n.metric-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n}\n.metric-card.employees[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #2196f3 0%,\n      #1976d2 100%);\n  color: white;\n}\n.metric-card.active[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #ff9800 0%,\n      #f57c00 100%);\n  color: white;\n}\n.metric-card.processed[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #4caf50 0%,\n      #388e3c 100%);\n  color: white;\n}\n.metric-card.time[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #9c27b0 0%,\n      #7b1fa2 100%);\n  color: white;\n}\n.metric[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.metric-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(255, 255, 255, 0.2);\n}\n.metric-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  width: 2rem;\n  height: 2rem;\n}\n.metric-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: bold;\n}\n.metric-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.25rem 0 0 0;\n  font-size: 1rem;\n}\n.request-types-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.type-card[_ngcontent-%COMP%] {\n  background-color: #f8f9fa;\n  border-left: 4px solid;\n}\n.type-card.leave[_ngcontent-%COMP%] {\n  border-left-color: #2196f3;\n}\n.type-card.expense[_ngcontent-%COMP%] {\n  border-left-color: #ff9800;\n}\n.type-card.training[_ngcontent-%COMP%] {\n  border-left-color: #4caf50;\n}\n.type-metric[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.type-metric[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  width: 2rem;\n  height: 2rem;\n  color: #666;\n}\n.type-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 1.5rem;\n  color: #333;\n}\n.type-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.25rem 0 0 0;\n  color: #666;\n}\n.content-card[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n}\n.tab-content[_ngcontent-%COMP%] {\n  padding: 1rem;\n}\n.tab-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.requests-table[_ngcontent-%COMP%] {\n  overflow-x: auto;\n}\n.requests-table[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.employee-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.employee-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.8rem;\n}\n.request-title[_ngcontent-%COMP%] {\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.current-step[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 0.8rem;\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.type-leave[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n.type-expense[_ngcontent-%COMP%] {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n}\n.type-training[_ngcontent-%COMP%] {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.type-it[_ngcontent-%COMP%] {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.type-profile[_ngcontent-%COMP%] {\n  background-color: #fce4ec;\n  color: #c2185b;\n}\n.no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #4caf50;\n}\n.employee-stats[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 2rem;\n}\n.stat-section[_ngcontent-%COMP%] {\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.stat-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 1rem 0;\n  color: #333;\n}\n.department-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.department-item[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.dept-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.dept-count[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #666;\n}\n.recent-hires[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.hire-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.hire-avatar[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: #2196f3;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 0.9rem;\n}\n.hire-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.hire-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.8rem;\n}\n.analytics-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 2rem;\n}\n.analytics-card[_ngcontent-%COMP%] {\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.analytics-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 1rem 0;\n  color: #333;\n}\n.chart-placeholder[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.chart-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  width: 3rem;\n  height: 3rem;\n  margin-bottom: 1rem;\n}\n.efficiency-metrics[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.efficiency-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  padding: 0.5rem;\n  background-color: white;\n  border-radius: 4px;\n}\n.efficiency-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.efficiency-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\n  color: #2196f3;\n  font-weight: bold;\n}\n@media (max-width: 768px) {\n  .metrics-grid[_ngcontent-%COMP%] {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  .request-types-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .tab-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n  }\n  .action-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .employee-stats[_ngcontent-%COMP%], \n   .analytics-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n@media (max-width: 480px) {\n  .metrics-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=hr-dashboard.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HRDashboardComponent, [{
    type: Component,
    args: [{ selector: "app-hr-dashboard", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatTableModule,
      MatChipsModule,
      MatTabsModule,
      MatProgressBarModule
    ], template: `
    <div class="hr-dashboard">
      <div class="dashboard-header">
        <h1>HR Dashboard</h1>
        <p>Manage employee requests and HR processes</p>
      </div>

      <!-- Key Metrics -->
      <div class="metrics-grid">
        <mat-card class="metric-card employees">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>people</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.totalEmployees}}</h3>
                <p>Total Employees</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card active">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>assignment</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.activeRequests}}</h3>
                <p>Active Requests</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card processed">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>done_all</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.processedThisMonth}}</h3>
                <p>Processed This Month</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card time">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>schedule</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.averageProcessingTime}}h</h3>
                <p>Avg. Processing Time</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Request Type Breakdown -->
      <div class="request-types-grid">
        <mat-card class="type-card leave">
          <mat-card-content>
            <div class="type-metric">
              <mat-icon>event_available</mat-icon>
              <div class="type-info">
                <h4>{{hrMetrics.leaveRequests}}</h4>
                <p>Leave Requests</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="type-card expense">
          <mat-card-content>
            <div class="type-metric">
              <mat-icon>receipt</mat-icon>
              <div class="type-info">
                <h4>{{hrMetrics.expenseReports}}</h4>
                <p>Expense Reports</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="type-card training">
          <mat-card-content>
            <div class="type-metric">
              <mat-icon>school</mat-icon>
              <div class="type-info">
                <h4>{{hrMetrics.trainingRequests}}</h4>
                <p>Training Requests</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Main Content Tabs -->
      <mat-card class="content-card">
        <mat-tab-group>
          <!-- Pending HR Review -->
          <mat-tab label="Pending Review ({{pendingHRRequests.length}})">
            <div class="tab-content">
              <div class="tab-header">
                <h3>Requests Awaiting HR Review</h3>
                <button mat-raised-button color="primary" routerLink="/requests">
                  <mat-icon>list</mat-icon>
                  View All Requests
                </button>
              </div>

              <div *ngIf="pendingHRRequests.length > 0" class="requests-table">
                <table mat-table [dataSource]="pendingHRRequests">
                  <!-- Employee Column -->
                  <ng-container matColumnDef="employee">
                    <th mat-header-cell *matHeaderCellDef>Employee</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="employee-info">
                        <strong>{{request.initiatorName}}</strong>
                        <small>{{request.createdAt | date:'short'}}</small>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Request Type Column -->
                  <ng-container matColumnDef="type">
                    <th mat-header-cell *matHeaderCellDef>Type</th>
                    <td mat-cell *matCellDef="let request">
                      <mat-chip [class]="getRequestTypeClass(request.type)">
                        {{getRequestTypeLabel(request.type)}}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Title Column -->
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Request</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="request-title">
                        {{request.title || 'No Title'}}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Current Step Column -->
                  <ng-container matColumnDef="currentStep">
                    <th mat-header-cell *matHeaderCellDef>Current Step</th>
                    <td mat-cell *matCellDef="let request">
                      <span class="current-step">{{getCurrentStepName(request)}}</span>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="action-buttons">
                        <button mat-icon-button [routerLink]="['/requests/details', request.id]">
                          <mat-icon>visibility</mat-icon>
                        </button>
                        <button mat-raised-button color="primary" (click)="processRequest(request)">
                          <mat-icon>check</mat-icon>
                          Process
                        </button>
                      </div>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="hrColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: hrColumns;"></tr>
                </table>
              </div>

              <div *ngIf="pendingHRRequests.length === 0" class="no-data">
                <mat-icon>assignment_turned_in</mat-icon>
                <h3>No Pending Reviews</h3>
                <p>All requests have been processed or there are no requests requiring HR review.</p>
              </div>
            </div>
          </mat-tab>

          <!-- Employee Management -->
          <mat-tab label="Employee Management">
            <div class="tab-content">
              <div class="employee-stats">
                <div class="stat-section">
                  <h4>Department Breakdown</h4>
                  <div class="department-list">
                    <div *ngFor="let dept of departments" class="department-item">
                      <span class="dept-name">{{dept.name}}</span>
                      <span class="dept-count">{{dept.count}} employees</span>
                      <mat-progress-bar mode="determinate" [value]="dept.percentage"></mat-progress-bar>
                    </div>
                  </div>
                </div>

                <div class="stat-section">
                  <h4>Recent Hires</h4>
                  <div class="recent-hires">
                    <div *ngFor="let hire of recentHires" class="hire-item">
                      <div class="hire-avatar">{{getInitials(hire.name)}}</div>
                      <div class="hire-info">
                        <strong>{{hire.name}}</strong>
                        <small>{{hire.department}} - {{hire.startDate | date:'short'}}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Analytics -->
          <mat-tab label="Analytics">
            <div class="tab-content">
              <div class="analytics-grid">
                <div class="analytics-card">
                  <h4>Request Volume Trends</h4>
                  <div class="chart-placeholder">
                    <mat-icon>trending_up</mat-icon>
                    <p>Request volume chart would be displayed here</p>
                  </div>
                </div>

                <div class="analytics-card">
                  <h4>Processing Efficiency</h4>
                  <div class="efficiency-metrics">
                    <div class="efficiency-item">
                      <span class="label">Average Processing Time:</span>
                      <span class="value">{{hrMetrics.averageProcessingTime}} hours</span>
                    </div>
                    <div class="efficiency-item">
                      <span class="label">Requests Processed Today:</span>
                      <span class="value">{{dailyProcessed}}</span>
                    </div>
                    <div class="efficiency-item">
                      <span class="label">Efficiency Score:</span>
                      <span class="value">{{efficiencyScore}}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;d87b4d41b3c6a8dc15c0b94159df7b3cd5ac364ff5d283132536706c024bd2ee;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/dashboard/components/hr-dashboard/hr-dashboard.component.ts */\n.hr-dashboard {\n  padding: 1rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.dashboard-header {\n  margin-bottom: 2rem;\n}\n.dashboard-header h1 {\n  margin: 0;\n  color: #333;\n}\n.dashboard-header p {\n  margin: 0.5rem 0 0 0;\n  color: #666;\n}\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.metric-card {\n  transition: transform 0.2s ease;\n}\n.metric-card:hover {\n  transform: translateY(-2px);\n}\n.metric-card.employees {\n  background:\n    linear-gradient(\n      135deg,\n      #2196f3 0%,\n      #1976d2 100%);\n  color: white;\n}\n.metric-card.active {\n  background:\n    linear-gradient(\n      135deg,\n      #ff9800 0%,\n      #f57c00 100%);\n  color: white;\n}\n.metric-card.processed {\n  background:\n    linear-gradient(\n      135deg,\n      #4caf50 0%,\n      #388e3c 100%);\n  color: white;\n}\n.metric-card.time {\n  background:\n    linear-gradient(\n      135deg,\n      #9c27b0 0%,\n      #7b1fa2 100%);\n  color: white;\n}\n.metric {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.metric-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(255, 255, 255, 0.2);\n}\n.metric-icon mat-icon {\n  font-size: 2rem;\n  width: 2rem;\n  height: 2rem;\n}\n.metric-info h3 {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: bold;\n}\n.metric-info p {\n  margin: 0.25rem 0 0 0;\n  font-size: 1rem;\n}\n.request-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.type-card {\n  background-color: #f8f9fa;\n  border-left: 4px solid;\n}\n.type-card.leave {\n  border-left-color: #2196f3;\n}\n.type-card.expense {\n  border-left-color: #ff9800;\n}\n.type-card.training {\n  border-left-color: #4caf50;\n}\n.type-metric {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.type-metric mat-icon {\n  font-size: 2rem;\n  width: 2rem;\n  height: 2rem;\n  color: #666;\n}\n.type-info h4 {\n  margin: 0;\n  font-size: 1.5rem;\n  color: #333;\n}\n.type-info p {\n  margin: 0.25rem 0 0 0;\n  color: #666;\n}\n.content-card {\n  margin-top: 1rem;\n}\n.tab-content {\n  padding: 1rem;\n}\n.tab-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.requests-table {\n  overflow-x: auto;\n}\n.requests-table table {\n  width: 100%;\n}\n.employee-info {\n  display: flex;\n  flex-direction: column;\n}\n.employee-info small {\n  color: #666;\n  font-size: 0.8rem;\n}\n.request-title {\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.current-step {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 0.8rem;\n}\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.type-leave {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n.type-expense {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n}\n.type-training {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.type-it {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.type-profile {\n  background-color: #fce4ec;\n  color: #c2185b;\n}\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data mat-icon {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #4caf50;\n}\n.employee-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 2rem;\n}\n.stat-section {\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.stat-section h4 {\n  margin: 0 0 1rem 0;\n  color: #333;\n}\n.department-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.department-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.dept-name {\n  font-weight: 500;\n}\n.dept-count {\n  font-size: 0.8rem;\n  color: #666;\n}\n.recent-hires {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.hire-item {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.hire-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: #2196f3;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 0.9rem;\n}\n.hire-info {\n  display: flex;\n  flex-direction: column;\n}\n.hire-info small {\n  color: #666;\n  font-size: 0.8rem;\n}\n.analytics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 2rem;\n}\n.analytics-card {\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.analytics-card h4 {\n  margin: 0 0 1rem 0;\n  color: #333;\n}\n.chart-placeholder {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.chart-placeholder mat-icon {\n  font-size: 3rem;\n  width: 3rem;\n  height: 3rem;\n  margin-bottom: 1rem;\n}\n.efficiency-metrics {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.efficiency-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 0.5rem;\n  background-color: white;\n  border-radius: 4px;\n}\n.efficiency-item .label {\n  font-weight: 500;\n}\n.efficiency-item .value {\n  color: #2196f3;\n  font-weight: bold;\n}\n@media (max-width: 768px) {\n  .metrics-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  .request-types-grid {\n    grid-template-columns: 1fr;\n  }\n  .tab-header {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n  }\n  .action-buttons {\n    flex-direction: column;\n  }\n  .employee-stats,\n  .analytics-grid {\n    grid-template-columns: 1fr;\n  }\n}\n@media (max-width: 480px) {\n  .metrics-grid {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=hr-dashboard.component.css.map */\n"] }]
  }], () => [{ type: RequestService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(HRDashboardComponent, { className: "HRDashboardComponent", filePath: "src/app/features/dashboard/components/hr-dashboard/hr-dashboard.component.ts", lineNumber: 667 });
})();
export {
  HRDashboardComponent
};
//# sourceMappingURL=chunk-AAIUTEBR.js.map
