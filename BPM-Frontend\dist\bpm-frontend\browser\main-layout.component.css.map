{"version": 3, "sources": ["src/app/shared/components/layout/main-layout.component.scss"], "sourcesContent": [".sidenav-container {\n  height: 100vh;\n}\n\n.sidenav {\n  width: 280px;\n  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  \n  .sidenav-header {\n    padding: 24px 16px;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    \n    .logo-container {\n      display: flex;\n      align-items: center;\n      margin-bottom: 24px;\n      \n      .logo-icon {\n        font-size: 32px;\n        width: 32px;\n        height: 32px;\n        margin-right: 12px;\n        color: white;\n      }\n      \n      .logo-text {\n        font-size: 24px;\n        font-weight: 600;\n        color: white;\n      }\n    }\n    \n    .user-info {\n      display: flex;\n      align-items: center;\n      \n      .user-avatar {\n        margin-right: 12px;\n        \n        mat-icon {\n          font-size: 40px;\n          width: 40px;\n          height: 40px;\n          color: rgba(255, 255, 255, 0.8);\n        }\n      }\n      \n      .user-details {\n        .user-name {\n          font-weight: 500;\n          font-size: 14px;\n          color: white;\n          margin-bottom: 2px;\n        }\n        \n        .user-role {\n          font-size: 12px;\n          color: rgba(255, 255, 255, 0.7);\n        }\n      }\n    }\n  }\n  \n  .nav-list {\n    padding: 0;\n    flex: 1;\n    \n    .nav-item {\n      color: rgba(255, 255, 255, 0.8);\n      transition: all 0.3s ease;\n      margin: 4px 8px;\n      border-radius: 8px;\n      \n      &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        color: white;\n      }\n      \n      &.active-link {\n        background-color: rgba(255, 255, 255, 0.15);\n        color: white;\n        \n        mat-icon {\n          color: white;\n        }\n      }\n      \n      mat-icon {\n        color: rgba(255, 255, 255, 0.7);\n        margin-right: 16px;\n      }\n    }\n  }\n  \n  .sidenav-footer {\n    margin-top: auto;\n    padding: 16px 0;\n    border-top: 1px solid rgba(255, 255, 255, 0.1);\n    \n    .nav-item {\n      color: rgba(255, 255, 255, 0.8);\n      margin: 4px 8px;\n      border-radius: 8px;\n      \n      &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        color: white;\n      }\n      \n      mat-icon {\n        color: rgba(255, 255, 255, 0.7);\n        margin-right: 16px;\n      }\n    }\n  }\n}\n\n.toolbar {\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  background: white !important;\n  color: #333 !important;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  \n  .toolbar-title {\n    font-size: 20px;\n    font-weight: 500;\n    color: #333;\n  }\n  \n  .toolbar-spacer {\n    flex: 1 1 auto;\n  }\n  \n  button {\n    color: #333;\n    \n    &:hover {\n      background-color: rgba(0, 0, 0, 0.04);\n    }\n  }\n}\n\n.main-content {\n  padding: 24px;\n  min-height: calc(100vh - 64px);\n  background-color: #f8f9fa;\n}\n\n// Notification Menu Styles\n.notification-menu {\n  width: 400px;\n  max-height: 500px;\n  \n  .notification-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16px;\n    border-bottom: 1px solid #e0e0e0;\n    font-weight: 500;\n    \n    button {\n      font-size: 12px;\n      color: #667eea;\n    }\n  }\n  \n  .notification-list {\n    max-height: 300px;\n    overflow-y: auto;\n    \n    .notification-item {\n      padding: 12px 16px;\n      border-bottom: 1px solid #f0f0f0;\n      cursor: pointer;\n      transition: background-color 0.2s ease;\n      \n      &:hover {\n        background-color: #f5f5f5;\n      }\n      \n      &.unread {\n        background-color: #e3f2fd;\n        \n        &:hover {\n          background-color: #bbdefb;\n        }\n      }\n      \n      .notification-time {\n        font-size: 11px;\n        color: #666;\n        margin-top: 4px;\n      }\n    }\n  }\n  \n  .no-notifications {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    padding: 32px 16px;\n    color: #666;\n    \n    mat-icon {\n      font-size: 48px;\n      width: 48px;\n      height: 48px;\n      margin-bottom: 8px;\n      opacity: 0.5;\n    }\n  }\n}\n\n// Responsive Design\n@media (max-width: 768px) {\n  .sidenav {\n    width: 100%;\n  }\n  \n  .main-content {\n    padding: 16px;\n  }\n  \n  .notification-menu {\n    width: 320px;\n  }\n}\n\n// Dark mode support (optional)\n@media (prefers-color-scheme: dark) {\n  .toolbar {\n    background: #1e1e1e !important;\n    color: white !important;\n    \n    .toolbar-title {\n      color: white;\n    }\n    \n    button {\n      color: white;\n      \n      &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n      }\n    }\n  }\n  \n  .main-content {\n    background-color: #121212;\n    color: white;\n  }\n}\n\n// Animation for smooth transitions\n.nav-item {\n  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);\n}\n\n.notification-item {\n  transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);\n}\n\n// Custom scrollbar for notification list\n.notification-list::-webkit-scrollbar {\n  width: 6px;\n}\n\n.notification-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n.notification-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.notification-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}"], "mappings": ";AAAA,CAAA;AACE,UAAA;;AAGF,CAAA;AACE,SAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,SAAA;;AAEA,CALF,QAKE,CAAA;AACE,WAAA,KAAA;AACA,iBAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAEA,CATJ,QASI,CAJF,eAIE,CAAA;AACE,WAAA;AACA,eAAA;AACA,iBAAA;;AAEA,CAdN,QAcM,CATJ,eASI,CALF,eAKE,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,gBAAA;AACA,SAAA;;AAGF,CAtBN,QAsBM,CAjBJ,eAiBI,CAbF,eAaE,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAIJ,CA7BJ,QA6BI,CAxBF,eAwBE,CAAA;AACE,WAAA;AACA,eAAA;;AAEA,CAjCN,QAiCM,CA5BJ,eA4BI,CAJF,UAIE,CAAA;AACE,gBAAA;;AAEA,CApCR,QAoCQ,CA/BN,eA+BM,CAPJ,UAOI,CAHF,YAGE;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAKF,CA7CR,QA6CQ,CAxCN,eAwCM,CAhBJ,UAgBI,CAAA,aAAA,CAAA;AACE,eAAA;AACA,aAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CApDR,QAoDQ,CA/CN,eA+CM,CAvBJ,UAuBI,CAPA,aAOA,CAAA;AACE,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAMR,CA5DF,QA4DE,CAAA;AACE,WAAA;AACA,QAAA;;AAEA,CAhEJ,QAgEI,CAJF,SAIE,CAAA;AACE,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,IAAA,KAAA;AACA,UAAA,IAAA;AACA,iBAAA;;AAEA,CAtEN,QAsEM,CAVJ,SAUI,CANF,QAME;AACE,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;;AAGF,CA3EN,QA2EM,CAfJ,SAeI,CAXF,QAWE,CAAA;AACE,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;;AAEA,CA/ER,QA+EQ,CAnBN,SAmBM,CAfJ,QAeI,CAJF,YAIE;AACE,SAAA;;AAIJ,CApFN,QAoFM,CAxBJ,SAwBI,CApBF,SAoBE;AACE,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,gBAAA;;AAKN,CA3FF,QA2FE,CAAA;AACE,cAAA;AACA,WAAA,KAAA;AACA,cAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAEA,CAhGJ,QAgGI,CALF,eAKE,CAhCA;AAiCE,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA;AACA,iBAAA;;AAEA,CArGN,QAqGM,CAVJ,eAUI,CArCF,QAqCE;AACE,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;;AAGF,CA1GN,QA0GM,CAfJ,eAeI,CA1CF,SA0CE;AACE,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,gBAAA;;AAMR,CAAA;AACE,YAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA;AACA,SAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CARF,QAQE,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAdF,QAcE,CAAA;AACE,QAAA,EAAA,EAAA;;AAGF,CAlBF,QAkBE;AACE,SAAA;;AAEA,CArBJ,QAqBI,MAAA;AACE,oBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAKN,CAAA;AACE,WAAA;AACA,cAAA,KAAA,MAAA,EAAA;AACA,oBAAA;;AAIF,CAAA;AACE,SAAA;AACA,cAAA;;AAEA,CAJF,kBAIE,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,iBAAA,IAAA,MAAA;AACA,eAAA;;AAEA,CAZJ,kBAYI,CARF,oBAQE;AACE,aAAA;AACA,SAAA;;AAIJ,CAlBF,kBAkBE,CAAA;AACE,cAAA;AACA,cAAA;;AAEA,CAtBJ,kBAsBI,CAJF,kBAIE,CAAA;AACE,WAAA,KAAA;AACA,iBAAA,IAAA,MAAA;AACA,UAAA;AACA,cAAA,iBAAA,KAAA;;AAEA,CA5BN,kBA4BM,CAVJ,kBAUI,CANF,iBAME;AACE,oBAAA;;AAGF,CAhCN,kBAgCM,CAdJ,kBAcI,CAVF,iBAUE,CAAA;AACE,oBAAA;;AAEA,CAnCR,kBAmCQ,CAjBN,kBAiBM,CAbJ,iBAaI,CAHF,MAGE;AACE,oBAAA;;AAIJ,CAxCN,kBAwCM,CAtBJ,kBAsBI,CAlBF,kBAkBE,CAAA;AACE,aAAA;AACA,SAAA;AACA,cAAA;;AAKN,CAhDF,kBAgDE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,WAAA,KAAA;AACA,SAAA;;AAEA,CAvDJ,kBAuDI,CAPF,iBAOE;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;;AAMN,OAAA,CAAA,SAAA,EAAA;AACE,GAvNF;AAwNI,WAAA;;AAGF,GA9EF;AA+EI,aAAA;;AAGF,GA3EF;AA4EI,WAAA;;;AAKJ,OAAA,CAAA,oBAAA,EAAA;AACE,GApHF;AAqHI,gBAAA;AACA,WAAA;;AAEA,GAxHJ,QAwHI,CAhHF;AAiHI,WAAA;;AAGF,GA5HJ,QA4HI;AACE,WAAA;;AAEA,GA/HN,QA+HM,MAAA;AACE,sBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAKN,GA1GF;AA2GI,sBAAA;AACA,WAAA;;;AAKJ,CA9LI;AA+LF,cAAA,IAAA,KAAA,aAAA,GAAA,EAAA,CAAA,EAAA,GAAA,EAAA;;AAGF,CAxFI;AAyFF,cAAA,IAAA,KAAA,aAAA,GAAA,EAAA,CAAA,EAAA,GAAA,EAAA;;AAIF,CAjGE,iBAiGF;AACE,SAAA;;AAGF,CArGE,iBAqGF;AACE,cAAA;;AAGF,CAzGE,iBAyGF;AACE,cAAA;AACA,iBAAA;;AAGF,CA9GE,iBA8GF,yBAAA;AACE,cAAA;;", "names": []}