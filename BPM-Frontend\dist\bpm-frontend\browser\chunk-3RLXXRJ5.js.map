{"version": 3, "sources": ["src/app/features/requests/requests-routing.module.ts", "src/app/features/requests/requests.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\n\nconst routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/request-list/request-list.component').then(c => c.RequestListComponent)\n  },\n  {\n    path: 'new/:type',\n    loadComponent: () => import('./components/request-form/request-form.component').then(c => c.RequestFormComponent)\n  },\n  {\n    path: 'details/:id',\n    loadComponent: () => import('./components/request-details/request-details.component').then(c => c.RequestDetailsComponent)\n  },\n  {\n    path: 'approval',\n    loadComponent: () => import('./components/request-approval/request-approval.component').then(c => c.RequestApprovalComponent)\n  },\n  {\n    path: '**',\n    loadComponent: () => import('./components/requests-not-found/requests-not-found.component').then(c => c.RequestsNotFoundComponent)\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class RequestsRoutingModule { }\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\nimport { RequestsRoutingModule } from './requests-routing.module';\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule,\n    RouterModule,\n    RequestsRoutingModule\n  ]\n})\nexport class RequestsModule { }\n"], "mappings": ";;;;;;;;;;;;AAGA,IAAM,SAAiB;EACrB;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAkD,EAAE,KAAK,OAAK,EAAE,oBAAoB;;EAElH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAkD,EAAE,KAAK,OAAK,EAAE,oBAAoB;;EAElH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAwD,EAAE,KAAK,OAAK,EAAE,uBAAuB;;EAE3H;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA0D,EAAE,KAAK,OAAK,EAAE,wBAAwB;;EAE9H;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA8D,EAAE,KAAK,OAAK,EAAE,yBAAyB;;;AAQ/H,IAAO,wBAAP,MAAO,uBAAqB;;qCAArB,wBAAqB;EAAA;wEAArB,uBAAqB,CAAA;4EAHtB,aAAa,SAAS,MAAM,GAC5B,YAAY,EAAA,CAAA;;;sEAEX,uBAAqB,CAAA;UAJjC;WAAS;MACR,SAAS,CAAC,aAAa,SAAS,MAAM,CAAC;MACvC,SAAS,CAAC,YAAY;KACvB;;;;;ACfK,IAAO,iBAAP,MAAO,gBAAc;;qCAAd,iBAAc;EAAA;wEAAd,gBAAc,CAAA;;IALvB;IACA;IACA;EAAqB,EAAA,CAAA;;;sEAGZ,gBAAc,CAAA;UAR1B;WAAS;MACR,cAAc,CAAA;MACd,SAAS;QACP;QACA;QACA;;KAEH;;;", "names": []}