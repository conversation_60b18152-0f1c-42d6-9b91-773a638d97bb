{"version": 3, "sources": ["src/app/shared/components/unauthorized/unauthorized.component.ts"], "sourcesContent": ["\n    .unauthorized-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 2rem;\n    }\n\n    .unauthorized-card {\n      max-width: 500px;\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .error-icon {\n      margin-bottom: 1rem;\n    }\n\n    .error-icon mat-icon {\n      font-size: 64px;\n      width: 64px;\n      height: 64px;\n      color: #f44336;\n    }\n\n    h1 {\n      color: #333;\n      margin-bottom: 1rem;\n    }\n\n    p {\n      color: #666;\n      margin-bottom: 1rem;\n      line-height: 1.6;\n    }\n\n    .actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      flex-wrap: wrap;\n      margin-top: 2rem;\n    }\n\n    @media (max-width: 480px) {\n      .actions {\n        flex-direction: column;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,WAAA;;AAGF,CAAA;AACE,aAAA;AACA,cAAA;AACA,WAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAJA,WAIA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;;AAGF;AACE,SAAA;AACA,iBAAA;;AAGF;AACE,SAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,mBAAA;AACA,aAAA;AACA,cAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GATF;AAUI,oBAAA;;;", "names": []}