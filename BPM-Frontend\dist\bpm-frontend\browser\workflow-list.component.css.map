{"version": 3, "sources": ["src/app/features/workflows/components/workflow-list/workflow-list.component.ts"], "sourcesContent": ["\n    .workflow-list-container {\n      padding: 1rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .filters {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n      flex-wrap: wrap;\n    }\n\n    .filters mat-form-field {\n      min-width: 200px;\n    }\n\n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .workflows-table {\n      width: 100%;\n    }\n\n    .workflow-row:hover {\n      background-color: #f5f5f5;\n    }\n\n    .workflow-name strong {\n      display: block;\n      font-size: 1rem;\n    }\n\n    .workflow-name small {\n      color: #666;\n      font-size: 0.8rem;\n      display: block;\n      margin-top: 0.25rem;\n    }\n\n    .version-chip {\n      background-color: #e3f2fd;\n      color: #1976d2;\n      font-size: 0.75rem;\n    }\n\n    .steps-count {\n      color: #666;\n      font-size: 0.9rem;\n    }\n\n    .status-active {\n      background-color: #d4edda;\n      color: #155724;\n    }\n\n    .status-inactive {\n      background-color: #f8d7da;\n      color: #721c24;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 0.5rem;\n      align-items: center;\n    }\n\n    .no-data {\n      text-align: center;\n      padding: 3rem;\n      color: #666;\n    }\n\n    .no-data mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #4caf50;\n    }\n\n    @media (max-width: 768px) {\n      .filters {\n        flex-direction: column;\n      }\n      \n      .filters mat-form-field {\n        min-width: 100%;\n      }\n\n      .action-buttons {\n        flex-direction: column;\n        gap: 0.25rem;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAPA,QAOA;AACE,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA,YAAA;AACE,oBAAA;;AAGF,CAAA,cAAA;AACE,WAAA;AACA,aAAA;;AAGF,CALA,cAKA;AACE,SAAA;AACA,aAAA;AACA,WAAA;AACA,cAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,eAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;;AAGF,CANA,QAMA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GAnFF;AAoFI,oBAAA;;AAGF,GAvFF,QAuFE;AACE,eAAA;;AAGF,GA7BF;AA8BI,oBAAA;AACA,SAAA;;;", "names": []}