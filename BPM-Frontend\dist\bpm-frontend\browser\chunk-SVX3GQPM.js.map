{"version": 3, "sources": ["node_modules/@angular/cdk/fesm2022/fake-event-detection-DWOdFTFz.mjs", "node_modules/@angular/cdk/fesm2022/keycodes-CpHkExLC.mjs", "node_modules/@angular/cdk/fesm2022/shadow-dom-B0oHn41l.mjs", "node_modules/@angular/cdk/fesm2022/backwards-compatibility-DHR38MsD.mjs", "node_modules/@angular/cdk/fesm2022/platform-DmdVEw_C.mjs", "node_modules/@angular/cdk/fesm2022/passive-listeners-esHZRgIN.mjs", "node_modules/@angular/cdk/fesm2022/element-x4z00URv.mjs", "node_modules/@angular/cdk/fesm2022/focus-monitor-e2l_RpN3.mjs", "node_modules/@angular/cdk/fesm2022/style-loader-Cu9AvjH9.mjs", "node_modules/@angular/cdk/fesm2022/private.mjs", "node_modules/@angular/cdk/fesm2022/array-I1yfCXUO.mjs", "node_modules/@angular/cdk/fesm2022/breakpoints-observer-CljOfYGy.mjs", "node_modules/@angular/cdk/fesm2022/observers.mjs", "node_modules/@angular/cdk/fesm2022/a11y-module-BYox5gpI.mjs", "node_modules/@angular/cdk/fesm2022/id-generator-Dw_9dSDu.mjs", "node_modules/@angular/cdk/fesm2022/typeahead-9ZW4Dtsf.mjs", "node_modules/@angular/cdk/fesm2022/keycodes.mjs", "node_modules/@angular/cdk/fesm2022/list-key-manager-CyOIXo8P.mjs", "node_modules/@angular/cdk/fesm2022/activedescendant-key-manager-DC3-fwQI.mjs", "node_modules/@angular/cdk/fesm2022/focus-key-manager-C1rAQJ5z.mjs", "node_modules/@angular/cdk/fesm2022/a11y.mjs", "node_modules/@angular/cdk/fesm2022/scrolling-BkvA05C8.mjs", "node_modules/@angular/cdk/fesm2022/test-environment-CT0XxPyp.mjs", "node_modules/@angular/cdk/fesm2022/platform.mjs", "node_modules/@angular/cdk/fesm2022/boolean-property-DaaVhX5A.mjs", "node_modules/@angular/cdk/fesm2022/css-pixel-value-C_HEqLhI.mjs", "node_modules/@angular/cdk/fesm2022/coercion.mjs", "node_modules/@angular/material/fesm2022/ripple-BT3tzh6F.mjs", "node_modules/@angular/material/fesm2022/ripple-loader-Ce3DAhPW.mjs", "node_modules/@angular/material/fesm2022/structural-styles-BQUT6wsL.mjs", "node_modules/@angular/material/fesm2022/icon-button-D1J0zeqv.mjs", "node_modules/@angular/cdk/fesm2022/directionality-CBXD4hga.mjs", "node_modules/@angular/cdk/fesm2022/bidi.mjs", "node_modules/@angular/material/fesm2022/common-module-WayjW0Pb.mjs", "node_modules/@angular/material/fesm2022/index-SYVYjXwK.mjs", "node_modules/@angular/material/fesm2022/button.mjs"], "sourcesContent": ["/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\nexport { isFakeTouchstartFromScreenReader as a, isFakeMousedownFromScreenReader as i };\n", "const MAC_ENTER = 3;\nconst BACKSPACE = 8;\nconst TAB = 9;\nconst NUM_CENTER = 12;\nconst ENTER = 13;\nconst SHIFT = 16;\nconst CONTROL = 17;\nconst ALT = 18;\nconst PAUSE = 19;\nconst CAPS_LOCK = 20;\nconst ESCAPE = 27;\nconst SPACE = 32;\nconst PAGE_UP = 33;\nconst PAGE_DOWN = 34;\nconst END = 35;\nconst HOME = 36;\nconst LEFT_ARROW = 37;\nconst UP_ARROW = 38;\nconst RIGHT_ARROW = 39;\nconst DOWN_ARROW = 40;\nconst PLUS_SIGN = 43;\nconst PRINT_SCREEN = 44;\nconst INSERT = 45;\nconst DELETE = 46;\nconst ZERO = 48;\nconst ONE = 49;\nconst TWO = 50;\nconst THREE = 51;\nconst FOUR = 52;\nconst FIVE = 53;\nconst SIX = 54;\nconst SEVEN = 55;\nconst EIGHT = 56;\nconst NINE = 57;\nconst FF_SEMICOLON = 59; // Firefox (Gecko) fires this for semicolon instead of 186\nconst FF_EQUALS = 61; // Firefox (Gecko) fires this for equals instead of 187\nconst QUESTION_MARK = 63;\nconst AT_SIGN = 64;\nconst A = 65;\nconst B = 66;\nconst C = 67;\nconst D = 68;\nconst E = 69;\nconst F = 70;\nconst G = 71;\nconst H = 72;\nconst I = 73;\nconst J = 74;\nconst K = 75;\nconst L = 76;\nconst M = 77;\nconst N = 78;\nconst O = 79;\nconst P = 80;\nconst Q = 81;\nconst R = 82;\nconst S = 83;\nconst T = 84;\nconst U = 85;\nconst V = 86;\nconst W = 87;\nconst X = 88;\nconst Y = 89;\nconst Z = 90;\nconst META = 91; // WIN_KEY_LEFT\nconst MAC_WK_CMD_LEFT = 91;\nconst MAC_WK_CMD_RIGHT = 93;\nconst CONTEXT_MENU = 93;\nconst NUMPAD_ZERO = 96;\nconst NUMPAD_ONE = 97;\nconst NUMPAD_TWO = 98;\nconst NUMPAD_THREE = 99;\nconst NUMPAD_FOUR = 100;\nconst NUMPAD_FIVE = 101;\nconst NUMPAD_SIX = 102;\nconst NUMPAD_SEVEN = 103;\nconst NUMPAD_EIGHT = 104;\nconst NUMPAD_NINE = 105;\nconst NUMPAD_MULTIPLY = 106;\nconst NUMPAD_PLUS = 107;\nconst NUMPAD_MINUS = 109;\nconst NUMPAD_PERIOD = 110;\nconst NUMPAD_DIVIDE = 111;\nconst F1 = 112;\nconst F2 = 113;\nconst F3 = 114;\nconst F4 = 115;\nconst F5 = 116;\nconst F6 = 117;\nconst F7 = 118;\nconst F8 = 119;\nconst F9 = 120;\nconst F10 = 121;\nconst F11 = 122;\nconst F12 = 123;\nconst NUM_LOCK = 144;\nconst SCROLL_LOCK = 145;\nconst FIRST_MEDIA = 166;\nconst FF_MINUS = 173;\nconst MUTE = 173; // Firefox (Gecko) fires 181 for MUTE\nconst VOLUME_DOWN = 174; // Firefox (Gecko) fires 182 for VOLUME_DOWN\nconst VOLUME_UP = 175; // Firefox (Gecko) fires 183 for VOLUME_UP\nconst FF_MUTE = 181;\nconst FF_VOLUME_DOWN = 182;\nconst LAST_MEDIA = 183;\nconst FF_VOLUME_UP = 183;\nconst SEMICOLON = 186; // Firefox (Gecko) fires 59 for SEMICOLON\nconst EQUALS = 187; // Firefox (Gecko) fires 61 for EQUALS\nconst COMMA = 188;\nconst DASH = 189; // Firefox (Gecko) fires 173 for DASH/MINUS\nconst PERIOD = 190;\nconst SLASH = 191;\nconst APOSTROPHE = 192;\nconst TILDE = 192;\nconst OPEN_SQUARE_BRACKET = 219;\nconst BACKSLASH = 220;\nconst CLOSE_SQUARE_BRACKET = 221;\nconst SINGLE_QUOTE = 222;\nconst MAC_META = 224;\nexport { FF_EQUALS as $, A, BACKSPACE as B, CONTROL as C, DOWN_ARROW as D, END as E, F1 as F, PRINT_SCREEN as G, HOME as H, INSERT as I, TWO as J, THREE as K, LEFT_ARROW as L, MAC_META as M, NINE as N, ONE as O, PAGE_DOWN as P, FOUR as Q, RIGHT_ARROW as R, SPACE as S, TAB as T, UP_ARROW as U, FIVE as V, SIX as W, SEVEN as X, EIGHT as Y, Z, FF_SEMICOLON as _, PAGE_UP as a, CLOSE_SQUARE_BRACKET as a$, QUESTION_MARK as a0, AT_SIGN as a1, B as a2, C as a3, D as a4, E as a5, F as a6, G as a7, H as a8, I as a9, NUMPAD_SEVEN as aA, NUMPAD_EIGHT as aB, NUMPAD_NINE as aC, NUMPAD_MULTIPLY as aD, NUMPAD_PLUS as aE, NUMPAD_MINUS as aF, NUMPAD_PERIOD as aG, NUMPAD_DIVIDE as aH, NUM_LOCK as aI, SCROLL_LOCK as aJ, FIRST_MEDIA as aK, FF_MINUS as aL, MUTE as aM, VOLUME_DOWN as aN, VOLUME_UP as aO, FF_MUTE as aP, FF_VOLUME_DOWN as aQ, LAST_MEDIA as aR, FF_VOLUME_UP as aS, SEMICOLON as aT, EQUALS as aU, DASH as aV, SLASH as aW, APOSTROPHE as aX, TILDE as aY, OPEN_SQUARE_BRACKET as aZ, BACKSLASH as a_, J as aa, K as ab, L as ac, M as ad, N as ae, O as af, P as ag, Q as ah, R as ai, S as aj, T as ak, U as al, V as am, W as an, X as ao, Y as ap, MAC_WK_CMD_LEFT as aq, MAC_WK_CMD_RIGHT as ar, CONTEXT_MENU as as, NUMPAD_ZERO as at, NUMPAD_ONE as au, NUMPAD_TWO as av, NUMPAD_THREE as aw, NUMPAD_FOUR as ax, NUMPAD_FIVE as ay, NUMPAD_SIX as az, ZERO as b, SINGLE_QUOTE as b0, ENTER as c, ALT as d, META as e, SHIFT as f, ESCAPE as g, PERIOD as h, DELETE as i, F2 as j, F3 as k, F4 as l, F5 as m, F6 as n, F7 as o, F8 as p, F9 as q, F10 as r, F11 as s, F12 as t, COMMA as u, MAC_ENTER as v, NUM_CENTER as w, PAUSE as x, CAPS_LOCK as y, PLUS_SIGN as z };\n", "let shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n  }\n  return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null;\n    // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n  return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n  let activeElement = typeof document !== 'undefined' && document ? document.activeElement : null;\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement;\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n  return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return event.composedPath ? event.composedPath()[0] : event.target;\n}\nexport { _getEventTarget as _, _getShadowRoot as a, _supportsShadowDom as b, _getFocusedElementPierceShadowDom as c };\n", "import { VERSION } from '@angular/core';\n\n// TODO(crisbeto): remove this function when making breaking changes for v20.\n/**\n * Binds an event listener with specific options in a backwards-compatible way.\n * This function is necessary, because `Renderer2.listen` only supports listener options\n * after 19.1 and during the v19 period we support any 19.x version.\n * @docs-private\n */\nfunction _bindEventWithOptions(renderer, target, eventName, callback, options) {\n  const major = parseInt(VERSION.major);\n  const minor = parseInt(VERSION.minor);\n  // Event options in `listen` are only supported in 19.1 and beyond.\n  // We also allow 0.0.x, because that indicates a build at HEAD.\n  if (major > 19 || major === 19 && minor > 0 || major === 0 && minor === 0) {\n    return renderer.listen(target, eventName, callback, options);\n  }\n  target.addEventListener(eventName, callback, options);\n  return () => {\n    target.removeEventListener(eventName, callback, options);\n  };\n}\nexport { _bindEventWithOptions as _ };\n", "import * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Injectable } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n  _platformId = inject(PLATFORM_ID);\n  // We want to use the Angular platform check because if the Document is shimmed\n  // without the navigator, the following checks will fail. This is preferred because\n  // sometimes the Document may be shimmed without the user's knowledge or intention\n  /** Whether the Angular application is being rendered in the browser. */\n  isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n  /** Whether the current browser is Microsoft Edge. */\n  EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n  /** Whether the current rendering engine is Microsoft Trident. */\n  TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n  // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n  /** Whether the current rendering engine is Blink. */\n  BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT;\n  // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n  // ensure that Webkit runs standalone and is not used as another engine's base.\n  /** Whether the current rendering engine is WebKit. */\n  WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n  /** Whether the current platform is Apple iOS. */\n  IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n  // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n  // them self as Gecko-like browsers and modify the userAgent's according to that.\n  // Since we only cover one explicit Firefox case, we can simply check for Firefox\n  // instead of having an unstable check for Gecko.\n  /** Whether the current browser is Firefox. */\n  FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n  /** Whether the current platform is Android. */\n  // Trident on mobile adds the android platform to the userAgent to trick detections.\n  ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n  // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n  // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n  // Safari browser should also use Webkit as its layout engine.\n  /** Whether the current browser is Safari. */\n  SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n  constructor() {}\n  static ɵfac = function Platform_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Platform)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { Platform as P };\n", "/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nfunction supportsPassiveEventListeners() {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n        get: () => supportsPassiveEvents = true\n      }));\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n  return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nfunction normalizePassiveListenerOptions(options) {\n  return supportsPassiveEventListeners() ? options : !!options.capture;\n}\nexport { normalizePassiveListenerOptions as n, supportsPassiveEventListeners as s };\n", "import { ElementRef } from '@angular/core';\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  if (_isNumberValue(value)) {\n    return Number(value);\n  }\n  return arguments.length === 2 ? fallbackValue : 0;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\nexport { _isNumberValue as _, coerceElement as a, coerceNumberProperty as c };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output } from '@angular/core';\nimport { BehaviorSubject, Subject, of } from 'rxjs';\nimport { skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport { d as ALT, C as CONTROL, M as MAC_META, e as META, f as SHIFT } from './keycodes-CpHkExLC.mjs';\nimport { _ as _getEventTarget, a as _getShadowRoot } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { n as normalizePassiveListenerOptions } from './passive-listeners-esHZRgIN.mjs';\nimport { a as coerceElement } from './element-x4z00URv.mjs';\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n  passive: true,\n  capture: true\n};\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n  _platform = inject(Platform);\n  _listenerCleanups;\n  /** Emits whenever an input modality is detected. */\n  modalityDetected;\n  /** Emits when the input modality changes. */\n  modalityChanged;\n  /** The most recently detected input modality. */\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n  /**\n   * The most recently detected input modality event target. Is null if no input modality has been\n   * detected or if the associated event target is null for some unknown reason.\n   */\n  _mostRecentTarget = null;\n  /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n  _modality = new BehaviorSubject(null);\n  /** Options for this InputModalityDetector. */\n  _options;\n  /**\n   * The timestamp of the last touch input modality. Used to determine whether mousedown events\n   * should be attributed to mouse or touch.\n   */\n  _lastTouchMs = 0;\n  /**\n   * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n   * bound.\n   */\n  _onKeydown = event => {\n    // If this is one of the keys we should ignore, then ignore it and don't update the input\n    // modality to keyboard.\n    if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n      return;\n    }\n    this._modality.next('keyboard');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onMousedown = event => {\n    // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n    // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n    // after the previous touch event.\n    if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n      return;\n    }\n    // Fake mousedown events are fired by some screen readers when controls are activated by the\n    // screen reader. Attribute them to keyboard input modality.\n    this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onTouchstart = event => {\n    // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n    // events are fired. Again, attribute to keyboard input modality.\n    if (isFakeTouchstartFromScreenReader(event)) {\n      this._modality.next('keyboard');\n      return;\n    }\n    // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n    // triggered via mouse vs touch.\n    this._lastTouchMs = Date.now();\n    this._modality.next('touch');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  constructor() {\n    const ngZone = inject(NgZone);\n    const document = inject(DOCUMENT);\n    const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, {\n      optional: true\n    });\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    };\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (this._platform.isBrowser) {\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n      this._listenerCleanups = ngZone.runOutsideAngular(() => {\n        return [_bindEventWithOptions(renderer, document, 'keydown', this._onKeydown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'mousedown', this._onMousedown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'touchstart', this._onTouchstart, modalityEventListenerOptions)];\n      });\n    }\n  }\n  ngOnDestroy() {\n    this._modality.complete();\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n  }\n  static ɵfac = function InputModalityDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputModalityDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputModalityDetector,\n    factory: InputModalityDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _inputModalityDetector = inject(InputModalityDetector);\n  /** The focus origin that the next focus event is a result of. */\n  _origin = null;\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  _lastFocusOrigin;\n  /** Whether the window has just been focused. */\n  _windowFocused = false;\n  /** The timeout id of the window focus timeout. */\n  _windowFocusTimeoutId;\n  /** The timeout id of the origin clearing timeout. */\n  _originTimeoutId;\n  /**\n   * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n   * focus events to touch interactions requires special logic.\n   */\n  _originFromTouchInteraction = false;\n  /** Map of elements being monitored to their info. */\n  _elementInfo = new Map();\n  /** The number of elements currently being monitored. */\n  _monitoredElementCount = 0;\n  /**\n   * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n   * as well as the number of monitored elements that they contain. We have to treat focus/blur\n   * handlers differently from the rest of the events, because the browser won't emit events\n   * to the document when focus moves inside of a shadow root.\n   */\n  _rootNodeFocusListenerCount = new Map();\n  /**\n   * The specified detection mode, used for attributing the origin of a focus\n   * event.\n   */\n  _detectionMode;\n  /**\n   * Event listener for `focus` events on the window.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _windowFocusListener = () => {\n    // Make a note of when the window regains focus, so we can\n    // restore the origin info for the focused element.\n    this._windowFocused = true;\n    this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false);\n  };\n  /** Used to reference correct document/window */\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  /** Subject for stopping our InputModalityDetector subscription. */\n  _stopInputModalityDetector = new Subject();\n  constructor() {\n    const options = inject(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  /**\n   * Event listener for `focus` and 'blur' events on the document.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _rootNodeFocusAndBlurListener = event => {\n    const target = _getEventTarget(event);\n    // We need to walk up the ancestor chain in order to support `checkChildren`.\n    for (let element = target; element; element = element.parentElement) {\n      if (event.type === 'focus') {\n        this._onFocus(event, element);\n      } else {\n        this._onBlur(event, element);\n      }\n    }\n  };\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element);\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return of();\n    }\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n      return cachedInfo.subject;\n    }\n    // Create monitored element info.\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n    return info.subject;\n  }\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n    if (elementInfo) {\n      elementInfo.subject.complete();\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin);\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget(event);\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true /* isFromInteraction */);\n      });\n    }\n  }\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n    // Unregister global listeners when last element is unmonitored.\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  _getClosestElementsInfo(element) {\n    const results = [];\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector;\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n    const labels = focusEventTarget.labels;\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static ɵfac = function FocusMonitor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusMonitor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusMonitor,\n    factory: FocusMonitor.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n  _elementRef = inject(ElementRef);\n  _focusMonitor = inject(FocusMonitor);\n  _monitorSubscription;\n  _focusOrigin = null;\n  cdkFocusChange = new EventEmitter();\n  constructor() {}\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkMonitorFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkMonitorFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkMonitorFocus,\n    selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n    outputs: {\n      cdkFocusChange: \"cdkFocusChange\"\n    },\n    exportAs: [\"cdkMonitorFocus\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus'\n    }]\n  }], () => [], {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\nexport { CdkMonitorFocus as C, FocusMonitor as F, InputModalityDetector as I, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS as a, INPUT_MODALITY_DETECTOR_OPTIONS as b, FocusMonitorDetectionMode as c, FOCUS_MONITOR_DEFAULT_OPTIONS as d };\n", "import * as i0 from '@angular/core';\nimport { inject, Injector, EnvironmentInjector, ApplicationRef, createComponent, Injectable } from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = new WeakMap();\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\nclass _CdkPrivateStyleLoader {\n  _appRef;\n  _injector = inject(Injector);\n  _environmentInjector = inject(EnvironmentInjector);\n  /**\n   * Loads a set of styles.\n   * @param loader Component which will be instantiated to load the styles.\n   */\n  load(loader) {\n    // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n    const appRef = this._appRef = this._appRef || this._injector.get(ApplicationRef);\n    let data = appsWithLoaders.get(appRef);\n    // If we haven't loaded for this app before, we have to initialize it.\n    if (!data) {\n      data = {\n        loaders: new Set(),\n        refs: []\n      };\n      appsWithLoaders.set(appRef, data);\n      // When the app is destroyed, we need to clean up all the related loaders.\n      appRef.onDestroy(() => {\n        appsWithLoaders.get(appRef)?.refs.forEach(ref => ref.destroy());\n        appsWithLoaders.delete(appRef);\n      });\n    }\n    // If the loader hasn't been loaded before, we need to instatiate it.\n    if (!data.loaders.has(loader)) {\n      data.loaders.add(loader);\n      data.refs.push(createComponent(loader, {\n        environmentInjector: this._environmentInjector\n      }));\n    }\n  }\n  static ɵfac = function _CdkPrivateStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkPrivateStyleLoader)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _CdkPrivateStyleLoader,\n    factory: _CdkPrivateStyleLoader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkPrivateStyleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _CdkPrivateStyleLoader as _ };\n", "export { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\nclass _VisuallyHiddenLoader {\n  static ɵfac = function _VisuallyHiddenLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _VisuallyHiddenLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _VisuallyHiddenLoader,\n    selectors: [[\"ng-component\"]],\n    exportAs: [\"cdkVisuallyHidden\"],\n    decls: 0,\n    vars: 0,\n    template: function _VisuallyHiddenLoader_Template(rf, ctx) {},\n    styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_VisuallyHiddenLoader, [{\n    type: Component,\n    args: [{\n      exportAs: 'cdkVisuallyHidden',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { _VisuallyHiddenLoader };\n", "function coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nexport { coerceArray as c };\n", "import * as i0 from '@angular/core';\nimport { inject, CSP_NONCE, Injectable, NgZone } from '@angular/core';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n  _platform = inject(Platform);\n  _nonce = inject(CSP_NONCE, {\n    optional: true\n  });\n  /** The internal matchMedia method to return back a MediaQueryList like object. */\n  _matchMedia;\n  constructor() {\n    this._matchMedia = this._platform.isBrowser && window.matchMedia ?\n    // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n    // call it from a different scope.\n    window.matchMedia.bind(window) : noopMatchMedia;\n  }\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n  matchMedia(query) {\n    if (this._platform.WEBKIT || this._platform.BLINK) {\n      createEmptyStyleRule(query, this._nonce);\n    }\n    return this._matchMedia(query);\n  }\n  static ɵfac = function MediaMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MediaMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MediaMatcher,\n    factory: MediaMatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MediaMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      if (nonce) {\n        mediaQueryStyleNode.setAttribute('nonce', nonce);\n      }\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n\n/** Utility for checking the matching state of `@media` queries. */\nclass BreakpointObserver {\n  _mediaMatcher = inject(MediaMatcher);\n  _zone = inject(NgZone);\n  /**  A map of all media queries currently being listened for. */\n  _queries = new Map();\n  /** A subject for all other observables to takeUntil based on. */\n  _destroySubject = new Subject();\n  constructor() {}\n  /** Completes the active subject, signalling to all other observables to complete. */\n  ngOnDestroy() {\n    this._destroySubject.next();\n    this._destroySubject.complete();\n  }\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n  isMatched(value) {\n    const queries = splitQueries(coerceArray(value));\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @param value One or more media queries to check.\n   * @returns A stream of matches for the given queries.\n   */\n  observe(value) {\n    const queries = splitQueries(coerceArray(value));\n    const observables = queries.map(query => this._registerQuery(query).observable);\n    let stateObservable = combineLatest(observables);\n    // Emit the first state immediately, and then debounce the subsequent emissions.\n    stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n    return stateObservable.pipe(map(breakpointStates => {\n      const response = {\n        matches: false,\n        breakpoints: {}\n      };\n      breakpointStates.forEach(({\n        matches,\n        query\n      }) => {\n        response.matches = response.matches || matches;\n        response.breakpoints[query] = matches;\n      });\n      return response;\n    }));\n  }\n  /** Registers a specific query to be listened for. */\n  _registerQuery(query) {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query);\n    }\n    const mql = this._mediaMatcher.matchMedia(query);\n    // Create callback for match changes and add it is as a listener.\n    const queryObservable = new Observable(observer => {\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      const handler = e => this._zone.run(() => observer.next(e));\n      mql.addListener(handler);\n      return () => {\n        mql.removeListener(handler);\n      };\n    }).pipe(startWith(mql), map(({\n      matches\n    }) => ({\n      query,\n      matches\n    })), takeUntil(this._destroySubject));\n    // Add the MediaQueryList to the set of queries.\n    const output = {\n      observable: queryObservable,\n      mql\n    };\n    this._queries.set(query, output);\n    return output;\n  }\n  static ɵfac = function BreakpointObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BreakpointObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BreakpointObserver,\n    factory: BreakpointObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreakpointObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\nexport { BreakpointObserver as B, MediaMatcher as M };\n", "import * as i0 from '@angular/core';\nimport { Injectable, inject, Ng<PERSON>one, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n  static ɵfac = function MutationObserverFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MutationObserverFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MutationObserverFactory,\n    factory: MutationObserverFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  _mutationObserverFactory = inject(MutationObserverFactory);\n  /** Keeps track of the existing MutationObservers so they can be reused. */\n  _observedElements = new Map();\n  _ngZone = inject(NgZone);\n  constructor() {}\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n        this._ngZone.run(() => {\n          observer.next(records);\n        });\n      });\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject();\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true\n          });\n        }\n        this._observedElements.set(element, {\n          observer,\n          stream,\n          count: 1\n        });\n      } else {\n        this._observedElements.get(element).count++;\n      }\n      return this._observedElements.get(element).stream;\n    });\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n  static ɵfac = function ContentObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ContentObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ContentObserver,\n    factory: ContentObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  _contentObserver = inject(ContentObserver);\n  _elementRef = inject(ElementRef);\n  /** Event emitted for each change in the element's content. */\n  event = new EventEmitter();\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  _disabled = false;\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  _debounce;\n  _currentSubscription = null;\n  constructor() {}\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n  }\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n  static ɵfac = function CdkObserveContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkObserveContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkObserveContent,\n    selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n    inputs: {\n      disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n      debounce: \"debounce\"\n    },\n    outputs: {\n      event: \"cdkObserveContent\"\n    },\n    exportAs: [\"cdkObserveContent\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent'\n    }]\n  }], () => [], {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkObserveContentDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {\n  static ɵfac = function ObserversModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ObserversModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ObserversModule,\n    imports: [CdkObserveContent],\n    exports: [CdkObserveContent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MutationObserverFactory]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n", "import * as i0 from '@angular/core';\nimport { inject, Injectable, afterNextRender, NgZone, Injector, ElementRef, booleanAttribute, Directive, Input, InjectionToken, NgModule } from '@angular/core';\nimport { C as CdkMonitorFocus } from './focus-monitor-e2l_RpN3.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nimport { B as BreakpointObserver } from './breakpoints-observer-CljOfYGy.mjs';\nimport { ContentObserver, ObserversModule } from './observers.mjs';\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  /**\n   * Whether to count an element as focusable even if it is not currently visible.\n   */\n  ignoreVisibility = false;\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  _platform = inject(Platform);\n  constructor() {}\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n  static ɵfac = function InteractivityChecker_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InteractivityChecker)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InteractivityChecker,\n    factory: InteractivityChecker.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  _element;\n  _checker;\n  _ngZone;\n  _document;\n  _injector;\n  _startAnchor;\n  _endAnchor;\n  _hasAttached = false;\n  // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n  startAnchorListener = () => this.focusLastTabbableElement();\n  endAnchorListener = () => this.focusFirstTabbableElement();\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  _enabled = true;\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false, /** @breaking-change 20.0.0 param to become required */\n  _injector) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._injector = _injector;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {\n        injector: this._injector\n      });\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  _injector = inject(Injector);\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n  }\n  static ɵfac = function FocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapFactory,\n    factory: FocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n  _elementRef = inject(ElementRef);\n  _focusTrapFactory = inject(FocusTrapFactory);\n  /** Underlying FocusTrap instance. */\n  focusTrap;\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  _previouslyFocusedElement = null;\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  autoCapture;\n  constructor() {\n    const platform = inject(Platform);\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap?.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n  static ɵfac = function CdkTrapFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTrapFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTrapFocus,\n    selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n    inputs: {\n      enabled: [2, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n      autoCapture: [2, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n    },\n    exportAs: [\"cdkTrapFocus\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus'\n    }]\n  }], () => [], {\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocus',\n        transform: booleanAttribute\n      }]\n    }],\n    autoCapture: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocusAutoCapture',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n  _ngZone = inject(NgZone);\n  _defaultOptions = inject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _liveElement;\n  _document = inject(DOCUMENT);\n  _previousTimeout;\n  _currentPromise;\n  _currentResolve;\n  constructor() {\n    const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, {\n      optional: true\n    });\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n    this.clear();\n    clearTimeout(this._previousTimeout);\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n    this._document.body.appendChild(liveEl);\n    return liveEl;\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  _exposeAnnouncerToModals(id) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  static ɵfac = function LiveAnnouncer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LiveAnnouncer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LiveAnnouncer,\n    factory: LiveAnnouncer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n  _elementRef = inject(ElementRef);\n  _liveAnnouncer = inject(LiveAnnouncer);\n  _contentObserver = inject(ContentObserver);\n  _ngZone = inject(NgZone);\n  /** The aria-live politeness level to use when announcing messages. */\n  get politeness() {\n    return this._politeness;\n  }\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  _politeness = 'polite';\n  /** Time in milliseconds after which to clear out the announcer element. */\n  duration;\n  _previousAnnouncedText;\n  _subscription;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkAriaLive_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAriaLive)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAriaLive,\n    selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n    inputs: {\n      politeness: [0, \"cdkAriaLive\", \"politeness\"],\n      duration: [0, \"cdkAriaLiveDuration\", \"duration\"]\n    },\n    exportAs: [\"cdkAriaLive\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive'\n    }]\n  }], () => [], {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n  _platform = inject(Platform);\n  /**\n   * Figuring out the high contrast mode and adding the body classes can cause\n   * some expensive layouts. This flag is used to ensure that we only do it once.\n   */\n  _hasCheckedHighContrastMode;\n  _document = inject(DOCUMENT);\n  _breakpointSubscription;\n  constructor() {\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n  static ɵfac = function HighContrastModeDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HighContrastModeDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HighContrastModeDetector,\n    factory: HighContrastModeDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass A11yModule {\n  constructor() {\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n  static ɵfac = function A11yModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || A11yModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: A11yModule,\n    imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ObserversModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], () => [], null);\n})();\nexport { A11yModule as A, CdkTrapFocus as C, FocusTrapFactory as F, HighContrastModeDetector as H, InteractivityChecker as I, LiveAnnouncer as L, FocusTrap as a, HighContrastMode as b, IsFocusableConfig as c, CdkAriaLive as d, LIVE_ANNOUNCER_ELEMENT_TOKEN as e, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY as f, LIVE_ANNOUNCER_DEFAULT_OPTIONS as g };\n", "import * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable } from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nclass _IdGenerator {\n  _appId = inject(APP_ID);\n  /**\n   * Generates a unique ID with a specific prefix.\n   * @param prefix Prefix to add to the ID.\n   */\n  getId(prefix) {\n    // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n    // Angular app on them, we can reduce the amount of breakages by not adding it.\n    if (this._appId !== 'ng') {\n      prefix += this._appId;\n    }\n    if (!counters.hasOwnProperty(prefix)) {\n      counters[prefix] = 0;\n    }\n    return `${prefix}${counters[prefix]++}`;\n  }\n  static ɵfac = function _IdGenerator_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _IdGenerator)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _IdGenerator,\n    factory: _IdGenerator.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_IdGenerator, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _IdGenerator as _ };\n", "import { Subject } from 'rxjs';\nimport { tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { A, Z, b as ZERO, N as NINE } from './keycodes-CpHkExLC.mjs';\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n  _letterKeyStream = new Subject();\n  _items = [];\n  _selectedItemIndex = -1;\n  /** Buffer for the letters that the user has pressed */\n  _pressedLetters = [];\n  _skipPredicateFn;\n  _selectedItem = new Subject();\n  selectedItem = this._selectedItem;\n  constructor(initialItems, config) {\n    const typeAheadInterval = typeof config?.debounceInterval === 'number' ? config.debounceInterval : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n    if (config?.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && initialItems.length && initialItems.some(item => typeof item.getLabel !== 'function')) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n  setCurrentSelectedItemIndex(index) {\n    this._selectedItemIndex = index;\n  }\n  setItems(items) {\n    this._items = items;\n  }\n  handleKey(event) {\n    const keyCode = event.keyCode;\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Resets the currently stored sequence of typed letters. */\n  reset() {\n    this._pressedLetters = [];\n  }\n  _setupKeyHandler(typeAheadInterval) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase())).subscribe(inputString => {\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < this._items.length + 1; i++) {\n        const index = (this._selectedItemIndex + i) % this._items.length;\n        const item = this._items[index];\n        if (!this._skipPredicateFn?.(item) && item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n          this._selectedItem.next(item);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n  }\n}\nexport { Typeahead as T };\n", "export { A, d as ALT, aX as APOSTROPHE, a1 as AT_SIGN, a2 as B, a_ as BACKSLASH, B as BACKSPACE, a3 as C, y as CAPS_LOCK, a$ as CLOSE_SQUARE_BRACKET, u as COMM<PERSON>, as as CONTEXT_MENU, C as CONTROL, a4 as D, aV as DASH, i as DELETE, D as DOWN_ARROW, a5 as E, Y as EIGHT, E as END, c as ENTER, aU as EQUALS, g as ESCAPE, a6 as F, F as F1, r as F10, s as F11, t as F12, j as F2, k as F3, l as F4, m as F5, n as F6, o as F7, p as F8, q as F9, $ as FF_EQUALS, aL as FF_MINUS, aP as FF_MUTE, _ as FF_SEMICOLON, aQ as FF_VOLUME_DOWN, aS as FF_VOLUME_UP, aK as FIRST_MEDIA, V as FIVE, Q as FOUR, a7 as G, a8 as H, H as HOME, a9 as I, I as INSERT, aa as J, ab as K, ac as L, aR as LAST_MEDIA, L as LEFT_ARROW, ad as M, v as MAC_ENTER, M as MAC_META, aq as MAC_WK_CMD_LEFT, ar as MAC_WK_CMD_RIGHT, e as META, aM as MUTE, ae as N, N as NINE, aH as NUMPAD_DIVIDE, aB as NUMPAD_EIGHT, ay as NUMPAD_FIVE, ax as NUMPAD_FOUR, aF as NUMPAD_MINUS, aD as NUMPAD_MULTIPLY, aC as NUMPAD_NINE, au as NUMPAD_ONE, aG as NUMPAD_PERIOD, aE as NUMPAD_PLUS, aA as NUMPAD_SEVEN, az as NUMPAD_SIX, aw as NUMPAD_THREE, av as NUMPAD_TWO, at as NUMPAD_ZERO, w as NUM_CENTER, aI as NUM_LOCK, af as O, O as ONE, aZ as OPEN_SQUARE_BRACKET, ag as P, P as PAGE_DOWN, a as PAGE_UP, x as PAUSE, h as PERIOD, z as PLUS_SIGN, G as PRINT_SCREEN, ah as Q, a0 as QUESTION_MARK, ai as R, R as RIGHT_ARROW, aj as S, aJ as SCROLL_LOCK, aT as SEMICOLON, X as SEVEN, f as SHIFT, b0 as SINGLE_QUOTE, W as SIX, aW as SLASH, S as SPACE, ak as T, T as TAB, K as THREE, aY as TILDE, J as TWO, al as U, U as UP_ARROW, am as V, aN as VOLUME_DOWN, aO as VOLUME_UP, an as W, ao as X, ap as Y, Z, b as ZERO } from './keycodes-CpHkExLC.mjs';\n\n/**\n * Checks whether a modifier key is pressed.\n * @param event Event to be checked.\n */\nfunction hasModifierKey(event, ...modifiers) {\n  if (modifiers.length) {\n    return modifiers.some(modifier => event[modifier]);\n  }\n  return event.altKey || event.shiftKey || event.ctrlKey || event.metaKey;\n}\nexport { hasModifierKey };\n", "import { signal, QueryList, isSignal, effect } from '@angular/core';\nimport { Subscription, Subject } from 'rxjs';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { P as PAGE_DOWN, a as PAGE_UP, E as END, H as HOME, L as LEFT_ARROW, R as RIGHT_ARROW, U as UP_ARROW, D as DOWN_ARROW, T as TAB } from './keycodes-CpHkExLC.mjs';\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n  _items;\n  _activeItemIndex = -1;\n  _activeItem = signal(null);\n  _wrap = false;\n  _typeaheadSubscription = Subscription.EMPTY;\n  _itemChangesSubscription;\n  _vertical = true;\n  _horizontal;\n  _allowedModifierKeys = [];\n  _homeAndEnd = false;\n  _pageUpAndDown = {\n    enabled: false,\n    delta: 10\n  };\n  _effectRef;\n  _typeahead;\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager. By default, disabled items are skipped.\n   */\n  _skipPredicateFn = item => item.disabled;\n  constructor(_items, injector) {\n    this._items = _items;\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe(newItems => this._itemsChanged(newItems.toArray()));\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n      this._effectRef = effect(() => this._itemsChanged(_items()), {\n        injector\n      });\n    }\n  }\n  /**\n   * Stream that emits any time the TAB key is pressed, so components can react\n   * when focus is shifted off of the list.\n   */\n  tabOut = new Subject();\n  /** Stream that emits whenever the active item of the list manager changes. */\n  change = new Subject();\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval = 200) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n    this._typeaheadSubscription.unsubscribe();\n    const items = this._getItemsArray();\n    this._typeahead = new Typeahead(items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.setActiveItem(item);\n    });\n    return this;\n  }\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead() {\n    this._typeahead?.reset();\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled = true, delta = 10) {\n    this._pageUpAndDown = {\n      enabled,\n      delta\n    };\n    return this;\n  }\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem();\n    this.updateActiveItem(item);\n    if (this._activeItem() !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          this._typeahead?.handleKey(event);\n        }\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n  get activeItem() {\n    return this._activeItem();\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return !!this._typeahead && this._typeahead.isTyping();\n  }\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem.set(activeItem == null ? null : activeItem);\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._effectRef?.destroy();\n    this._typeahead?.destroy();\n    this.tabOut.complete();\n    this.change.complete();\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n    if (!items[index]) {\n      return;\n    }\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n      if (!items[index]) {\n        return;\n      }\n    }\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n  _getItemsArray() {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n  /** Callback for when the items have changed. */\n  _itemsChanged(newItems) {\n    this._typeahead?.setItems(newItems);\n    const activeItem = this._activeItem();\n    if (activeItem) {\n      const newIndex = newItems.indexOf(activeItem);\n      if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n        this._activeItemIndex = newIndex;\n        this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n      }\n    }\n  }\n}\nexport { ListKeyManager as L };\n", "import { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\nexport { ActiveDescendantKeyManager as A };\n", "import { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\nclass FocusKeyManager extends ListKeyManager {\n  _origin = 'program';\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  setActiveItem(item) {\n    super.setActiveItem(item);\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\nexport { FocusKeyManager as F };\n", "export { C as CdkMonitorFocus, d as FOCUS_MONITOR_DEFAULT_OPTIONS, F as FocusMonitor, c as FocusMonitorDetectionMode, a as INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, b as INPUT_MODALITY_DETECTOR_OPTIONS, I as InputModalityDetector } from './focus-monitor-e2l_RpN3.mjs';\nimport { a as FocusTrap, I as InteractivityChecker } from './a11y-module-BYox5gpI.mjs';\nexport { A as A11yModule, d as CdkAriaLive, C as CdkTrapFocus, F as FocusTrapFactory, b as HighContrastMode, H as HighContrastModeDetector, c as IsFocusableConfig, g as LIVE_ANNOUNCER_DEFAULT_OPTIONS, e as LIVE_ANNOUNCER_ELEMENT_TOKEN, f as LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, L as LiveAnnouncer } from './a11y-module-BYox5gpI.mjs';\nexport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, InjectionToken, NgZone, Injector } from '@angular/core';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nexport { A as ActiveDescendantKeyManager } from './activedescendant-key-manager-DC3-fwQI.mjs';\nexport { F as FocusKeyManager } from './focus-key-manager-C1rAQJ5z.mjs';\nexport { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\nimport { Subject } from 'rxjs';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-KnCoIkIC.mjs';\nexport { b as TREE_KEY_MANAGER_FACTORY, c as TREE_KEY_MANAGER_FACTORY_PROVIDER, a as TreeKeyManager } from './tree-key-manager-KnCoIkIC.mjs';\nexport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport 'rxjs/operators';\nimport './keycodes-CpHkExLC.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport './element-x4z00URv.mjs';\nimport './breakpoints-observer-CljOfYGy.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './typeahead-9ZW4Dtsf.mjs';\nimport './keycodes.mjs';\nimport './coercion/private.mjs';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT);\n  /** Map of all registered message elements that have been placed into the document. */\n  _messageRegistry = new Map();\n  /** Container for all registered messages. */\n  _messagesContainer = null;\n  /** Unique ID for the service. */\n  _id = `${nextId++}`;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n    this._createMessagesContainer();\n    this._messagesContainer.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n    const messagesContainer = this._document.createElement('div');\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n    if (!this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n  static ɵfac = function AriaDescriber_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AriaDescriber)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AriaDescriber,\n    factory: AriaDescriber.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n  _isNoopTreeKeyManager = true;\n  // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n  // implementation that does not emit to streams.\n  change = new Subject();\n  destroy() {\n    this.change.complete();\n  }\n  onKeydown() {\n    // noop\n  }\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  focusItem() {\n    // noop\n  }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n  return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY\n};\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  _focusTrapManager;\n  _inertStrategy;\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  /** Focus event handler. */\n  _listener = null;\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  // A stack of the FocusTraps on the page. Only the FocusTrap at the\n  // top of the stack is active.\n  _focusTrapStack = [];\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n  static ɵfac = function FocusTrapManager_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapManager)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapManager,\n    factory: FocusTrapManager.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _focusTrapManager = inject(FocusTrapManager);\n  _document = inject(DOCUMENT);\n  _inertStrategy;\n  _injector = inject(Injector);\n  constructor() {\n    const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, {\n      optional: true\n    });\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n  }\n  static ɵfac = function ConfigurableFocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfigurableFocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ConfigurableFocusTrapFactory,\n    factory: ConfigurableFocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_TRAP_INERT_STRATEGY, FocusTrap, InteractivityChecker, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId };\n", "/** The possible ways the browser may handle the horizontal scroll axis in RTL languages. */\nvar RtlScrollAxisType;\n(function (RtlScrollAxisType) {\n  /**\n   * scrollLeft is 0 when scrolled all the way left and (scrollWidth - clientWidth) when scrolled\n   * all the way right.\n   */\n  RtlScrollAxisType[RtlScrollAxisType[\"NORMAL\"] = 0] = \"NORMAL\";\n  /**\n   * scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  RtlScrollAxisType[RtlScrollAxisType[\"NEGATED\"] = 1] = \"NEGATED\";\n  /**\n   * scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  RtlScrollAxisType[RtlScrollAxisType[\"INVERTED\"] = 2] = \"INVERTED\";\n})(RtlScrollAxisType || (RtlScrollAxisType = {}));\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n  if (scrollBehaviorSupported == null) {\n    // If we're not in the browser, it can't be supported. Also check for `Element`, because\n    // some projects stub out the global `document` during SSR which can throw us off.\n    if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n      scrollBehaviorSupported = false;\n      return scrollBehaviorSupported;\n    }\n    // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n    if ('scrollBehavior' in document.documentElement.style) {\n      scrollBehaviorSupported = true;\n    } else {\n      // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n      // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n      const scrollToFunction = Element.prototype.scrollTo;\n      if (scrollToFunction) {\n        // We can detect if the function has been polyfilled by calling `toString` on it. Native\n        // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n        // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n        // polyfilled functions as supporting scroll behavior.\n        scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n      } else {\n        scrollBehaviorSupported = false;\n      }\n    }\n  }\n  return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n  // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n  if (typeof document !== 'object' || !document) {\n    return RtlScrollAxisType.NORMAL;\n  }\n  if (rtlScrollAxisType == null) {\n    // Create a 1px wide scrolling container and a 2px wide content element.\n    const scrollContainer = document.createElement('div');\n    const containerStyle = scrollContainer.style;\n    scrollContainer.dir = 'rtl';\n    containerStyle.width = '1px';\n    containerStyle.overflow = 'auto';\n    containerStyle.visibility = 'hidden';\n    containerStyle.pointerEvents = 'none';\n    containerStyle.position = 'absolute';\n    const content = document.createElement('div');\n    const contentStyle = content.style;\n    contentStyle.width = '2px';\n    contentStyle.height = '1px';\n    scrollContainer.appendChild(content);\n    document.body.appendChild(scrollContainer);\n    rtlScrollAxisType = RtlScrollAxisType.NORMAL;\n    // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n    // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n    // dealing with one of the other two types of browsers.\n    if (scrollContainer.scrollLeft === 0) {\n      // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n      // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n      // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n      // return 0 when we read it again.\n      scrollContainer.scrollLeft = 1;\n      rtlScrollAxisType = scrollContainer.scrollLeft === 0 ? RtlScrollAxisType.NEGATED : RtlScrollAxisType.INVERTED;\n    }\n    scrollContainer.remove();\n  }\n  return rtlScrollAxisType;\n}\nexport { RtlScrollAxisType as R, getRtlScrollAxisType as g, supportsScrollBehavior as s };\n", "/** Gets whether the code is currently running in a test environment. */\nfunction _isTestEnvironment() {\n  // We can't use `declare const` because it causes conflicts inside Google with the real typings\n  // for these symbols and we can't read them off the global object, because they don't appear to\n  // be attached there for some runners like Je<PERSON>.\n  // (see: https://github.com/angular/components/issues/23365#issuecomment-938146643)\n  return (\n    // @ts-ignore\n    typeof __karma__ !== 'undefined' && !!__karma__ ||\n    // @ts-ignore\n    typeof jasmine !== 'undefined' && !!jasmine ||\n    // @ts-ignore\n    typeof jest !== 'undefined' && !!jest ||\n    // @ts-ignore\n    typeof Mocha !== 'undefined' && !!Mocha\n  );\n}\nexport { _isTestEnvironment as _ };\n", "export { P as Platform } from './platform-DmdVEw_C.mjs';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { n as normalizePassiveListenerOptions, s as supportsPassiveEventListeners } from './passive-listeners-esHZRgIN.mjs';\nexport { R as RtlScrollAxisType, g as getRtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nexport { _ as _getEventTarget, c as _getFocusedElementPierceShadowDom, a as _getShadowRoot, b as _supportsShadowDom } from './shadow-dom-B0oHn41l.mjs';\nexport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nexport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport '@angular/common';\nclass PlatformModule {\n  static ɵfac = function PlatformModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PlatformModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PlatformModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n// `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n// first changing it to something else:\n// The specified value \"\" does not conform to the required format.\n// The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n'color', 'button', 'checkbox', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week'];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n  return supportedInputTypes;\n}\nexport { PlatformModule, getSupportedInputTypes };\n", "/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\nexport { coerceBooleanProperty as c };\n", "/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n  if (value == null) {\n    return '';\n  }\n  return typeof value === 'string' ? value : `${value}px`;\n}\nexport { coerceCssPixelValue as c };\n", "export { c as coerceBooleanProperty } from './boolean-property-DaaVhX5A.mjs';\nexport { _ as _isNumberValue, a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nexport { c as coerceArray } from './array-I1yfCXUO.mjs';\nexport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport '@angular/core';\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n  const result = [];\n  if (value != null) {\n    const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n    for (const sourceValue of sourceValues) {\n      const trimmedString = `${sourceValue}`.trim();\n      if (trimmedString) {\n        result.push(trimmedString);\n      }\n    }\n  }\n  return result;\n}\nexport { coerceStringArray };\n", "import { normalizePassiveListenerOptions, _getEventTarget, Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, InjectionToken, inject, ElementRef, ANIMATION_MODULE_TYPE, NgZone, Injector, Directive, Input } from '@angular/core';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\n\n/** Possible states for a ripple element. */\nvar RippleState;\n(function (RippleState) {\n  RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n  RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n  RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n  RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n})(RippleState || (RippleState = {}));\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n  _renderer;\n  element;\n  config;\n  _animationForciblyDisabledThroughCss;\n  /** Current state of the ripple. */\n  state = RippleState.HIDDEN;\n  constructor(_renderer, /** Reference to the ripple HTML element. */\n  element, /** Ripple configuration used for the ripple. */\n  config, /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n  }\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n  _events = new Map();\n  /** Adds an event handler. */\n  addHandler(ngZone, name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n      });\n    }\n  }\n  /** Removes an event handler. */\n  removeHandler(name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (!handlersForEvent) {\n      return;\n    }\n    const handlersForElement = handlersForEvent.get(element);\n    if (!handlersForElement) {\n      return;\n    }\n    handlersForElement.delete(handler);\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n    }\n  }\n  /** Event handler that is bound and which dispatches the events to the different targets. */\n  _delegateEventHandler = event => {\n    const target = _getEventTarget(event);\n    if (target) {\n      this._events.get(event.type)?.forEach((handlers, element) => {\n        if (element === target || element.contains(target)) {\n          handlers.forEach(handler => handler.handleEvent(event));\n        }\n      });\n    }\n  };\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\nclass _MatRippleStylesLoader {\n  static ɵfac = function _MatRippleStylesLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _MatRippleStylesLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _MatRippleStylesLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"mat-ripple-style-loader\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _MatRippleStylesLoader_Template(rf, ctx) {},\n    styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatRippleStylesLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'mat-ripple-style-loader': ''\n      },\n      styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"]\n    }]\n  }], null, null);\n})();\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n  _target;\n  _ngZone;\n  _platform;\n  /** Element where the ripples are being added to. */\n  _containerElement;\n  /** Element which triggers the ripple elements on mouse events. */\n  _triggerElement;\n  /** Whether the pointer is currently down or not. */\n  _isPointerDown = false;\n  /**\n   * Map of currently active ripple references.\n   * The ripple reference is mapped to its element event listeners.\n   * The reason why `| null` is used is that event listeners are added only\n   * when the condition is truthy (see the `_startFadeOutTransition` method).\n   */\n  _activeRipples = new Map();\n  /** Latest non-persistent ripple that was triggered. */\n  _mostRecentTransientRipple;\n  /** Time in milliseconds when the last touchstart event happened. */\n  _lastTouchStartEvent;\n  /** Whether pointer-up event listeners have been registered. */\n  _pointerUpEventsRegistered = false;\n  /**\n   * Cached dimensions of the ripple container. Set when the first\n   * ripple is shown and cleared once no more ripples are visible.\n   */\n  _containerRect;\n  static _eventManager = new RippleEventManager();\n  constructor(_target, _ngZone, elementOrElementRef, _platform, injector) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n    if (injector) {\n      injector.get(_CdkPrivateStyleLoader).load(_MatRippleStylesLoader);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n    this._containerElement.appendChild(ripple);\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n    // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s' ||\n    // If the container is 0x0, it's likely `display: none`.\n    containerRect.width === 0 && containerRect.height === 0;\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = RippleState.FADING_IN;\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n    let eventListeners = null;\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => {\n          // Clear the fallback timer since the transition fired correctly.\n          if (eventListeners) {\n            eventListeners.fallbackTimer = null;\n          }\n          clearTimeout(fallbackTimer);\n          this._finishRippleTransition(rippleRef);\n        };\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        // In some cases where there's a higher load on the browser, it can choose not to dispatch\n        // neither `transitionend` nor `transitioncancel` (see b/227356674). This timer serves as a\n        // fallback for such cases so that the ripple doesn't become stuck. We add a 100ms buffer\n        // because timers aren't precise. Note that another approach can be to transition the ripple\n        // to the `VISIBLE` state immediately above and to `FADING_IN` afterwards inside\n        // `transitionstart`. We go with the timer because it's one less event listener and\n        // it's less likely to break existing tests.\n        const fallbackTimer = setTimeout(onTransitionCancel, enterDuration + 100);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel,\n          fallbackTimer\n        };\n      });\n    }\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n      return;\n    }\n    const rippleEl = rippleRef.element;\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    };\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = RippleState.FADING_OUT;\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    }\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === RippleState.FADING_IN) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === RippleState.FADING_OUT) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = RippleState.VISIBLE;\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  _destroyRipple(rippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n    rippleRef.state = RippleState.HIDDEN;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n      if (eventListeners.fallbackTimer !== null) {\n        clearTimeout(eventListeners.fallbackTimer);\n      }\n    }\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches;\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n    this._isPointerDown = false;\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === RippleState.VISIBLE || ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN;\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n    if (trigger) {\n      pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n        this._pointerUpEventsRegistered = false;\n      }\n    }\n  }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n  _elementRef = inject(ElementRef);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  /** Custom color for all ripples. */\n  color;\n  /** Whether the ripples should be visible outside the component's bounds. */\n  unbounded;\n  /**\n   * Whether the ripple always originates from the center of the host element's bounds, rather\n   * than originating from the location of the click event.\n   */\n  centered;\n  /**\n   * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n   * will be the distance from the center of the ripple to the furthest corner of the host element's\n   * bounding rectangle.\n   */\n  radius = 0;\n  /**\n   * Configuration for the ripple animation. Allows modifying the enter and exit animation\n   * duration of the ripples. The animation durations will be overwritten if the\n   * `NoopAnimationsModule` is being used.\n   */\n  animation;\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  _disabled = false;\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  _trigger;\n  /** Renderer for the ripple DOM manipulations. */\n  _rippleRenderer;\n  /** Options that are set globally for all ripples. */\n  _globalOptions;\n  /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n  _isInitialized = false;\n  constructor() {\n    const ngZone = inject(NgZone);\n    const platform = inject(Platform);\n    const globalOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    const injector = inject(Injector);\n    // Note: cannot use `inject()` here, because this class\n    // gets instantiated manually in the ripple loader.\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, this._elementRef, platform, injector);\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig() {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: {\n        ...this._globalOptions.animation,\n        ...(this._animationMode === 'NoopAnimations' ? {\n          enterDuration: 0,\n          exitDuration: 0\n        } : {}),\n        ...this.animation\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n    };\n  }\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled() {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX, y = 0, config) {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, {\n        ...this.rippleConfig,\n        ...config\n      });\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, {\n        ...this.rippleConfig,\n        ...configOrX\n      });\n    }\n  }\n  static ɵfac = function MatRipple_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRipple)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatRipple,\n    selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n    hostAttrs: [1, \"mat-ripple\"],\n    hostVars: 2,\n    hostBindings: function MatRipple_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n      }\n    },\n    inputs: {\n      color: [0, \"matRippleColor\", \"color\"],\n      unbounded: [0, \"matRippleUnbounded\", \"unbounded\"],\n      centered: [0, \"matRippleCentered\", \"centered\"],\n      radius: [0, \"matRippleRadius\", \"radius\"],\n      animation: [0, \"matRippleAnimation\", \"animation\"],\n      disabled: [0, \"matRippleDisabled\", \"disabled\"],\n      trigger: [0, \"matRippleTrigger\", \"trigger\"]\n    },\n    exportAs: [\"matRipple\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRipple, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-ripple], [matRipple]',\n      exportAs: 'matRipple',\n      host: {\n        'class': 'mat-ripple',\n        '[class.mat-ripple-unbounded]': 'unbounded'\n      }\n    }]\n  }], () => [], {\n    color: [{\n      type: Input,\n      args: ['matRippleColor']\n    }],\n    unbounded: [{\n      type: Input,\n      args: ['matRippleUnbounded']\n    }],\n    centered: [{\n      type: Input,\n      args: ['matRippleCentered']\n    }],\n    radius: [{\n      type: Input,\n      args: ['matRippleRadius']\n    }],\n    animation: [{\n      type: Input,\n      args: ['matRippleAnimation']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matRippleDisabled']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['matRippleTrigger']\n    }]\n  });\n})();\nexport { MatRipple as M, RippleRenderer as R, MAT_RIPPLE_GLOBAL_OPTIONS as a, RippleState as b, RippleRef as c, defaultRippleAnimationConfig as d };\n", "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, NgZone, Injector, RendererFactory2, Injectable } from '@angular/core';\nimport { Platform, _bindEventWithOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS, R as RippleRenderer, d as defaultRippleAnimationConfig } from './ripple-BT3tzh6F.mjs';\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {\n  capture: true\n};\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n  _document = inject(DOCUMENT);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n    optional: true\n  });\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _injector = inject(Injector);\n  _eventCleanups;\n  _hosts = new Map();\n  constructor() {\n    const renderer = inject(RendererFactory2).createRenderer(null, null);\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => {\n      return rippleInteractionEvents.map(name => _bindEventWithOptions(renderer, this._document, name, this._onInteraction, eventListenerOptions));\n    });\n  }\n  ngOnDestroy() {\n    const hosts = this._hosts.keys();\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n    this._eventCleanups.forEach(cleanup => cleanup());\n  }\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(host, config) {\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, this._globalRippleOptions?.namespace ?? '');\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host, disabled) {\n    const ripple = this._hosts.get(host);\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.target.rippleDisabled = disabled;\n      if (!disabled && !ripple.hasSetUpEvents) {\n        ripple.hasSetUpEvents = true;\n        ripple.renderer.setupTriggerEvents(host);\n      }\n    } else if (disabled) {\n      // Otherwise, set an attribute so we know what the\n      // disabled state should be when the ripple is initialized.\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n  /**\n   * Handles creating and attaching component internals\n   * when a component is initially interacted with.\n   */\n  _onInteraction = event => {\n    const eventTarget = _getEventTarget(event);\n    if (eventTarget instanceof HTMLElement) {\n      // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n      const element = eventTarget.closest(`[${matRippleUninitialized}=\"${this._globalRippleOptions?.namespace ?? ''}\"]`);\n      if (element) {\n        this._createRipple(element);\n      }\n    }\n  };\n  /** Creates a MatRipple and appends it to the given element. */\n  _createRipple(host) {\n    if (!this._document || this._hosts.has(host)) {\n      return;\n    }\n    // Create the ripple element.\n    host.querySelector('.mat-ripple')?.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n    host.append(rippleEl);\n    const isNoopAnimations = this._animationMode === 'NoopAnimations';\n    const globalOptions = this._globalRippleOptions;\n    const enterDuration = isNoopAnimations ? 0 : globalOptions?.animation?.enterDuration ?? defaultRippleAnimationConfig.enterDuration;\n    const exitDuration = isNoopAnimations ? 0 : globalOptions?.animation?.exitDuration ?? defaultRippleAnimationConfig.exitDuration;\n    const target = {\n      rippleDisabled: isNoopAnimations || globalOptions?.disabled || host.hasAttribute(matRippleDisabled),\n      rippleConfig: {\n        centered: host.hasAttribute(matRippleCentered),\n        terminateOnPointerUp: globalOptions?.terminateOnPointerUp,\n        animation: {\n          enterDuration,\n          exitDuration\n        }\n      }\n    };\n    const renderer = new RippleRenderer(target, this._ngZone, rippleEl, this._platform, this._injector);\n    const hasSetUpEvents = !target.rippleDisabled;\n    if (hasSetUpEvents) {\n      renderer.setupTriggerEvents(host);\n    }\n    this._hosts.set(host, {\n      target,\n      renderer,\n      hasSetUpEvents\n    });\n    host.removeAttribute(matRippleUninitialized);\n  }\n  destroyRipple(host) {\n    const ripple = this._hosts.get(host);\n    if (ripple) {\n      ripple.renderer._removeTriggerEvents();\n      this._hosts.delete(host);\n    }\n  }\n  static ɵfac = function MatRippleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRippleLoader)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatRippleLoader,\n    factory: MatRippleLoader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { MatRippleLoader as M };\n", "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\nclass _StructuralStylesLoader {\n  static ɵfac = function _StructuralStylesLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _StructuralStylesLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _StructuralStylesLoader,\n    selectors: [[\"structural-styles\"]],\n    decls: 0,\n    vars: 0,\n    template: function _StructuralStylesLoader_Template(rf, ctx) {},\n    styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_StructuralStylesLoader, [{\n    type: Component,\n    args: [{\n      selector: 'structural-styles',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { _StructuralStylesLoader as _ };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZ<PERSON>, ANIMATION_MODULE_TYPE, booleanAttribute, Directive, Input, Renderer2, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { M as MatRippleLoader } from './ripple-loader-Ce3DAhPW.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\n\n/** Injection token that can be used to provide the default options the button component. */\nconst _c0 = [\"mat-icon-button\", \"\"];\nconst _c1 = [\"*\"];\nconst _c2 = \".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\";\nconst _c3 = \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\";\nconst MAT_BUTTON_CONFIG = new InjectionToken('MAT_BUTTON_CONFIG');\n/** Shared host configuration for all buttons */\nconst MAT_BUTTON_HOST = {\n  '[attr.disabled]': '_getDisabledAttribute()',\n  '[attr.aria-disabled]': '_getAriaDisabled()',\n  '[class.mat-mdc-button-disabled]': 'disabled',\n  '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true',\n  '[class]': 'color ? \"mat-\" + color : \"\"'\n};\n/** List of classes to add to buttons instances based on host attribute selector. */\nconst HOST_SELECTOR_MDC_CLASS_PAIR = [{\n  attribute: 'mat-button',\n  mdcClasses: ['mdc-button', 'mat-mdc-button']\n}, {\n  attribute: 'mat-flat-button',\n  mdcClasses: ['mdc-button', 'mdc-button--unelevated', 'mat-mdc-unelevated-button']\n}, {\n  attribute: 'mat-raised-button',\n  mdcClasses: ['mdc-button', 'mdc-button--raised', 'mat-mdc-raised-button']\n}, {\n  attribute: 'mat-stroked-button',\n  mdcClasses: ['mdc-button', 'mdc-button--outlined', 'mat-mdc-outlined-button']\n}, {\n  attribute: 'mat-fab',\n  mdcClasses: ['mdc-fab', 'mat-mdc-fab-base', 'mat-mdc-fab']\n}, {\n  attribute: 'mat-mini-fab',\n  mdcClasses: ['mdc-fab', 'mat-mdc-fab-base', 'mdc-fab--mini', 'mat-mdc-mini-fab']\n}, {\n  attribute: 'mat-icon-button',\n  mdcClasses: ['mdc-icon-button', 'mat-mdc-icon-button']\n}];\n/** Base class for all buttons.  */\nclass MatButtonBase {\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _focusMonitor = inject(FocusMonitor);\n  /**\n   * Handles the lazy creation of the MatButton ripple.\n   * Used to improve initial load time of large applications.\n   */\n  _rippleLoader = inject(MatRippleLoader);\n  /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n  _isFab = false;\n  /**\n   * Theme color of the button. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Whether the ripple effect is disabled or not. */\n  get disableRipple() {\n    return this._disableRipple;\n  }\n  set disableRipple(value) {\n    this._disableRipple = value;\n    this._updateRippleDisabled();\n  }\n  _disableRipple = false;\n  /** Whether the button is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._updateRippleDisabled();\n  }\n  _disabled = false;\n  /** `aria-disabled` value of the button. */\n  ariaDisabled;\n  /**\n   * Natively disabled buttons prevent focus and any pointer events from reaching the button.\n   * In some scenarios this might not be desirable, because it can prevent users from finding out\n   * why the button is disabled (e.g. via tooltip). This is also useful for buttons that may\n   * become disabled when activated, which would cause focus to be transferred to the document\n   * body instead of remaining on the button.\n   *\n   * Enabling this input will change the button so that it is styled to be disabled and will be\n   * marked as `aria-disabled`, but it will allow the button to receive events and focus.\n   *\n   * Note that by enabling this, you need to set the `tabindex` yourself if the button isn't\n   * meant to be tabbable and you have to prevent the button action (e.g. form submissions).\n   */\n  disabledInteractive;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const config = inject(MAT_BUTTON_CONFIG, {\n      optional: true\n    });\n    const element = this._elementRef.nativeElement;\n    const classList = element.classList;\n    this.disabledInteractive = config?.disabledInteractive ?? false;\n    this.color = config?.color ?? null;\n    this._rippleLoader?.configureRipple(element, {\n      className: 'mat-mdc-button-ripple'\n    });\n    // For each of the variant selectors that is present in the button's host\n    // attributes, add the correct corresponding MDC classes.\n    for (const {\n      attribute,\n      mdcClasses\n    } of HOST_SELECTOR_MDC_CLASS_PAIR) {\n      if (element.hasAttribute(attribute)) {\n        classList.add(...mdcClasses);\n      }\n    }\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n  }\n  /** Focuses the button. */\n  focus(origin = 'program', options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._elementRef.nativeElement, origin, options);\n    } else {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n  _getAriaDisabled() {\n    if (this.ariaDisabled != null) {\n      return this.ariaDisabled;\n    }\n    return this.disabled && this.disabledInteractive ? true : null;\n  }\n  _getDisabledAttribute() {\n    return this.disabledInteractive || !this.disabled ? null : true;\n  }\n  _updateRippleDisabled() {\n    this._rippleLoader?.setDisabled(this._elementRef.nativeElement, this.disableRipple || this.disabled);\n  }\n  static ɵfac = function MatButtonBase_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatButtonBase)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatButtonBase,\n    inputs: {\n      color: \"color\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      ariaDisabled: [2, \"aria-disabled\", \"ariaDisabled\", booleanAttribute],\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonBase, [{\n    type: Directive\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    ariaDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute,\n        alias: 'aria-disabled'\n      }]\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/** Shared host configuration for buttons using the `<a>` tag. */\nconst MAT_ANCHOR_HOST = {\n  // Note that this is basically a noop on anchors,\n  // but it appears that some internal apps depend on it.\n  '[attr.disabled]': '_getDisabledAttribute()',\n  '[class.mat-mdc-button-disabled]': 'disabled',\n  '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  // Note that we ignore the user-specified tabindex when it's disabled for\n  // consistency with the `mat-button` applied on native buttons where even\n  // though they have an index, they're not tabbable.\n  '[attr.tabindex]': 'disabled && !disabledInteractive ? -1 : tabIndex',\n  '[attr.aria-disabled]': '_getAriaDisabled()',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true',\n  '[class]': 'color ? \"mat-\" + color : \"\"'\n};\n/**\n * Anchor button base.\n */\nclass MatAnchorBase extends MatButtonBase {\n  _renderer = inject(Renderer2);\n  _cleanupClick;\n  tabIndex;\n  ngOnInit() {\n    this._ngZone.runOutsideAngular(() => {\n      this._cleanupClick = this._renderer.listen(this._elementRef.nativeElement, 'click', this._haltDisabledEvents);\n    });\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._cleanupClick?.();\n  }\n  _haltDisabledEvents = event => {\n    // A disabled button shouldn't apply any actions\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopImmediatePropagation();\n    }\n  };\n  _getAriaDisabled() {\n    if (this.ariaDisabled != null) {\n      return this.ariaDisabled;\n    }\n    return this.disabled || null;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatAnchorBase_BaseFactory;\n    return function MatAnchorBase_Factory(__ngFactoryType__) {\n      return (ɵMatAnchorBase_BaseFactory || (ɵMatAnchorBase_BaseFactory = i0.ɵɵgetInheritedFactory(MatAnchorBase)))(__ngFactoryType__ || MatAnchorBase);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatAnchorBase,\n    inputs: {\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => {\n        return value == null ? undefined : numberAttribute(value);\n      }]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAnchorBase, [{\n    type: Directive\n  }], null, {\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => {\n          return value == null ? undefined : numberAttribute(value);\n        }\n      }]\n    }]\n  });\n})();\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nclass MatIconButton extends MatButtonBase {\n  constructor() {\n    super();\n    this._rippleLoader.configureRipple(this._elementRef.nativeElement, {\n      centered: true\n    });\n  }\n  static ɵfac = function MatIconButton_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatIconButton)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatIconButton,\n    selectors: [[\"button\", \"mat-icon-button\", \"\"]],\n    hostVars: 14,\n    hostBindings: function MatIconButton_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx._getAriaDisabled());\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n      }\n    },\n    exportAs: [\"matButton\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c0,\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 0,\n    consts: [[1, \"mat-mdc-button-persistent-ripple\", \"mdc-icon-button__ripple\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n    template: function MatIconButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n      }\n    },\n    styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconButton, [{\n    type: Component,\n    args: [{\n      selector: `button[mat-icon-button]`,\n      host: MAT_BUTTON_HOST,\n      exportAs: 'matButton',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"]\n    }]\n  }], () => [], null);\n})();\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nclass MatIconAnchor extends MatAnchorBase {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatIconAnchor_BaseFactory;\n    return function MatIconAnchor_Factory(__ngFactoryType__) {\n      return (ɵMatIconAnchor_BaseFactory || (ɵMatIconAnchor_BaseFactory = i0.ɵɵgetInheritedFactory(MatIconAnchor)))(__ngFactoryType__ || MatIconAnchor);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatIconAnchor,\n    selectors: [[\"a\", \"mat-icon-button\", \"\"]],\n    hostVars: 15,\n    hostBindings: function MatIconAnchor_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"tabindex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx._getAriaDisabled());\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n      }\n    },\n    exportAs: [\"matButton\", \"matAnchor\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c0,\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 0,\n    consts: [[1, \"mat-mdc-button-persistent-ripple\", \"mdc-icon-button__ripple\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n    template: function MatIconAnchor_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n      }\n    },\n    styles: [_c2, _c3],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconAnchor, [{\n    type: Component,\n    args: [{\n      selector: `a[mat-icon-button]`,\n      host: MAT_ANCHOR_HOST,\n      exportAs: 'matButton, matAnchor',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { MatIconButton as M, MatButtonBase as a, MAT_BUTTON_HOST as b, MatAnchorBase as c, MAT_ANCHOR_HOST as d, MatIconAnchor as e, MAT_BUTTON_CONFIG as f };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Injectable } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction DIR_DOCUMENT_FACTORY() {\n  return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n  const value = rawValue?.toLowerCase() || '';\n  if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n  /** The current 'ltr' or 'rtl' value. */\n  value = 'ltr';\n  /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n  change = new EventEmitter();\n  constructor() {\n    const _document = inject(DIR_DOCUMENT, {\n      optional: true\n    });\n    if (_document) {\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n    }\n  }\n  ngOnDestroy() {\n    this.change.complete();\n  }\n  static ɵfac = function Directionality_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Directionality)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Directionality,\n    factory: Directionality.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Directionality, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { Directionality as D, _resolveDirectionality as _, DIR_DOCUMENT as a };\n", "import { _ as _resolveDirectionality, D as Directionality } from './directionality-CBXD4hga.mjs';\nexport { a as DIR_DOCUMENT } from './directionality-CBXD4hga.mjs';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport '@angular/common';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nclass Dir {\n  /** Normalized direction that accounts for invalid/unsupported values. */\n  _dir = 'ltr';\n  /** Whether the `value` has been set to its initial value. */\n  _isInitialized = false;\n  /** Direction as passed in by the consumer. */\n  _rawDir;\n  /** Event emitted when the direction changes. */\n  change = new EventEmitter();\n  /** @docs-private */\n  get dir() {\n    return this._dir;\n  }\n  set dir(value) {\n    const previousValue = this._dir;\n    // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n    // whereas the browser does it based on the content of the element. Since doing so based\n    // on the content can be expensive, for now we're doing the simpler matching.\n    this._dir = _resolveDirectionality(value);\n    this._rawDir = value;\n    if (previousValue !== this._dir && this._isInitialized) {\n      this.change.emit(this._dir);\n    }\n  }\n  /** Current layout direction of the element. */\n  get value() {\n    return this.dir;\n  }\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    this.change.complete();\n  }\n  static ɵfac = function Dir_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Dir)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Dir,\n    selectors: [[\"\", \"dir\", \"\"]],\n    hostVars: 1,\n    hostBindings: function Dir_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"dir\", ctx._rawDir);\n      }\n    },\n    inputs: {\n      dir: \"dir\"\n    },\n    outputs: {\n      change: \"dirChange\"\n    },\n    exportAs: [\"dir\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: Directionality,\n      useExisting: Dir\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dir, [{\n    type: Directive,\n    args: [{\n      selector: '[dir]',\n      providers: [{\n        provide: Directionality,\n        useExisting: Dir\n      }],\n      host: {\n        '[attr.dir]': '_rawDir'\n      },\n      exportAs: 'dir'\n    }]\n  }], null, {\n    change: [{\n      type: Output,\n      args: ['dirChange']\n    }],\n    dir: [{\n      type: Input\n    }]\n  });\n})();\nclass BidiModule {\n  static ɵfac = function BidiModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BidiModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BidiModule,\n    imports: [Dir],\n    exports: [Dir]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BidiModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Dir],\n      exports: [Dir]\n    }]\n  }], null, null);\n})();\nexport { BidiModule, Dir, Directionality };\n", "import { HighContrastModeDetector } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, NgModule } from '@angular/core';\n\n/**\n * Injection token that configures whether the Material sanity checks are enabled.\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nclass MatCommonModule {\n  constructor() {\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n  static ɵfac = function MatCommonModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCommonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCommonModule,\n    imports: [BidiModule],\n    exports: [BidiModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [BidiModule, BidiModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule],\n      exports: [BidiModule]\n    }]\n  }], () => [], null);\n})();\nexport { MatCommonModule as M, MATERIAL_SANITY_CHECKS as a };\n", "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nclass MatRippleModule {\n  static ɵfac = function MatRippleModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRippleModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatRippleModule,\n    imports: [MatCommonModule, MatRipple],\n    exports: [MatRipple, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRipple],\n      exports: [MatR<PERSON>ple, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatRippleModule as M };\n", "import { a as <PERSON><PERSON>uttonBase, b as MAT_BUTTON_HOST, c as Mat<PERSON>nchorBase, d as MAT_ANCHOR_HOST, e as MatIconAnchor, M as MatIconButton } from './icon-button-D1J0zeqv.mjs';\nconst _c0 = [\"mat-button\", \"\"];\nconst _c1 = [[[\"\", 8, \"material-icons\", 3, \"iconPositionEnd\", \"\"], [\"mat-icon\", 3, \"iconPositionEnd\", \"\"], [\"\", \"matButtonIcon\", \"\", 3, \"iconPositionEnd\", \"\"]], \"*\", [[\"\", \"iconPositionEnd\", \"\", 8, \"material-icons\"], [\"mat-icon\", \"iconPositionEnd\", \"\"], [\"\", \"matButtonIcon\", \"\", \"iconPositionEnd\", \"\"]]];\nconst _c2 = [\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\", \"*\", \".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\"];\nconst _c3 = \".mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 12px);height:var(--mdc-text-button-container-height, 40px);font-family:var(--mdc-text-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-sys-label-large-weight))}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-sys-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 16px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display, block)}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-filled-button-container-height, 40px);font-family:var(--mdc-filled-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-filled-button-horizontal-padding, 24px)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-sys-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-unelevated-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display, block)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-sys-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-sys-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-filled-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-sys-level1));height:var(--mdc-protected-button-container-height, 40px);font-family:var(--mdc-protected-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-protected-button-horizontal-padding, 24px)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-raised-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-raised-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display, block)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-sys-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-sys-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-sys-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-protected-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-sys-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-outlined-button-container-height, 40px);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-sys-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-sys-corner-full));border-width:var(--mdc-outlined-button-outline-width, 1px);padding:0 var(--mat-outlined-button-horizontal-padding, 24px)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-outlined-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-outlined-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display, block)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-sys-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-sys-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:var(--mdc-outlined-button-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-focus-indicator,.mat-mdc-unelevated-button .mat-focus-indicator,.mat-mdc-raised-button .mat-focus-indicator,.mat-mdc-outlined-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-button:focus>.mat-focus-indicator::before,.mat-mdc-unelevated-button:focus>.mat-focus-indicator::before,.mat-mdc-raised-button:focus>.mat-focus-indicator::before,.mat-mdc-outlined-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-focus-indicator::before,.mat-mdc-raised-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}\\n\";\nconst _c4 = \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\";\nconst _c5 = [\"mat-fab\", \"\"];\nconst _c6 = [\"mat-mini-fab\", \"\"];\nconst _c7 = \".mat-mdc-fab-base{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1);flex-shrink:0;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-fab-base .mat-mdc-button-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab-base .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-fab-base .mdc-button__label,.mat-mdc-fab-base .mat-icon{z-index:1;position:relative}.mat-mdc-fab-base .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab-base:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-fab-base._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab-base::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-fab-base[hidden]{display:none}.mat-mdc-fab-base::-moz-focus-inner{padding:0;border:0}.mat-mdc-fab-base:active,.mat-mdc-fab-base:focus{outline:none}.mat-mdc-fab-base:hover{cursor:pointer}.mat-mdc-fab-base>svg{width:100%}.mat-mdc-fab-base .mat-icon,.mat-mdc-fab-base .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab-base .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base[disabled]:focus,.mat-mdc-fab-base.mat-mdc-button-disabled,.mat-mdc-fab-base.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-fab-base.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab{background-color:var(--mdc-fab-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-container-shape, var(--mat-sys-corner-large));color:var(--mat-fab-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:hover{box-shadow:var(--mdc-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-fab:focus{box-shadow:var(--mdc-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:active,.mat-mdc-fab:focus:active{box-shadow:var(--mdc-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab[disabled],.mat-mdc-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-touch-target-display, block)}.mat-mdc-fab .mat-ripple-element{background-color:var(--mat-fab-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-disabled-state-layer-color)}.mat-mdc-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-mini-fab{width:40px;height:40px;background-color:var(--mdc-fab-small-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-small-container-shape, var(--mat-sys-corner-medium));color:var(--mat-fab-small-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-small-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:hover{box-shadow:var(--mdc-fab-small-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-mini-fab:focus{box-shadow:var(--mdc-fab-small-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:var(--mdc-fab-small-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab[disabled],.mat-mdc-mini-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-small-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-small-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-small-touch-target-display)}.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-fab-small-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-mini-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-disabled-state-layer-color)}.mat-mdc-mini-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-mini-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-mini-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-extended-fab{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;line-height:normal;height:var(--mdc-extended-fab-container-height, 56px);border-radius:var(--mdc-extended-fab-container-shape, var(--mat-sys-corner-large));font-family:var(--mdc-extended-fab-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-extended-fab-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-extended-fab-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-extended-fab-label-text-tracking, var(--mat-sys-label-large-tracking));box-shadow:var(--mdc-extended-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:hover{box-shadow:var(--mdc-extended-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-extended-fab:focus{box-shadow:var(--mdc-extended-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:active,.mat-mdc-extended-fab:focus:active{box-shadow:var(--mdc-extended-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab[disabled]:focus,.mat-mdc-extended-fab.mat-mdc-button-disabled,.mat-mdc-extended-fab.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-extended-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:-8px;margin-right:12px}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons,[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons{margin-left:12px;margin-right:-8px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}\\n\";\nexport { f as MAT_BUTTON_CONFIG } from './icon-button-D1J0zeqv.mjs';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, inject, booleanAttribute, Input, NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/private';\nimport './ripple-loader-Ce3DAhPW.mjs';\nimport '@angular/common';\nimport '@angular/cdk/platform';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-BQUT6wsL.mjs';\nimport '@angular/cdk/bidi';\n\n/**\n * Material Design button component. Users interact with a button to perform an action.\n * See https://material.io/components/buttons\n *\n * The `MatButton` class applies to native button elements and captures the appearances for\n * \"text button\", \"outlined button\", and \"contained button\" per the Material Design\n * specification. `MatButton` additionally captures an additional \"flat\" appearance, which matches\n * \"contained\" but without elevation.\n */\nclass MatButton extends MatButtonBase {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatButton_BaseFactory;\n    return function MatButton_Factory(__ngFactoryType__) {\n      return (ɵMatButton_BaseFactory || (ɵMatButton_BaseFactory = i0.ɵɵgetInheritedFactory(MatButton)))(__ngFactoryType__ || MatButton);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatButton,\n    selectors: [[\"button\", \"mat-button\", \"\"], [\"button\", \"mat-raised-button\", \"\"], [\"button\", \"mat-flat-button\", \"\"], [\"button\", \"mat-stroked-button\", \"\"]],\n    hostVars: 14,\n    hostBindings: function MatButton_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx._getAriaDisabled());\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n      }\n    },\n    exportAs: [\"matButton\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c0,\n    ngContentSelectors: _c2,\n    decls: 7,\n    vars: 4,\n    consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n    template: function MatButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementStart(2, \"span\", 1);\n        i0.ɵɵprojection(3, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(4, 2);\n        i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n      }\n    },\n    styles: [\".mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 12px);height:var(--mdc-text-button-container-height, 40px);font-family:var(--mdc-text-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-sys-label-large-weight))}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-sys-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 16px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display, block)}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-filled-button-container-height, 40px);font-family:var(--mdc-filled-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-filled-button-horizontal-padding, 24px)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-sys-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-unelevated-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display, block)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-sys-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-sys-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-filled-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-sys-level1));height:var(--mdc-protected-button-container-height, 40px);font-family:var(--mdc-protected-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-protected-button-horizontal-padding, 24px)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-raised-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-raised-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display, block)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-sys-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-sys-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-sys-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-protected-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-sys-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-outlined-button-container-height, 40px);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-sys-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-sys-corner-full));border-width:var(--mdc-outlined-button-outline-width, 1px);padding:0 var(--mat-outlined-button-horizontal-padding, 24px)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-outlined-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-outlined-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display, block)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-sys-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-sys-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:var(--mdc-outlined-button-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-focus-indicator,.mat-mdc-unelevated-button .mat-focus-indicator,.mat-mdc-raised-button .mat-focus-indicator,.mat-mdc-outlined-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-button:focus>.mat-focus-indicator::before,.mat-mdc-unelevated-button:focus>.mat-focus-indicator::before,.mat-mdc-raised-button:focus>.mat-focus-indicator::before,.mat-mdc-outlined-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-focus-indicator::before,.mat-mdc-raised-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButton, [{\n    type: Component,\n    args: [{\n      selector: `\n    button[mat-button], button[mat-raised-button], button[mat-flat-button],\n    button[mat-stroked-button]\n  `,\n      host: MAT_BUTTON_HOST,\n      exportAs: 'matButton',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span\\n    class=\\\"mat-mdc-button-persistent-ripple\\\"\\n    [class.mdc-button__ripple]=\\\"!_isFab\\\"\\n    [class.mdc-fab__ripple]=\\\"_isFab\\\"></span>\\n\\n<ng-content select=\\\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\\\">\\n</ng-content>\\n\\n<span class=\\\"mdc-button__label\\\"><ng-content></ng-content></span>\\n\\n<ng-content select=\\\".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\\\">\\n</ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 12px);height:var(--mdc-text-button-container-height, 40px);font-family:var(--mdc-text-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-sys-label-large-weight))}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-sys-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 16px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display, block)}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-filled-button-container-height, 40px);font-family:var(--mdc-filled-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-filled-button-horizontal-padding, 24px)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-sys-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-unelevated-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display, block)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-sys-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-sys-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-filled-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-sys-level1));height:var(--mdc-protected-button-container-height, 40px);font-family:var(--mdc-protected-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-protected-button-horizontal-padding, 24px)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-raised-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-raised-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display, block)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-sys-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-sys-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-sys-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-protected-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-sys-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-outlined-button-container-height, 40px);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-sys-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-sys-corner-full));border-width:var(--mdc-outlined-button-outline-width, 1px);padding:0 var(--mat-outlined-button-horizontal-padding, 24px)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-outlined-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-outlined-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display, block)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-sys-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-sys-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:var(--mdc-outlined-button-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-focus-indicator,.mat-mdc-unelevated-button .mat-focus-indicator,.mat-mdc-raised-button .mat-focus-indicator,.mat-mdc-outlined-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-button:focus>.mat-focus-indicator::before,.mat-mdc-unelevated-button:focus>.mat-focus-indicator::before,.mat-mdc-raised-button:focus>.mat-focus-indicator::before,.mat-mdc-outlined-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-focus-indicator::before,.mat-mdc-raised-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"]\n    }]\n  }], null, null);\n})();\n/**\n * Material Design button component for anchor elements. Anchor elements are used to provide\n * links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons\n *\n * The `MatAnchor` class applies to native anchor elements and captures the appearances for\n * \"text button\", \"outlined button\", and \"contained button\" per the Material Design\n * specification. `MatAnchor` additionally captures an additional \"flat\" appearance, which matches\n * \"contained\" but without elevation.\n */\nclass MatAnchor extends MatAnchorBase {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatAnchor_BaseFactory;\n    return function MatAnchor_Factory(__ngFactoryType__) {\n      return (ɵMatAnchor_BaseFactory || (ɵMatAnchor_BaseFactory = i0.ɵɵgetInheritedFactory(MatAnchor)))(__ngFactoryType__ || MatAnchor);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatAnchor,\n    selectors: [[\"a\", \"mat-button\", \"\"], [\"a\", \"mat-raised-button\", \"\"], [\"a\", \"mat-flat-button\", \"\"], [\"a\", \"mat-stroked-button\", \"\"]],\n    hostVars: 15,\n    hostBindings: function MatAnchor_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"tabindex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx._getAriaDisabled());\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n      }\n    },\n    exportAs: [\"matButton\", \"matAnchor\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c0,\n    ngContentSelectors: _c2,\n    decls: 7,\n    vars: 4,\n    consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n    template: function MatAnchor_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementStart(2, \"span\", 1);\n        i0.ɵɵprojection(3, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(4, 2);\n        i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n      }\n    },\n    styles: [_c3, _c4],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAnchor, [{\n    type: Component,\n    args: [{\n      selector: `a[mat-button], a[mat-raised-button], a[mat-flat-button], a[mat-stroked-button]`,\n      exportAs: 'matButton, matAnchor',\n      host: MAT_ANCHOR_HOST,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span\\n    class=\\\"mat-mdc-button-persistent-ripple\\\"\\n    [class.mdc-button__ripple]=\\\"!_isFab\\\"\\n    [class.mdc-fab__ripple]=\\\"_isFab\\\"></span>\\n\\n<ng-content select=\\\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\\\">\\n</ng-content>\\n\\n<span class=\\\"mdc-button__label\\\"><ng-content></ng-content></span>\\n\\n<ng-content select=\\\".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\\\">\\n</ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 12px);height:var(--mdc-text-button-container-height, 40px);font-family:var(--mdc-text-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-sys-label-large-weight))}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-sys-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 16px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display, block)}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-filled-button-container-height, 40px);font-family:var(--mdc-filled-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-filled-button-horizontal-padding, 24px)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-sys-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-unelevated-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display, block)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-sys-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-sys-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-filled-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-sys-level1));height:var(--mdc-protected-button-container-height, 40px);font-family:var(--mdc-protected-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-protected-button-horizontal-padding, 24px)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-raised-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-raised-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display, block)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-sys-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-sys-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-sys-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-protected-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-sys-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-outlined-button-container-height, 40px);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-sys-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-sys-corner-full));border-width:var(--mdc-outlined-button-outline-width, 1px);padding:0 var(--mat-outlined-button-horizontal-padding, 24px)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-outlined-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-outlined-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display, block)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-sys-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-sys-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:var(--mdc-outlined-button-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-focus-indicator,.mat-mdc-unelevated-button .mat-focus-indicator,.mat-mdc-raised-button .mat-focus-indicator,.mat-mdc-outlined-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-button:focus>.mat-focus-indicator::before,.mat-mdc-unelevated-button:focus>.mat-focus-indicator::before,.mat-mdc-raised-button:focus>.mat-focus-indicator::before,.mat-mdc-outlined-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-focus-indicator::before,.mat-mdc-raised-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/** Injection token to be used to override the default options for FAB. */\nconst MAT_FAB_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-fab-default-options', {\n  providedIn: 'root',\n  factory: MAT_FAB_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_FAB_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    // The FAB by default has its color set to accent.\n    color: 'accent'\n  };\n}\n// Default FAB configuration.\nconst defaults = MAT_FAB_DEFAULT_OPTIONS_FACTORY();\n/**\n * Material Design floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://material.io/components/buttons-floating-action-button/\n *\n * The `MatFabButton` class has two appearances: normal and extended.\n */\nclass MatFabButton extends MatButtonBase {\n  _options = inject(MAT_FAB_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _isFab = true;\n  extended;\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n  }\n  static ɵfac = function MatFabButton_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatFabButton)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatFabButton,\n    selectors: [[\"button\", \"mat-fab\", \"\"]],\n    hostVars: 18,\n    hostBindings: function MatFabButton_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx._getAriaDisabled());\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true)(\"mdc-fab--extended\", ctx.extended)(\"mat-mdc-extended-fab\", ctx.extended);\n      }\n    },\n    inputs: {\n      extended: [2, \"extended\", \"extended\", booleanAttribute]\n    },\n    exportAs: [\"matButton\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c5,\n    ngContentSelectors: _c2,\n    decls: 7,\n    vars: 4,\n    consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n    template: function MatFabButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementStart(2, \"span\", 1);\n        i0.ɵɵprojection(3, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(4, 2);\n        i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n      }\n    },\n    styles: [\".mat-mdc-fab-base{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1);flex-shrink:0;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-fab-base .mat-mdc-button-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab-base .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-fab-base .mdc-button__label,.mat-mdc-fab-base .mat-icon{z-index:1;position:relative}.mat-mdc-fab-base .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab-base:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-fab-base._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab-base::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-fab-base[hidden]{display:none}.mat-mdc-fab-base::-moz-focus-inner{padding:0;border:0}.mat-mdc-fab-base:active,.mat-mdc-fab-base:focus{outline:none}.mat-mdc-fab-base:hover{cursor:pointer}.mat-mdc-fab-base>svg{width:100%}.mat-mdc-fab-base .mat-icon,.mat-mdc-fab-base .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab-base .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base[disabled]:focus,.mat-mdc-fab-base.mat-mdc-button-disabled,.mat-mdc-fab-base.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-fab-base.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab{background-color:var(--mdc-fab-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-container-shape, var(--mat-sys-corner-large));color:var(--mat-fab-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:hover{box-shadow:var(--mdc-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-fab:focus{box-shadow:var(--mdc-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:active,.mat-mdc-fab:focus:active{box-shadow:var(--mdc-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab[disabled],.mat-mdc-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-touch-target-display, block)}.mat-mdc-fab .mat-ripple-element{background-color:var(--mat-fab-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-disabled-state-layer-color)}.mat-mdc-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-mini-fab{width:40px;height:40px;background-color:var(--mdc-fab-small-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-small-container-shape, var(--mat-sys-corner-medium));color:var(--mat-fab-small-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-small-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:hover{box-shadow:var(--mdc-fab-small-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-mini-fab:focus{box-shadow:var(--mdc-fab-small-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:var(--mdc-fab-small-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab[disabled],.mat-mdc-mini-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-small-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-small-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-small-touch-target-display)}.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-fab-small-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-mini-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-disabled-state-layer-color)}.mat-mdc-mini-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-mini-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-mini-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-extended-fab{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;line-height:normal;height:var(--mdc-extended-fab-container-height, 56px);border-radius:var(--mdc-extended-fab-container-shape, var(--mat-sys-corner-large));font-family:var(--mdc-extended-fab-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-extended-fab-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-extended-fab-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-extended-fab-label-text-tracking, var(--mat-sys-label-large-tracking));box-shadow:var(--mdc-extended-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:hover{box-shadow:var(--mdc-extended-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-extended-fab:focus{box-shadow:var(--mdc-extended-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:active,.mat-mdc-extended-fab:focus:active{box-shadow:var(--mdc-extended-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab[disabled]:focus,.mat-mdc-extended-fab.mat-mdc-button-disabled,.mat-mdc-extended-fab.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-extended-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:-8px;margin-right:12px}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons,[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons{margin-left:12px;margin-right:-8px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFabButton, [{\n    type: Component,\n    args: [{\n      selector: `button[mat-fab]`,\n      host: {\n        ...MAT_BUTTON_HOST,\n        '[class.mdc-fab--extended]': 'extended',\n        '[class.mat-mdc-extended-fab]': 'extended'\n      },\n      exportAs: 'matButton',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span\\n    class=\\\"mat-mdc-button-persistent-ripple\\\"\\n    [class.mdc-button__ripple]=\\\"!_isFab\\\"\\n    [class.mdc-fab__ripple]=\\\"_isFab\\\"></span>\\n\\n<ng-content select=\\\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\\\">\\n</ng-content>\\n\\n<span class=\\\"mdc-button__label\\\"><ng-content></ng-content></span>\\n\\n<ng-content select=\\\".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\\\">\\n</ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-fab-base{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1);flex-shrink:0;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-fab-base .mat-mdc-button-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab-base .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-fab-base .mdc-button__label,.mat-mdc-fab-base .mat-icon{z-index:1;position:relative}.mat-mdc-fab-base .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab-base:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-fab-base._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab-base::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-fab-base[hidden]{display:none}.mat-mdc-fab-base::-moz-focus-inner{padding:0;border:0}.mat-mdc-fab-base:active,.mat-mdc-fab-base:focus{outline:none}.mat-mdc-fab-base:hover{cursor:pointer}.mat-mdc-fab-base>svg{width:100%}.mat-mdc-fab-base .mat-icon,.mat-mdc-fab-base .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab-base .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base[disabled]:focus,.mat-mdc-fab-base.mat-mdc-button-disabled,.mat-mdc-fab-base.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-fab-base.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab{background-color:var(--mdc-fab-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-container-shape, var(--mat-sys-corner-large));color:var(--mat-fab-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:hover{box-shadow:var(--mdc-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-fab:focus{box-shadow:var(--mdc-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:active,.mat-mdc-fab:focus:active{box-shadow:var(--mdc-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab[disabled],.mat-mdc-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-touch-target-display, block)}.mat-mdc-fab .mat-ripple-element{background-color:var(--mat-fab-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-disabled-state-layer-color)}.mat-mdc-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-mini-fab{width:40px;height:40px;background-color:var(--mdc-fab-small-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-small-container-shape, var(--mat-sys-corner-medium));color:var(--mat-fab-small-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-small-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:hover{box-shadow:var(--mdc-fab-small-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-mini-fab:focus{box-shadow:var(--mdc-fab-small-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:var(--mdc-fab-small-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab[disabled],.mat-mdc-mini-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-small-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-small-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-small-touch-target-display)}.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-fab-small-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-mini-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-disabled-state-layer-color)}.mat-mdc-mini-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-mini-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-mini-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-extended-fab{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;line-height:normal;height:var(--mdc-extended-fab-container-height, 56px);border-radius:var(--mdc-extended-fab-container-shape, var(--mat-sys-corner-large));font-family:var(--mdc-extended-fab-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-extended-fab-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-extended-fab-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-extended-fab-label-text-tracking, var(--mat-sys-label-large-tracking));box-shadow:var(--mdc-extended-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:hover{box-shadow:var(--mdc-extended-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-extended-fab:focus{box-shadow:var(--mdc-extended-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:active,.mat-mdc-extended-fab:focus:active{box-shadow:var(--mdc-extended-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab[disabled]:focus,.mat-mdc-extended-fab.mat-mdc-button-disabled,.mat-mdc-extended-fab.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-extended-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:-8px;margin-right:12px}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons,[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons{margin-left:12px;margin-right:-8px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}\\n\"]\n    }]\n  }], () => [], {\n    extended: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Material Design mini floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://material.io/components/buttons-floating-action-button/\n */\nclass MatMiniFabButton extends MatButtonBase {\n  _options = inject(MAT_FAB_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _isFab = true;\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n  }\n  static ɵfac = function MatMiniFabButton_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatMiniFabButton)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatMiniFabButton,\n    selectors: [[\"button\", \"mat-mini-fab\", \"\"]],\n    hostVars: 14,\n    hostBindings: function MatMiniFabButton_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx._getAriaDisabled());\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n      }\n    },\n    exportAs: [\"matButton\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c6,\n    ngContentSelectors: _c2,\n    decls: 7,\n    vars: 4,\n    consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n    template: function MatMiniFabButton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementStart(2, \"span\", 1);\n        i0.ɵɵprojection(3, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(4, 2);\n        i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n      }\n    },\n    styles: [_c7],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMiniFabButton, [{\n    type: Component,\n    args: [{\n      selector: `button[mat-mini-fab]`,\n      host: MAT_BUTTON_HOST,\n      exportAs: 'matButton',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span\\n    class=\\\"mat-mdc-button-persistent-ripple\\\"\\n    [class.mdc-button__ripple]=\\\"!_isFab\\\"\\n    [class.mdc-fab__ripple]=\\\"_isFab\\\"></span>\\n\\n<ng-content select=\\\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\\\">\\n</ng-content>\\n\\n<span class=\\\"mdc-button__label\\\"><ng-content></ng-content></span>\\n\\n<ng-content select=\\\".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\\\">\\n</ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-fab-base{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1);flex-shrink:0;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-fab-base .mat-mdc-button-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab-base .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-fab-base .mdc-button__label,.mat-mdc-fab-base .mat-icon{z-index:1;position:relative}.mat-mdc-fab-base .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab-base:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-fab-base._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab-base::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-fab-base[hidden]{display:none}.mat-mdc-fab-base::-moz-focus-inner{padding:0;border:0}.mat-mdc-fab-base:active,.mat-mdc-fab-base:focus{outline:none}.mat-mdc-fab-base:hover{cursor:pointer}.mat-mdc-fab-base>svg{width:100%}.mat-mdc-fab-base .mat-icon,.mat-mdc-fab-base .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab-base .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base[disabled]:focus,.mat-mdc-fab-base.mat-mdc-button-disabled,.mat-mdc-fab-base.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-fab-base.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab{background-color:var(--mdc-fab-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-container-shape, var(--mat-sys-corner-large));color:var(--mat-fab-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:hover{box-shadow:var(--mdc-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-fab:focus{box-shadow:var(--mdc-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:active,.mat-mdc-fab:focus:active{box-shadow:var(--mdc-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab[disabled],.mat-mdc-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-touch-target-display, block)}.mat-mdc-fab .mat-ripple-element{background-color:var(--mat-fab-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-disabled-state-layer-color)}.mat-mdc-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-mini-fab{width:40px;height:40px;background-color:var(--mdc-fab-small-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-small-container-shape, var(--mat-sys-corner-medium));color:var(--mat-fab-small-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-small-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:hover{box-shadow:var(--mdc-fab-small-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-mini-fab:focus{box-shadow:var(--mdc-fab-small-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:var(--mdc-fab-small-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab[disabled],.mat-mdc-mini-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-small-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-small-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-small-touch-target-display)}.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-fab-small-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-mini-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-disabled-state-layer-color)}.mat-mdc-mini-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-mini-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-mini-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-extended-fab{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;line-height:normal;height:var(--mdc-extended-fab-container-height, 56px);border-radius:var(--mdc-extended-fab-container-shape, var(--mat-sys-corner-large));font-family:var(--mdc-extended-fab-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-extended-fab-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-extended-fab-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-extended-fab-label-text-tracking, var(--mat-sys-label-large-tracking));box-shadow:var(--mdc-extended-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:hover{box-shadow:var(--mdc-extended-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-extended-fab:focus{box-shadow:var(--mdc-extended-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:active,.mat-mdc-extended-fab:focus:active{box-shadow:var(--mdc-extended-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab[disabled]:focus,.mat-mdc-extended-fab.mat-mdc-button-disabled,.mat-mdc-extended-fab.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-extended-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:-8px;margin-right:12px}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons,[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons{margin-left:12px;margin-right:-8px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}\\n\"]\n    }]\n  }], () => [], null);\n})();\n/**\n * Material Design floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons-floating-action-button/\n *\n * The `MatFabAnchor` class has two appearances: normal and extended.\n */\nclass MatFabAnchor extends MatAnchor {\n  _options = inject(MAT_FAB_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _isFab = true;\n  extended;\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n  }\n  static ɵfac = function MatFabAnchor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatFabAnchor)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatFabAnchor,\n    selectors: [[\"a\", \"mat-fab\", \"\"]],\n    hostVars: 19,\n    hostBindings: function MatFabAnchor_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"tabindex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx._getAriaDisabled());\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true)(\"mdc-fab--extended\", ctx.extended)(\"mat-mdc-extended-fab\", ctx.extended);\n      }\n    },\n    inputs: {\n      extended: [2, \"extended\", \"extended\", booleanAttribute]\n    },\n    exportAs: [\"matButton\", \"matAnchor\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c5,\n    ngContentSelectors: _c2,\n    decls: 7,\n    vars: 4,\n    consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n    template: function MatFabAnchor_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementStart(2, \"span\", 1);\n        i0.ɵɵprojection(3, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(4, 2);\n        i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n      }\n    },\n    styles: [_c7],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFabAnchor, [{\n    type: Component,\n    args: [{\n      selector: `a[mat-fab]`,\n      host: {\n        ...MAT_ANCHOR_HOST,\n        '[class.mdc-fab--extended]': 'extended',\n        '[class.mat-mdc-extended-fab]': 'extended'\n      },\n      exportAs: 'matButton, matAnchor',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span\\n    class=\\\"mat-mdc-button-persistent-ripple\\\"\\n    [class.mdc-button__ripple]=\\\"!_isFab\\\"\\n    [class.mdc-fab__ripple]=\\\"_isFab\\\"></span>\\n\\n<ng-content select=\\\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\\\">\\n</ng-content>\\n\\n<span class=\\\"mdc-button__label\\\"><ng-content></ng-content></span>\\n\\n<ng-content select=\\\".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\\\">\\n</ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-fab-base{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1);flex-shrink:0;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-fab-base .mat-mdc-button-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab-base .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-fab-base .mdc-button__label,.mat-mdc-fab-base .mat-icon{z-index:1;position:relative}.mat-mdc-fab-base .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab-base:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-fab-base._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab-base::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-fab-base[hidden]{display:none}.mat-mdc-fab-base::-moz-focus-inner{padding:0;border:0}.mat-mdc-fab-base:active,.mat-mdc-fab-base:focus{outline:none}.mat-mdc-fab-base:hover{cursor:pointer}.mat-mdc-fab-base>svg{width:100%}.mat-mdc-fab-base .mat-icon,.mat-mdc-fab-base .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab-base .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base[disabled]:focus,.mat-mdc-fab-base.mat-mdc-button-disabled,.mat-mdc-fab-base.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-fab-base.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab{background-color:var(--mdc-fab-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-container-shape, var(--mat-sys-corner-large));color:var(--mat-fab-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:hover{box-shadow:var(--mdc-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-fab:focus{box-shadow:var(--mdc-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:active,.mat-mdc-fab:focus:active{box-shadow:var(--mdc-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab[disabled],.mat-mdc-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-touch-target-display, block)}.mat-mdc-fab .mat-ripple-element{background-color:var(--mat-fab-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-disabled-state-layer-color)}.mat-mdc-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-mini-fab{width:40px;height:40px;background-color:var(--mdc-fab-small-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-small-container-shape, var(--mat-sys-corner-medium));color:var(--mat-fab-small-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-small-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:hover{box-shadow:var(--mdc-fab-small-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-mini-fab:focus{box-shadow:var(--mdc-fab-small-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:var(--mdc-fab-small-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab[disabled],.mat-mdc-mini-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-small-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-small-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-small-touch-target-display)}.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-fab-small-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-mini-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-disabled-state-layer-color)}.mat-mdc-mini-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-mini-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-mini-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-extended-fab{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;line-height:normal;height:var(--mdc-extended-fab-container-height, 56px);border-radius:var(--mdc-extended-fab-container-shape, var(--mat-sys-corner-large));font-family:var(--mdc-extended-fab-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-extended-fab-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-extended-fab-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-extended-fab-label-text-tracking, var(--mat-sys-label-large-tracking));box-shadow:var(--mdc-extended-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:hover{box-shadow:var(--mdc-extended-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-extended-fab:focus{box-shadow:var(--mdc-extended-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:active,.mat-mdc-extended-fab:focus:active{box-shadow:var(--mdc-extended-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab[disabled]:focus,.mat-mdc-extended-fab.mat-mdc-button-disabled,.mat-mdc-extended-fab.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-extended-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:-8px;margin-right:12px}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons,[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons{margin-left:12px;margin-right:-8px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}\\n\"]\n    }]\n  }], () => [], {\n    extended: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Material Design mini floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons-floating-action-button/\n */\nclass MatMiniFabAnchor extends MatAnchor {\n  _options = inject(MAT_FAB_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _isFab = true;\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n  }\n  static ɵfac = function MatMiniFabAnchor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatMiniFabAnchor)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatMiniFabAnchor,\n    selectors: [[\"a\", \"mat-mini-fab\", \"\"]],\n    hostVars: 15,\n    hostBindings: function MatMiniFabAnchor_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"tabindex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx._getAriaDisabled());\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n      }\n    },\n    exportAs: [\"matButton\", \"matAnchor\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c6,\n    ngContentSelectors: _c2,\n    decls: 7,\n    vars: 4,\n    consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n    template: function MatMiniFabAnchor_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementStart(2, \"span\", 1);\n        i0.ɵɵprojection(3, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(4, 2);\n        i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n      }\n    },\n    styles: [_c7],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMiniFabAnchor, [{\n    type: Component,\n    args: [{\n      selector: `a[mat-mini-fab]`,\n      host: MAT_ANCHOR_HOST,\n      exportAs: 'matButton, matAnchor',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span\\n    class=\\\"mat-mdc-button-persistent-ripple\\\"\\n    [class.mdc-button__ripple]=\\\"!_isFab\\\"\\n    [class.mdc-fab__ripple]=\\\"_isFab\\\"></span>\\n\\n<ng-content select=\\\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\\\">\\n</ng-content>\\n\\n<span class=\\\"mdc-button__label\\\"><ng-content></ng-content></span>\\n\\n<ng-content select=\\\".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\\\">\\n</ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-fab-base{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1);flex-shrink:0;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-fab-base .mat-mdc-button-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab-base .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-fab-base .mdc-button__label,.mat-mdc-fab-base .mat-icon{z-index:1;position:relative}.mat-mdc-fab-base .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab-base:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-fab-base._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab-base::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-fab-base[hidden]{display:none}.mat-mdc-fab-base::-moz-focus-inner{padding:0;border:0}.mat-mdc-fab-base:active,.mat-mdc-fab-base:focus{outline:none}.mat-mdc-fab-base:hover{cursor:pointer}.mat-mdc-fab-base>svg{width:100%}.mat-mdc-fab-base .mat-icon,.mat-mdc-fab-base .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab-base .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base[disabled]:focus,.mat-mdc-fab-base.mat-mdc-button-disabled,.mat-mdc-fab-base.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-fab-base.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab{background-color:var(--mdc-fab-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-container-shape, var(--mat-sys-corner-large));color:var(--mat-fab-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:hover{box-shadow:var(--mdc-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-fab:focus{box-shadow:var(--mdc-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:active,.mat-mdc-fab:focus:active{box-shadow:var(--mdc-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab[disabled],.mat-mdc-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-touch-target-display, block)}.mat-mdc-fab .mat-ripple-element{background-color:var(--mat-fab-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-disabled-state-layer-color)}.mat-mdc-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-mini-fab{width:40px;height:40px;background-color:var(--mdc-fab-small-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-small-container-shape, var(--mat-sys-corner-medium));color:var(--mat-fab-small-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-small-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:hover{box-shadow:var(--mdc-fab-small-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-mini-fab:focus{box-shadow:var(--mdc-fab-small-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:var(--mdc-fab-small-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab[disabled],.mat-mdc-mini-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-small-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-small-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-small-touch-target-display)}.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-fab-small-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-mini-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-disabled-state-layer-color)}.mat-mdc-mini-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-mini-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-mini-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-extended-fab{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;line-height:normal;height:var(--mdc-extended-fab-container-height, 56px);border-radius:var(--mdc-extended-fab-container-shape, var(--mat-sys-corner-large));font-family:var(--mdc-extended-fab-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-extended-fab-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-extended-fab-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-extended-fab-label-text-tracking, var(--mat-sys-label-large-tracking));box-shadow:var(--mdc-extended-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:hover{box-shadow:var(--mdc-extended-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-extended-fab:focus{box-shadow:var(--mdc-extended-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:active,.mat-mdc-extended-fab:focus:active{box-shadow:var(--mdc-extended-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab[disabled]:focus,.mat-mdc-extended-fab.mat-mdc-button-disabled,.mat-mdc-extended-fab.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-extended-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:-8px;margin-right:12px}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons,[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons{margin-left:12px;margin-right:-8px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}\\n\"]\n    }]\n  }], () => [], null);\n})();\nclass MatButtonModule {\n  static ɵfac = function MatButtonModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatButtonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatButtonModule,\n    imports: [MatCommonModule, MatRippleModule, MatAnchor, MatButton, MatIconAnchor, MatMiniFabAnchor, MatMiniFabButton, MatIconButton, MatFabAnchor, MatFabButton],\n    exports: [MatAnchor, MatButton, MatIconAnchor, MatIconButton, MatMiniFabAnchor, MatMiniFabButton, MatFabAnchor, MatFabButton, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatRippleModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRippleModule, MatAnchor, MatButton, MatIconAnchor, MatMiniFabAnchor, MatMiniFabButton, MatIconButton, MatFabAnchor, MatFabButton],\n      exports: [MatAnchor, MatButton, MatIconAnchor, MatIconButton, MatMiniFabAnchor, MatMiniFabButton, MatFabAnchor, MatFabButton, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_FAB_DEFAULT_OPTIONS, MAT_FAB_DEFAULT_OPTIONS_FACTORY, MatAnchor, MatButton, MatButtonModule, MatFabAnchor, MatFabButton, MatIconAnchor, MatIconButton, MatMiniFabAnchor, MatMiniFabButton };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAS,gCAAgC,OAAO;AAM9C,SAAO,MAAM,YAAY,KAAK,MAAM,WAAW;AACjD;AAEA,SAAS,iCAAiC,OAAO;AAC/C,QAAM,QAAQ,MAAM,WAAW,MAAM,QAAQ,CAAC,KAAK,MAAM,kBAAkB,MAAM,eAAe,CAAC;AAKjG,SAAO,CAAC,CAAC,SAAS,MAAM,eAAe,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY;AAC7I;;;AChBA,IAAM,YAAY;AAClB,IAAM,MAAM;AAEZ,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,MAAM;AAGZ,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,IAAM,aAAa;AAInB,IAAM,SAAS;AACf,IAAM,OAAO;AASb,IAAM,OAAO;AAKb,IAAM,IAAI;AAyBV,IAAM,IAAI;AACV,IAAM,OAAO;AAsDb,IAAM,WAAW;;;ACtHjB,IAAI;AAEJ,SAAS,qBAAqB;AAC5B,MAAI,wBAAwB,MAAM;AAChC,UAAM,OAAO,OAAO,aAAa,cAAc,SAAS,OAAO;AAC/D,2BAAuB,CAAC,EAAE,SAAS,KAAK,oBAAoB,KAAK;AAAA,EACnE;AACA,SAAO;AACT;AAEA,SAAS,eAAe,SAAS;AAC/B,MAAI,mBAAmB,GAAG;AACxB,UAAM,WAAW,QAAQ,cAAc,QAAQ,YAAY,IAAI;AAG/D,QAAI,OAAO,eAAe,eAAe,cAAc,oBAAoB,YAAY;AACrF,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,oCAAoC;AAC3C,MAAI,gBAAgB,OAAO,aAAa,eAAe,WAAW,SAAS,gBAAgB;AAC3F,SAAO,iBAAiB,cAAc,YAAY;AAChD,UAAM,mBAAmB,cAAc,WAAW;AAClD,QAAI,qBAAqB,eAAe;AACtC;AAAA,IACF,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,OAAO;AAG9B,SAAO,MAAM,eAAe,MAAM,aAAa,EAAE,CAAC,IAAI,MAAM;AAC9D;;;ACjCA,SAAS,sBAAsB,UAAU,QAAQ,WAAW,UAAU,SAAS;AAC7E,QAAM,QAAQ,SAAS,QAAQ,KAAK;AACpC,QAAM,QAAQ,SAAS,QAAQ,KAAK;AAGpC,MAAI,QAAQ,MAAM,UAAU,MAAM,QAAQ,KAAK,UAAU,KAAK,UAAU,GAAG;AACzE,WAAO,SAAS,OAAO,QAAQ,WAAW,UAAU,OAAO;AAAA,EAC7D;AACA,SAAO,iBAAiB,WAAW,UAAU,OAAO;AACpD,SAAO,MAAM;AACX,WAAO,oBAAoB,WAAW,UAAU,OAAO;AAAA,EACzD;AACF;;;ACfA,IAAI;AAMJ,IAAI;AACF,uBAAqB,OAAO,SAAS,eAAe,KAAK;AAC3D,QAAQ;AACN,uBAAqB;AACvB;AAKA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,cAAc,OAAO,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,YAAY,KAAK,cAAc,kBAAkB,KAAK,WAAW,IAAI,OAAO,aAAa,YAAY,CAAC,CAAC;AAAA;AAAA,EAEvG,OAAO,KAAK,aAAa,UAAU,KAAK,UAAU,SAAS;AAAA;AAAA,EAE3D,UAAU,KAAK,aAAa,kBAAkB,KAAK,UAAU,SAAS;AAAA;AAAA;AAAA,EAGtE,QAAQ,KAAK,aAAa,CAAC,EAAE,OAAO,UAAU,uBAAuB,OAAO,QAAQ,eAAe,CAAC,KAAK,QAAQ,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA,EAIvH,SAAS,KAAK,aAAa,eAAe,KAAK,UAAU,SAAS,KAAK,CAAC,KAAK,SAAS,CAAC,KAAK,QAAQ,CAAC,KAAK;AAAA;AAAA,EAE1G,MAAM,KAAK,aAAa,mBAAmB,KAAK,UAAU,SAAS,KAAK,EAAE,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxF,UAAU,KAAK,aAAa,uBAAuB,KAAK,UAAU,SAAS;AAAA;AAAA;AAAA,EAG3E,UAAU,KAAK,aAAa,WAAW,KAAK,UAAU,SAAS,KAAK,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1E,SAAS,KAAK,aAAa,UAAU,KAAK,UAAU,SAAS,KAAK,KAAK;AAAA,EACvE,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,YAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,UAAS;AAAA,IAClB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACvEH,IAAI;AAKJ,SAAS,gCAAgC;AACvC,MAAI,yBAAyB,QAAQ,OAAO,WAAW,aAAa;AAClE,QAAI;AACF,aAAO,iBAAiB,QAAQ,MAAM,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,QACzE,KAAK,MAAM,wBAAwB;AAAA,MACrC,CAAC,CAAC;AAAA,IACJ,UAAE;AACA,8BAAwB,yBAAyB;AAAA,IACnD;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,gCAAgC,SAAS;AAChD,SAAO,8BAA8B,IAAI,UAAU,CAAC,CAAC,QAAQ;AAC/D;;;ACzBA,SAAS,qBAAqB,OAAO,gBAAgB,GAAG;AACtD,MAAI,eAAe,KAAK,GAAG;AACzB,WAAO,OAAO,KAAK;AAAA,EACrB;AACA,SAAO,UAAU,WAAW,IAAI,gBAAgB;AAClD;AAKA,SAAS,eAAe,OAAO;AAI7B,SAAO,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AAC1D;AAMA,SAAS,cAAc,cAAc;AACnC,SAAO,wBAAwB,aAAa,aAAa,gBAAgB;AAC3E;;;ACPA,IAAM,kCAAkC,IAAI,eAAe,qCAAqC;AAiBhG,IAAM,0CAA0C;AAAA,EAC9C,YAAY,CAAC,KAAK,SAAS,UAAU,MAAM,KAAK;AAClD;AAQA,IAAM,kBAAkB;AAKxB,IAAM,+BAA+B;AAAA,EACnC,SAAS;AAAA,EACT,SAAS;AACX;AAeA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,qBAAqB;AACvB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAAA;AAAA,EAEpB,YAAY,IAAI,gBAAgB,IAAI;AAAA;AAAA,EAEpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,aAAa,WAAS;AAGpB,QAAI,KAAK,UAAU,YAAY,KAAK,aAAW,YAAY,MAAM,OAAO,GAAG;AACzE;AAAA,IACF;AACA,SAAK,UAAU,KAAK,UAAU;AAC9B,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,WAAS;AAItB,QAAI,KAAK,IAAI,IAAI,KAAK,eAAe,iBAAiB;AACpD;AAAA,IACF;AAGA,SAAK,UAAU,KAAK,gCAAgC,KAAK,IAAI,aAAa,OAAO;AACjF,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,WAAS;AAGvB,QAAI,iCAAiC,KAAK,GAAG;AAC3C,WAAK,UAAU,KAAK,UAAU;AAC9B;AAAA,IACF;AAGA,SAAK,eAAe,KAAK,IAAI;AAC7B,SAAK,UAAU,KAAK,OAAO;AAC3B,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAMA,YAAW,OAAO,QAAQ;AAChC,UAAM,UAAU,OAAO,iCAAiC;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,WAAW,kCACX,0CACA;AAGL,SAAK,mBAAmB,KAAK,UAAU,KAAK,KAAK,CAAC,CAAC;AACnD,SAAK,kBAAkB,KAAK,iBAAiB,KAAK,qBAAqB,CAAC;AAGxE,QAAI,KAAK,UAAU,WAAW;AAC5B,YAAM,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AACnE,WAAK,oBAAoB,OAAO,kBAAkB,MAAM;AACtD,eAAO,CAAC,sBAAsB,UAAUA,WAAU,WAAW,KAAK,YAAY,4BAA4B,GAAG,sBAAsB,UAAUA,WAAU,aAAa,KAAK,cAAc,4BAA4B,GAAG,sBAAsB,UAAUA,WAAU,cAAc,KAAK,eAAe,4BAA4B,CAAC;AAAA,MACjU,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,SAAS;AACxB,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AAAA,EACtD;AAAA,EACA,OAAO,YAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,IAC/B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,4BAA2B;AAMpC,EAAAA,2BAA0BA,2BAA0B,WAAW,IAAI,CAAC,IAAI;AAKxE,EAAAA,2BAA0BA,2BAA0B,UAAU,IAAI,CAAC,IAAI;AACzE,GAAG,8BAA8B,4BAA4B,CAAC,EAAE;AAEhE,IAAM,gCAAgC,IAAI,eAAe,mCAAmC;AAK5F,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,yBAAyB,OAAO,qBAAqB;AAAA;AAAA,EAErD,UAAU;AAAA;AAAA,EAEV;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,8BAA8B;AAAA;AAAA,EAE9B,eAAe,oBAAI,IAAI;AAAA;AAAA,EAEvB,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzB,8BAA8B,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,MAAM;AAG3B,SAAK,iBAAiB;AACtB,SAAK,wBAAwB,WAAW,MAAM,KAAK,iBAAiB,KAAK;AAAA,EAC3E;AAAA;AAAA,EAEA,YAAY,OAAO,UAAU;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,6BAA6B,IAAI,QAAQ;AAAA,EACzC,cAAc;AACZ,UAAM,UAAU,OAAO,+BAA+B;AAAA,MACpD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,iBAAiB,SAAS,iBAAiB,0BAA0B;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gCAAgC,WAAS;AACvC,UAAM,SAAS,gBAAgB,KAAK;AAEpC,aAAS,UAAU,QAAQ,SAAS,UAAU,QAAQ,eAAe;AACnE,UAAI,MAAM,SAAS,SAAS;AAC1B,aAAK,SAAS,OAAO,OAAO;AAAA,MAC9B,OAAO;AACL,aAAK,QAAQ,OAAO,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,gBAAgB,OAAO;AACtC,UAAM,gBAAgB,cAAc,OAAO;AAE3C,QAAI,CAAC,KAAK,UAAU,aAAa,cAAc,aAAa,GAAG;AAE7D,aAAO,GAAG;AAAA,IACZ;AAIA,UAAM,WAAW,eAAe,aAAa,KAAK,KAAK,aAAa;AACpE,UAAM,aAAa,KAAK,aAAa,IAAI,aAAa;AAEtD,QAAI,YAAY;AACd,UAAI,eAAe;AAIjB,mBAAW,gBAAgB;AAAA,MAC7B;AACA,aAAO,WAAW;AAAA,IACpB;AAEA,UAAM,OAAO;AAAA,MACX;AAAA,MACA,SAAS,IAAI,QAAQ;AAAA,MACrB;AAAA,IACF;AACA,SAAK,aAAa,IAAI,eAAe,IAAI;AACzC,SAAK,yBAAyB,IAAI;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,SAAS;AACtB,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,cAAc,KAAK,aAAa,IAAI,aAAa;AACvD,QAAI,aAAa;AACf,kBAAY,QAAQ,SAAS;AAC7B,WAAK,YAAY,aAAa;AAC9B,WAAK,aAAa,OAAO,aAAa;AACtC,WAAK,uBAAuB,WAAW;AAAA,IACzC;AAAA,EACF;AAAA,EACA,SAAS,SAAS,QAAQ,SAAS;AACjC,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,iBAAiB,KAAK,aAAa,EAAE;AAI3C,QAAI,kBAAkB,gBAAgB;AACpC,WAAK,wBAAwB,aAAa,EAAE,QAAQ,CAAC,CAAC,gBAAgB,IAAI,MAAM,KAAK,eAAe,gBAAgB,QAAQ,IAAI,CAAC;AAAA,IACnI,OAAO;AACL,WAAK,WAAW,MAAM;AAEtB,UAAI,OAAO,cAAc,UAAU,YAAY;AAC7C,sBAAc,MAAM,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ,CAAC,OAAO,YAAY,KAAK,eAAe,OAAO,CAAC;AAAA,EAC5E;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,UAAM,MAAM,KAAK,aAAa;AAC9B,WAAO,IAAI,eAAe;AAAA,EAC5B;AAAA,EACA,gBAAgB,kBAAkB;AAChC,QAAI,KAAK,SAAS;AAGhB,UAAI,KAAK,6BAA6B;AACpC,eAAO,KAAK,2BAA2B,gBAAgB,IAAI,UAAU;AAAA,MACvE,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAUA,QAAI,KAAK,kBAAkB,KAAK,kBAAkB;AAChD,aAAO,KAAK;AAAA,IACd;AAKA,QAAI,oBAAoB,KAAK,iCAAiC,gBAAgB,GAAG;AAC/E,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,2BAA2B,kBAAkB;AAW3C,WAAO,KAAK,mBAAmB,0BAA0B,YAAY,CAAC,CAAC,kBAAkB,SAAS,KAAK,uBAAuB,iBAAiB;AAAA,EACjJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS,QAAQ;AAC3B,YAAQ,UAAU,OAAO,eAAe,CAAC,CAAC,MAAM;AAChD,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,wBAAwB,WAAW,UAAU;AACtE,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,uBAAuB,WAAW,SAAS;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,QAAQ,oBAAoB,OAAO;AAC5C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,UAAU;AACf,WAAK,8BAA8B,WAAW,WAAW;AAMzD,UAAI,KAAK,mBAAmB,0BAA0B,WAAW;AAC/D,qBAAa,KAAK,gBAAgB;AAClC,cAAM,KAAK,KAAK,8BAA8B,kBAAkB;AAChE,aAAK,mBAAmB,WAAW,MAAM,KAAK,UAAU,MAAM,EAAE;AAAA,MAClE;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO,SAAS;AAOvB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,UAAM,mBAAmB,gBAAgB,KAAK;AAC9C,QAAI,CAAC,eAAe,CAAC,YAAY,iBAAiB,YAAY,kBAAkB;AAC9E;AAAA,IACF;AACA,SAAK,eAAe,SAAS,KAAK,gBAAgB,gBAAgB,GAAG,WAAW;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO,SAAS;AAGtB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,QAAI,CAAC,eAAe,YAAY,iBAAiB,MAAM,yBAAyB,QAAQ,QAAQ,SAAS,MAAM,aAAa,GAAG;AAC7H;AAAA,IACF;AACA,SAAK,YAAY,OAAO;AACxB,SAAK,YAAY,aAAa,IAAI;AAAA,EACpC;AAAA,EACA,YAAY,MAAM,QAAQ;AACxB,QAAI,KAAK,QAAQ,UAAU,QAAQ;AACjC,WAAK,QAAQ,IAAI,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IAClD;AAAA,EACF;AAAA,EACA,yBAAyB,aAAa;AACpC,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B;AAAA,IACF;AACA,UAAM,WAAW,YAAY;AAC7B,UAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ,KAAK;AACjF,QAAI,CAAC,wBAAwB;AAC3B,WAAK,QAAQ,kBAAkB,MAAM;AACnC,iBAAS,iBAAiB,SAAS,KAAK,+BAA+B,2BAA2B;AAClG,iBAAS,iBAAiB,QAAQ,KAAK,+BAA+B,2BAA2B;AAAA,MACnG,CAAC;AAAA,IACH;AACA,SAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAEzE,QAAI,EAAE,KAAK,2BAA2B,GAAG;AAGvC,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAMC,UAAS,KAAK,WAAW;AAC/B,QAAAA,QAAO,iBAAiB,SAAS,KAAK,oBAAoB;AAAA,MAC5D,CAAC;AAED,WAAK,uBAAuB,iBAAiB,KAAK,UAAU,KAAK,0BAA0B,CAAC,EAAE,UAAU,cAAY;AAClH,aAAK;AAAA,UAAW;AAAA,UAAU;AAAA;AAAA,QAA4B;AAAA,MACxD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,uBAAuB,aAAa;AAClC,UAAM,WAAW,YAAY;AAC7B,QAAI,KAAK,4BAA4B,IAAI,QAAQ,GAAG;AAClD,YAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ;AAC5E,UAAI,yBAAyB,GAAG;AAC9B,aAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAAA,MAC3E,OAAO;AACL,iBAAS,oBAAoB,SAAS,KAAK,+BAA+B,2BAA2B;AACrG,iBAAS,oBAAoB,QAAQ,KAAK,+BAA+B,2BAA2B;AACpG,aAAK,4BAA4B,OAAO,QAAQ;AAAA,MAClD;AAAA,IACF;AAEA,QAAI,CAAE,EAAE,KAAK,wBAAwB;AACnC,YAAMA,UAAS,KAAK,WAAW;AAC/B,MAAAA,QAAO,oBAAoB,SAAS,KAAK,oBAAoB;AAE7D,WAAK,2BAA2B,KAAK;AAErC,mBAAa,KAAK,qBAAqB;AACvC,mBAAa,KAAK,gBAAgB;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,SAAS,QAAQ,aAAa;AAC3C,SAAK,YAAY,SAAS,MAAM;AAChC,SAAK,YAAY,aAAa,MAAM;AACpC,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,SAAS;AAC/B,UAAM,UAAU,CAAC;AACjB,SAAK,aAAa,QAAQ,CAAC,MAAM,mBAAmB;AAClD,UAAI,mBAAmB,WAAW,KAAK,iBAAiB,eAAe,SAAS,OAAO,GAAG;AACxF,gBAAQ,KAAK,CAAC,gBAAgB,IAAI,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iCAAiC,kBAAkB;AACjD,UAAM;AAAA,MACJ,mBAAmB;AAAA,MACnB;AAAA,IACF,IAAI,KAAK;AAIT,QAAI,uBAAuB,WAAW,CAAC,oBAAoB,qBAAqB,oBAAoB,iBAAiB,aAAa,WAAW,iBAAiB,aAAa,cAAc,iBAAiB,UAAU;AAClN,aAAO;AAAA,IACT;AACA,UAAM,SAAS,iBAAiB;AAChC,QAAI,QAAQ;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,CAAC,EAAE,SAAS,gBAAgB,GAAG;AACxC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,YAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAUH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc,OAAO,UAAU;AAAA,EAC/B,gBAAgB,OAAO,YAAY;AAAA,EACnC;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB,IAAI,aAAa;AAAA,EAClC,cAAc;AAAA,EAAC;AAAA,EACf,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,SAAK,uBAAuB,KAAK,cAAc,QAAQ,SAAS,QAAQ,aAAa,KAAK,QAAQ,aAAa,wBAAwB,CAAC,EAAE,UAAU,YAAU;AAC5J,WAAK,eAAe;AACpB,WAAK,eAAe,KAAK,MAAM;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB,YAAY;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO,YAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,GAAG,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAAA,IAClF,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;ACjoBH,IAAM,kBAAkB,oBAAI,QAAQ;AAKpC,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,uBAAuB,OAAO,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjD,KAAK,QAAQ;AAEX,UAAM,SAAS,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,IAAI,cAAc;AAC/E,QAAI,OAAO,gBAAgB,IAAI,MAAM;AAErC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,QACL,SAAS,oBAAI,IAAI;AAAA,QACjB,MAAM,CAAC;AAAA,MACT;AACA,sBAAgB,IAAI,QAAQ,IAAI;AAEhC,aAAO,UAAU,MAAM;AACrB,wBAAgB,IAAI,MAAM,GAAG,KAAK,QAAQ,SAAO,IAAI,QAAQ,CAAC;AAC9D,wBAAgB,OAAO,MAAM;AAAA,MAC/B,CAAC;AAAA,IACH;AAEA,QAAI,CAAC,KAAK,QAAQ,IAAI,MAAM,GAAG;AAC7B,WAAK,QAAQ,IAAI,MAAM;AACvB,WAAK,KAAK,KAAK,gBAAgB,QAAQ;AAAA,QACrC,qBAAqB,KAAK;AAAA,MAC5B,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO,YAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,wBAAuB;AAAA,IAChC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AClDH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,YAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,UAAU,CAAC,mBAAmB;AAAA,IAC9B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AAAA,IAAC;AAAA,IAC5D,QAAQ,CAAC,oQAAoQ;AAAA,IAC7Q,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,oQAAoQ;AAAA,IAC/Q,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACnCH,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;;;ACMA,IAAM,qCAAqC,oBAAI,IAAI;AAEnD,IAAI;AAEJ,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,OAAO,QAAQ;AAAA,EAC3B,SAAS,OAAO,WAAW;AAAA,IACzB,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,KAAK,UAAU,aAAa,OAAO;AAAA;AAAA;AAAA,MAGtD,OAAO,WAAW,KAAK,MAAM;AAAA,QAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO;AAChB,QAAI,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO;AACjD,2BAAqB,OAAO,KAAK,MAAM;AAAA,IACzC;AACA,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA,EACA,OAAO,YAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAUH,SAAS,qBAAqB,OAAO,OAAO;AAC1C,MAAI,mCAAmC,IAAI,KAAK,GAAG;AACjD;AAAA,EACF;AACA,MAAI;AACF,QAAI,CAAC,qBAAqB;AACxB,4BAAsB,SAAS,cAAc,OAAO;AACpD,UAAI,OAAO;AACT,4BAAoB,aAAa,SAAS,KAAK;AAAA,MACjD;AACA,0BAAoB,aAAa,QAAQ,UAAU;AACnD,eAAS,KAAK,YAAY,mBAAmB;AAAA,IAC/C;AACA,QAAI,oBAAoB,OAAO;AAC7B,0BAAoB,MAAM,WAAW,UAAU,KAAK,cAAc,CAAC;AACnE,yCAAmC,IAAI,KAAK;AAAA,IAC9C;AAAA,EACF,SAAS,GAAG;AACV,YAAQ,MAAM,CAAC;AAAA,EACjB;AACF;AAEA,SAAS,eAAe,OAAO;AAG7B,SAAO;AAAA,IACL,SAAS,UAAU,SAAS,UAAU;AAAA,IACtC,OAAO;AAAA,IACP,aAAa,MAAM;AAAA,IAAC;AAAA,IACpB,gBAAgB,MAAM;AAAA,IAAC;AAAA,EACzB;AACF;AAGA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,gBAAgB,OAAO,YAAY;AAAA,EACnC,QAAQ,OAAO,MAAM;AAAA;AAAA,EAErB,WAAW,oBAAI,IAAI;AAAA;AAAA,EAEnB,kBAAkB,IAAI,QAAQ;AAAA,EAC9B,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,cAAc;AACZ,SAAK,gBAAgB,KAAK;AAC1B,SAAK,gBAAgB,SAAS;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,OAAO;AACf,UAAM,UAAU,aAAa,YAAY,KAAK,CAAC;AAC/C,WAAO,QAAQ,KAAK,gBAAc,KAAK,eAAe,UAAU,EAAE,IAAI,OAAO;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO;AACb,UAAM,UAAU,aAAa,YAAY,KAAK,CAAC;AAC/C,UAAM,cAAc,QAAQ,IAAI,WAAS,KAAK,eAAe,KAAK,EAAE,UAAU;AAC9E,QAAI,kBAAkB,cAAc,WAAW;AAE/C,sBAAkB,OAAO,gBAAgB,KAAK,KAAK,CAAC,CAAC,GAAG,gBAAgB,KAAK,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;AACtG,WAAO,gBAAgB,KAAK,IAAI,sBAAoB;AAClD,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QACT,aAAa,CAAC;AAAA,MAChB;AACA,uBAAiB,QAAQ,CAAC;AAAA,QACxB;AAAA,QACA;AAAA,MACF,MAAM;AACJ,iBAAS,UAAU,SAAS,WAAW;AACvC,iBAAS,YAAY,KAAK,IAAI;AAAA,MAChC,CAAC;AACD,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,eAAe,OAAO;AAEpB,QAAI,KAAK,SAAS,IAAI,KAAK,GAAG;AAC5B,aAAO,KAAK,SAAS,IAAI,KAAK;AAAA,IAChC;AACA,UAAM,MAAM,KAAK,cAAc,WAAW,KAAK;AAE/C,UAAM,kBAAkB,IAAI,WAAW,cAAY;AAMjD,YAAM,UAAU,OAAK,KAAK,MAAM,IAAI,MAAM,SAAS,KAAK,CAAC,CAAC;AAC1D,UAAI,YAAY,OAAO;AACvB,aAAO,MAAM;AACX,YAAI,eAAe,OAAO;AAAA,MAC5B;AAAA,IACF,CAAC,EAAE,KAAK,UAAU,GAAG,GAAG,IAAI,CAAC;AAAA,MAC3B;AAAA,IACF,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF,EAAE,GAAG,UAAU,KAAK,eAAe,CAAC;AAEpC,UAAM,SAAS;AAAA,MACb,YAAY;AAAA,MACZ;AAAA,IACF;AACA,SAAK,SAAS,IAAI,OAAO,MAAM;AAC/B,WAAO;AAAA,EACT;AAAA,EACA,OAAO,YAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,IAC5B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,SAAS,aAAa,SAAS;AAC7B,SAAO,QAAQ,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,OAAO,GAAG,OAAO,EAAE,CAAC,EAAE,IAAI,WAAS,MAAM,KAAK,CAAC;AAC3G;;;AChMA,SAAS,mBAAmB,QAAQ;AAElC,MAAI,OAAO,SAAS,mBAAmB,OAAO,kBAAkB,SAAS;AACvE,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,aAAa;AAC/B,aAAS,IAAI,GAAG,IAAI,OAAO,WAAW,QAAQ,KAAK;AACjD,UAAI,EAAE,OAAO,WAAW,CAAC,aAAa,UAAU;AAC9C,eAAO;AAAA,MACT;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,aAAa,QAAQ,KAAK;AACnD,UAAI,EAAE,OAAO,aAAa,CAAC,aAAa,UAAU;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAKA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,UAAU;AACf,WAAO,OAAO,qBAAqB,cAAc,OAAO,IAAI,iBAAiB,QAAQ;AAAA,EACvF;AAAA,EACA,OAAO,YAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,yBAAwB;AAAA,IACjC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,2BAA2B,OAAO,uBAAuB;AAAA;AAAA,EAEzD,oBAAoB,oBAAI,IAAI;AAAA,EAC5B,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,cAAc;AACZ,SAAK,kBAAkB,QAAQ,CAAC,GAAG,YAAY,KAAK,iBAAiB,OAAO,CAAC;AAAA,EAC/E;AAAA,EACA,QAAQ,cAAc;AACpB,UAAM,UAAU,cAAc,YAAY;AAC1C,WAAO,IAAI,WAAW,cAAY;AAChC,YAAM,SAAS,KAAK,gBAAgB,OAAO;AAC3C,YAAM,eAAe,OAAO,KAAK,IAAI,aAAW,QAAQ,OAAO,YAAU,CAAC,mBAAmB,MAAM,CAAC,CAAC,GAAG,OAAO,aAAW,CAAC,CAAC,QAAQ,MAAM,CAAC,EAAE,UAAU,aAAW;AAChK,aAAK,QAAQ,IAAI,MAAM;AACrB,mBAAS,KAAK,OAAO;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AACD,aAAO,MAAM;AACX,qBAAa,YAAY;AACzB,aAAK,kBAAkB,OAAO;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS;AACvB,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACxC,cAAM,SAAS,IAAI,QAAQ;AAC3B,cAAM,WAAW,KAAK,yBAAyB,OAAO,eAAa,OAAO,KAAK,SAAS,CAAC;AACzF,YAAI,UAAU;AACZ,mBAAS,QAAQ,SAAS;AAAA,YACxB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AACA,aAAK,kBAAkB,IAAI,SAAS;AAAA,UAClC;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AACL,aAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,MACtC;AACA,aAAO,KAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAS;AACzB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,WAAK,kBAAkB,IAAI,OAAO,EAAE;AACpC,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,EAAE,OAAO;AAC9C,aAAK,iBAAiB,OAAO;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,kBAAkB,IAAI,OAAO;AACtC,UAAI,UAAU;AACZ,iBAAS,WAAW;AAAA,MACtB;AACA,aAAO,SAAS;AAChB,WAAK,kBAAkB,OAAO,OAAO;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO,YAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,mBAAmB,OAAO,eAAe;AAAA,EACzC,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B,QAAQ,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,EACzD;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,qBAAqB,KAAK;AAC3C,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,qBAAqB;AACnB,QAAI,CAAC,KAAK,wBAAwB,CAAC,KAAK,UAAU;AAChD,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,aAAa;AACX,SAAK,aAAa;AAClB,UAAM,SAAS,KAAK,iBAAiB,QAAQ,KAAK,WAAW;AAC7D,SAAK,wBAAwB,KAAK,WAAW,OAAO,KAAK,aAAa,KAAK,QAAQ,CAAC,IAAI,QAAQ,UAAU,KAAK,KAAK;AAAA,EACtH;AAAA,EACA,eAAe;AACb,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAAA,EACA,OAAO,YAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,6BAA6B,YAAY,gBAAgB;AAAA,MACvE,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,YAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB;AAAA,IAC3B,SAAS,CAAC,iBAAiB;AAAA,EAC7B,CAAC;AAAA,EACD,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,WAAW,CAAC,uBAAuB;AAAA,EACrC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,MAC3B,WAAW,CAAC,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3OH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,WAAW,SAAS;AAGlB,WAAO,QAAQ,aAAa,UAAU;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,SAAS;AACjB,WAAO,YAAY,OAAO,KAAK,iBAAiB,OAAO,EAAE,eAAe;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAElB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,eAAe,gBAAgB,UAAU,OAAO,CAAC;AACvD,QAAI,cAAc;AAEhB,UAAI,iBAAiB,YAAY,MAAM,IAAI;AACzC,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,UAAU,YAAY,GAAG;AACjC,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,QAAI,gBAAgB,iBAAiB,OAAO;AAC5C,QAAI,QAAQ,aAAa,iBAAiB,GAAG;AAC3C,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,YAAY,aAAa,UAAU;AAIlD,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO,CAAC,yBAAyB,OAAO,GAAG;AACrF,aAAO;AAAA,IACT;AACA,QAAI,aAAa,SAAS;AAGxB,UAAI,CAAC,QAAQ,aAAa,UAAU,GAAG;AACrC,eAAO;AAAA,MACT;AAGA,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,SAAS;AAKxB,UAAI,kBAAkB,IAAI;AACxB,eAAO;AAAA,MACT;AAGA,UAAI,kBAAkB,MAAM;AAC1B,eAAO;AAAA,MACT;AAIA,aAAO,KAAK,UAAU,WAAW,QAAQ,aAAa,UAAU;AAAA,IAClE;AACA,WAAO,QAAQ,YAAY;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,SAAS,QAAQ;AAG3B,WAAO,uBAAuB,OAAO,KAAK,CAAC,KAAK,WAAW,OAAO,MAAM,QAAQ,oBAAoB,KAAK,UAAU,OAAO;AAAA,EAC5H;AAAA,EACA,OAAO,YAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,IAC9B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,SAAS,gBAAgBC,SAAQ;AAC/B,MAAI;AACF,WAAOA,QAAO;AAAA,EAChB,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,SAAS;AAG5B,SAAO,CAAC,EAAE,QAAQ,eAAe,QAAQ,gBAAgB,OAAO,QAAQ,mBAAmB,cAAc,QAAQ,eAAe,EAAE;AACpI;AAEA,SAAS,oBAAoB,SAAS;AACpC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,SAAO,aAAa,WAAW,aAAa,YAAY,aAAa,YAAY,aAAa;AAChG;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,eAAe,OAAO,KAAK,QAAQ,QAAQ;AACpD;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAO,gBAAgB,OAAO,KAAK,QAAQ,aAAa,MAAM;AAChE;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,QAAQ,aAAa,UAAU,KAAK,QAAQ,aAAa,QAAW;AACvE,WAAO;AAAA,EACT;AACA,MAAI,WAAW,QAAQ,aAAa,UAAU;AAC9C,SAAO,CAAC,EAAE,YAAY,CAAC,MAAM,SAAS,UAAU,EAAE,CAAC;AACrD;AAKA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,iBAAiB,OAAO,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,SAAS,QAAQ,aAAa,UAAU,KAAK,IAAI,EAAE;AACpE,SAAO,MAAM,QAAQ,IAAI,KAAK;AAChC;AAEA,SAAS,yBAAyB,SAAS;AACzC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,MAAI,YAAY,aAAa,WAAW,QAAQ;AAChD,SAAO,cAAc,UAAU,cAAc,cAAc,aAAa,YAAY,aAAa;AACnG;AAKA,SAAS,uBAAuB,SAAS;AAEvC,MAAI,cAAc,OAAO,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,oBAAoB,OAAO,KAAK,iBAAiB,OAAO,KAAK,QAAQ,aAAa,iBAAiB,KAAK,iBAAiB,OAAO;AACzI;AAEA,SAAS,UAAU,MAAM;AAEvB,SAAO,KAAK,iBAAiB,KAAK,cAAc,eAAe;AACjE;AASA,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA;AAAA,EAEf,sBAAsB,MAAM,KAAK,yBAAyB;AAAA,EAC1D,oBAAoB,MAAM,KAAK,0BAA0B;AAAA;AAAA,EAEzD,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,OAAO,KAAK,YAAY;AACnD,WAAK,sBAAsB,OAAO,KAAK,UAAU;AAAA,IACnD;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,YAAY,UAAU,UAAU,SAAS,WAAW,eAAe,OACnE,WAAW;AACT,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,QAAI,CAAC,cAAc;AACjB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,KAAK;AACvB,QAAI,aAAa;AACf,kBAAY,oBAAoB,SAAS,KAAK,mBAAmB;AACjE,kBAAY,OAAO;AAAA,IACrB;AACA,QAAI,WAAW;AACb,gBAAU,oBAAoB,SAAS,KAAK,iBAAiB;AAC7D,gBAAU,OAAO;AAAA,IACnB;AACA,SAAK,eAAe,KAAK,aAAa;AACtC,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AAEd,QAAI,KAAK,cAAc;AACrB,aAAO;AAAA,IACT;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe,KAAK,cAAc;AACvC,aAAK,aAAa,iBAAiB,SAAS,KAAK,mBAAmB;AAAA,MACtE;AACA,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,KAAK,cAAc;AACrC,aAAK,WAAW,iBAAiB,SAAS,KAAK,iBAAiB;AAAA,MAClE;AAAA,IACF,CAAC;AACD,QAAI,KAAK,SAAS,YAAY;AAC5B,WAAK,SAAS,WAAW,aAAa,KAAK,cAAc,KAAK,QAAQ;AACtE,WAAK,SAAS,WAAW,aAAa,KAAK,YAAY,KAAK,SAAS,WAAW;AAChF,WAAK,eAAe;AAAA,IACtB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,SAAS;AACpC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,oBAAoB,OAAO,CAAC,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mCAAmC,SAAS;AAC1C,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,0BAA0B,OAAO,CAAC,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kCAAkC,SAAS;AACzC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,yBAAyB,OAAO,CAAC,CAAC;AAAA,IAC7E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,OAAO;AAExB,UAAM,UAAU,KAAK,SAAS,iBAAiB,qBAAqB,KAAK,qBAA0B,KAAK,iBAAsB,KAAK,GAAG;AACtI,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAEvC,YAAI,QAAQ,CAAC,EAAE,aAAa,aAAa,KAAK,EAAE,GAAG;AACjD,kBAAQ,KAAK,gDAAgD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QACvL,WAAW,QAAQ,CAAC,EAAE,aAAa,oBAAoB,KAAK,EAAE,GAAG;AAC/D,kBAAQ,KAAK,uDAAuD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QAC9L;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,SAAS;AACpB,aAAO,QAAQ,SAAS,QAAQ,CAAC,IAAI,KAAK,yBAAyB,KAAK,QAAQ;AAAA,IAClF;AACA,WAAO,QAAQ,SAAS,QAAQ,QAAQ,SAAS,CAAC,IAAI,KAAK,wBAAwB,KAAK,QAAQ;AAAA,EAClG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,SAAS;AAE3B,UAAM,oBAAoB,KAAK,SAAS,cAAc,wCAA6C;AACnG,QAAI,mBAAmB;AAErB,WAAK,OAAO,cAAc,eAAe,cAAc,kBAAkB,aAAa,mBAAmB,GAAG;AAC1G,gBAAQ,KAAK,2IAAqJ,iBAAiB;AAAA,MACrL;AAGA,WAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACpG,gBAAQ,KAAK,0DAA0D,iBAAiB;AAAA,MAC1F;AACA,UAAI,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACjD,cAAM,iBAAiB,KAAK,yBAAyB,iBAAiB;AACtE,wBAAgB,MAAM,OAAO;AAC7B,eAAO,CAAC,CAAC;AAAA,MACX;AACA,wBAAkB,MAAM,OAAO;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,0BAA0B,OAAO;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,SAAS;AACjC,UAAM,oBAAoB,KAAK,mBAAmB,OAAO;AACzD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,SAAS;AAChC,UAAM,oBAAoB,KAAK,mBAAmB,KAAK;AACvD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,yBAAyB,MAAM;AAC7B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,yBAAyB,SAAS,CAAC,CAAC,IAAI;AAC1H,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,wBAAwB,MAAM;AAC5B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AAEA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,wBAAwB,SAAS,CAAC,CAAC,IAAI;AACzH,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AACjD,SAAK,sBAAsB,KAAK,UAAU,MAAM;AAChD,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,UAAU,IAAI,uBAAuB;AAC5C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,WAAW,QAAQ;AAGvC,gBAAY,OAAO,aAAa,YAAY,GAAG,IAAI,OAAO,gBAAgB,UAAU;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,SAAS;AACrB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,SAAS,KAAK,YAAY;AACrD,WAAK,sBAAsB,SAAS,KAAK,UAAU;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,IAAI;AAEnB,QAAI,KAAK,WAAW;AAClB,sBAAgB,IAAI;AAAA,QAClB,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,iBAAW,EAAE;AAAA,IACf;AAAA,EACF;AACF;AAIA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,WAAW,OAAO,oBAAoB;AAAA,EACtC,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,uBAAuB,OAAO;AAC5C,WAAO,IAAI,UAAU,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,sBAAsB,KAAK,SAAS;AAAA,EACjH;AAAA,EACA,OAAO,YAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc,OAAO,UAAU;AAAA,EAC/B,oBAAoB,OAAO,gBAAgB;AAAA;AAAA,EAE3C;AAAA;AAAA,EAEA,4BAA4B;AAAA;AAAA,EAE5B,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW,WAAW;AAAA,EACpC;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,UAAU;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,OAAO,QAAQ;AAChC,QAAI,SAAS,WAAW;AACtB,WAAK,YAAY,KAAK,kBAAkB,OAAO,KAAK,YAAY,eAAe,IAAI;AAAA,IACrF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,QAAQ;AAGxB,QAAI,KAAK,2BAA2B;AAClC,WAAK,0BAA0B,MAAM;AACrC,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,cAAc;AAC9B,QAAI,KAAK,aAAa;AACpB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,YAAY,GAAG;AACnD,WAAK,UAAU,cAAc;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,oBAAoB,QAAQ,aAAa;AAC/C,QAAI,qBAAqB,CAAC,kBAAkB,eAAe,KAAK,eAAe,KAAK,WAAW,YAAY,GAAG;AAC5G,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B,kCAAkC;AACnE,SAAK,WAAW,6BAA6B;AAAA,EAC/C;AAAA,EACA,OAAO,YAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,gBAAgB,WAAW,gBAAgB;AAAA,MACxD,aAAa,CAAC,GAAG,2BAA2B,eAAe,gBAAgB;AAAA,IAC7E;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,8BAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,+BAA+B,IAAI,eAAe,wBAAwB;AAAA,EAC9E,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,uCAAuC;AAC9C,SAAO;AACT;AAEA,IAAM,iCAAiC,IAAI,eAAe,gCAAgC;AAC1F,IAAI,YAAY;AAChB,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,UAAU,OAAO,MAAM;AAAA,EACvB,kBAAkB,OAAO,gCAAgC;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM,eAAe,OAAO,8BAA8B;AAAA,MACxD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,eAAe,gBAAgB,KAAK,mBAAmB;AAAA,EAC9D;AAAA,EACA,SAAS,YAAY,MAAM;AACzB,UAAM,iBAAiB,KAAK;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,CAAC,MAAM,UAAU;AACpD,iBAAW,KAAK,CAAC;AAAA,IACnB,OAAO;AACL,OAAC,YAAY,QAAQ,IAAI;AAAA,IAC3B;AACA,SAAK,MAAM;AACX,iBAAa,KAAK,gBAAgB;AAClC,QAAI,CAAC,YAAY;AACf,mBAAa,kBAAkB,eAAe,aAAa,eAAe,aAAa;AAAA,IACzF;AACA,QAAI,YAAY,QAAQ,gBAAgB;AACtC,iBAAW,eAAe;AAAA,IAC5B;AAEA,SAAK,aAAa,aAAa,aAAa,UAAU;AACtD,QAAI,KAAK,aAAa,IAAI;AACxB,WAAK,yBAAyB,KAAK,aAAa,EAAE;AAAA,IACpD;AAMA,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,kBAAkB,IAAI,QAAQ,aAAW,KAAK,kBAAkB,OAAO;AAAA,MAC9E;AACA,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB,WAAW,MAAM;AACvC,aAAK,aAAa,cAAc;AAChC,YAAI,OAAO,aAAa,UAAU;AAChC,eAAK,mBAAmB,WAAW,MAAM,KAAK,MAAM,GAAG,QAAQ;AAAA,QACjE;AAGA,aAAK,kBAAkB;AACvB,aAAK,kBAAkB,KAAK,kBAAkB;AAAA,MAChD,GAAG,GAAG;AACN,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,cAAc;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,gBAAgB;AAClC,SAAK,cAAc,OAAO;AAC1B,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB,KAAK,kBAAkB;AAAA,EAChD;AAAA,EACA,qBAAqB;AACnB,UAAM,eAAe;AACrB,UAAM,mBAAmB,KAAK,UAAU,uBAAuB,YAAY;AAC3E,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AAEjD,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,WAAO,UAAU,IAAI,YAAY;AACjC,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO,aAAa,aAAa,QAAQ;AACzC,WAAO,KAAK,sBAAsB,WAAW;AAC7C,SAAK,UAAU,KAAK,YAAY,MAAM;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,IAAI;AAO3B,UAAM,SAAS,KAAK,UAAU,iBAAiB,mDAAmD;AAClG,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,WAAW,MAAM,aAAa,WAAW;AAC/C,UAAI,CAAC,UAAU;AACb,cAAM,aAAa,aAAa,EAAE;AAAA,MACpC,WAAW,SAAS,QAAQ,EAAE,MAAM,IAAI;AACtC,cAAM,aAAa,aAAa,WAAW,MAAM,EAAE;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,YAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,IACvB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,iBAAiB,OAAO,aAAa;AAAA,EACrC,mBAAmB,OAAO,eAAe;AAAA,EACzC,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc,UAAU,SAAS,UAAU,cAAc,QAAQ;AACtE,QAAI,KAAK,gBAAgB,OAAO;AAC9B,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,YAAY;AAC/B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF,WAAW,CAAC,KAAK,eAAe;AAC9B,WAAK,gBAAgB,KAAK,QAAQ,kBAAkB,MAAM;AACxD,eAAO,KAAK,iBAAiB,QAAQ,KAAK,WAAW,EAAE,UAAU,MAAM;AAErE,gBAAM,cAAc,KAAK,YAAY,cAAc;AAGnD,cAAI,gBAAgB,KAAK,wBAAwB;AAC/C,iBAAK,eAAe,SAAS,aAAa,KAAK,aAAa,KAAK,QAAQ;AACzE,iBAAK,yBAAyB;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AAAA;AAAA,EAEd;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AAAA,EAC3D;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,YAAY;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,YAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACnC,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,eAAe,YAAY;AAAA,MAC3C,UAAU,CAAC,GAAG,uBAAuB,UAAU;AAAA,IACjD;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAC3B,EAAAA,kBAAiBA,kBAAiB,MAAM,IAAI,CAAC,IAAI;AACjD,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC3D,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC7D,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,IAAM,2BAA2B;AAEjC,IAAM,2BAA2B;AAEjC,IAAM,sCAAsC;AAY5C,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,OAAO,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,SAAK,0BAA0B,OAAO,kBAAkB,EAAE,QAAQ,yBAAyB,EAAE,UAAU,MAAM;AAC3G,UAAI,KAAK,6BAA6B;AACpC,aAAK,8BAA8B;AACnC,aAAK,qCAAqC;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB;AACpB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO,iBAAiB;AAAA,IAC1B;AAIA,UAAM,cAAc,KAAK,UAAU,cAAc,KAAK;AACtD,gBAAY,MAAM,kBAAkB;AACpC,gBAAY,MAAM,WAAW;AAC7B,SAAK,UAAU,KAAK,YAAY,WAAW;AAK3C,UAAM,iBAAiB,KAAK,UAAU,eAAe;AACrD,UAAM,gBAAgB,kBAAkB,eAAe,mBAAmB,eAAe,iBAAiB,WAAW,IAAI;AACzH,UAAM,iBAAiB,iBAAiB,cAAc,mBAAmB,IAAI,QAAQ,MAAM,EAAE;AAC7F,gBAAY,OAAO;AACnB,YAAQ,eAAe;AAAA;AAAA,MAErB,KAAK;AAAA;AAAA,MAEL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,iBAAiB;AAAA;AAAA,MAE1B,KAAK;AAAA;AAAA,MAEL,KAAK;AACH,eAAO,iBAAiB;AAAA,IAC5B;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,cAAc;AACZ,SAAK,wBAAwB,YAAY;AAAA,EAC3C;AAAA;AAAA,EAEA,uCAAuC;AACrC,QAAI,CAAC,KAAK,+BAA+B,KAAK,UAAU,aAAa,KAAK,UAAU,MAAM;AACxF,YAAM,cAAc,KAAK,UAAU,KAAK;AACxC,kBAAY,OAAO,qCAAqC,0BAA0B,wBAAwB;AAC1G,WAAK,8BAA8B;AACnC,YAAM,OAAO,KAAK,oBAAoB;AACtC,UAAI,SAAS,iBAAiB,gBAAgB;AAC5C,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E,WAAW,SAAS,iBAAiB,gBAAgB;AACnD,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,YAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,0BAAyB;AAAA,IAClC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc;AACZ,WAAO,wBAAwB,EAAE,qCAAqC;AAAA,EACxE;AAAA,EACA,OAAO,YAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,IACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,EACtD,CAAC;AAAA,EACD,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,MACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,IACtD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACz9BH,IAAM,WAAW,CAAC;AAElB,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,SAAS,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,MAAM,QAAQ;AAGZ,QAAI,KAAK,WAAW,MAAM;AACxB,gBAAU,KAAK;AAAA,IACjB;AACA,QAAI,CAAC,SAAS,eAAe,MAAM,GAAG;AACpC,eAAS,MAAM,IAAI;AAAA,IACrB;AACA,WAAO,GAAG,MAAM,GAAG,SAAS,MAAM,GAAG;AAAA,EACvC;AAAA,EACA,OAAO,YAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACxCH,IAAM,yCAAyC;AAK/C,IAAM,YAAN,MAAgB;AAAA,EACd,mBAAmB,IAAI,QAAQ;AAAA,EAC/B,SAAS,CAAC;AAAA,EACV,qBAAqB;AAAA;AAAA,EAErB,kBAAkB,CAAC;AAAA,EACnB;AAAA,EACA,gBAAgB,IAAI,QAAQ;AAAA,EAC5B,eAAe,KAAK;AAAA,EACpB,YAAY,cAAc,QAAQ;AAChC,UAAM,oBAAoB,OAAO,QAAQ,qBAAqB,WAAW,OAAO,mBAAmB;AACnG,QAAI,QAAQ,eAAe;AACzB,WAAK,mBAAmB,OAAO;AAAA,IACjC;AACA,SAAK,OAAO,cAAc,eAAe,cAAc,aAAa,UAAU,aAAa,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAC5I,YAAM,IAAI,MAAM,0EAA0E;AAAA,IAC5F;AACA,SAAK,SAAS,YAAY;AAC1B,SAAK,iBAAiB,iBAAiB;AAAA,EACzC;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB,CAAC;AACxB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,4BAA4B,OAAO;AACjC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,SAAS,OAAO;AACd,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM;AAGtB,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG;AACvC,WAAK,iBAAiB,KAAK,MAAM,IAAI,kBAAkB,CAAC;AAAA,IAC1D,WAAW,WAAW,KAAK,WAAW,KAAK,WAAW,QAAQ,WAAW,MAAM;AAC7E,WAAK,iBAAiB,KAAK,OAAO,aAAa,OAAO,CAAC;AAAA,IACzD;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,gBAAgB,SAAS;AAAA,EACvC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA,EACA,iBAAiB,mBAAmB;AAIlC,SAAK,iBAAiB,KAAK,IAAI,YAAU,KAAK,gBAAgB,KAAK,MAAM,CAAC,GAAG,aAAa,iBAAiB,GAAG,OAAO,MAAM,KAAK,gBAAgB,SAAS,CAAC,GAAG,IAAI,MAAM,KAAK,gBAAgB,KAAK,EAAE,EAAE,kBAAkB,CAAC,CAAC,EAAE,UAAU,iBAAe;AAGlP,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK;AAC/C,cAAM,SAAS,KAAK,qBAAqB,KAAK,KAAK,OAAO;AAC1D,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,YAAI,CAAC,KAAK,mBAAmB,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,WAAW,MAAM,GAAG;AAC7G,eAAK,cAAc,KAAK,IAAI;AAC5B;AAAA,QACF;AAAA,MACF;AACA,WAAK,kBAAkB,CAAC;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;;;ACrEA,SAAS,eAAe,UAAU,WAAW;AAC3C,MAAI,UAAU,QAAQ;AACpB,WAAO,UAAU,KAAK,cAAY,MAAM,QAAQ,CAAC;AAAA,EACnD;AACA,SAAO,MAAM,UAAU,MAAM,YAAY,MAAM,WAAW,MAAM;AAClE;;;ACDA,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA,mBAAmB;AAAA,EACnB,cAAc,OAAO,IAAI;AAAA,EACzB,QAAQ;AAAA,EACR,yBAAyB,aAAa;AAAA,EACtC;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,uBAAuB,CAAC;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,IACf,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,UAAQ,KAAK;AAAA,EAChC,YAAY,QAAQ,UAAU;AAC5B,SAAK,SAAS;AAId,QAAI,kBAAkB,WAAW;AAC/B,WAAK,2BAA2B,OAAO,QAAQ,UAAU,cAAY,KAAK,cAAc,SAAS,QAAQ,CAAC,CAAC;AAAA,IAC7G,WAAW,SAAS,MAAM,GAAG;AAC3B,UAAI,CAAC,aAAa,OAAO,cAAc,eAAe,YAAY;AAChE,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AACA,WAAK,aAAa,OAAO,MAAM,KAAK,cAAc,OAAO,CAAC,GAAG;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,IAAI,QAAQ;AAAA;AAAA,EAErB,SAAS,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,cAAc,WAAW;AACvB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,aAAa,MAAM;AAC1B,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,UAAU,MAAM;AACtC,SAAK,YAAY;AACjB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,WAAW;AACnC,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,MAAM;AAC5B,SAAK,uBAAuB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,mBAAmB,KAAK;AACpC,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAMC,SAAQ,KAAK,eAAe;AAClC,UAAIA,OAAM,SAAS,KAAKA,OAAM,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAC/E,cAAM,MAAM,8EAA8E;AAAA,MAC5F;AAAA,IACF;AACA,SAAK,uBAAuB,YAAY;AACxC,UAAM,QAAQ,KAAK,eAAe;AAClC,SAAK,aAAa,IAAI,UAAU,OAAO;AAAA,MACrC,kBAAkB,OAAO,qBAAqB,WAAW,mBAAmB;AAAA,MAC5E,eAAe,UAAQ,KAAK,iBAAiB,IAAI;AAAA,IACnD,CAAC;AACD,SAAK,yBAAyB,KAAK,WAAW,aAAa,UAAU,UAAQ;AAC3E,WAAK,cAAc,IAAI;AAAA,IACzB,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,YAAY,MAAM;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,UAAU,MAAM;AAC7B,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,UAAU,MAAM,QAAQ,IAAI;AACzC,SAAK,iBAAiB;AAAA,MACpB;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,qBAAqB,KAAK,YAAY;AAC5C,SAAK,iBAAiB,IAAI;AAC1B,QAAI,KAAK,YAAY,MAAM,oBAAoB;AAC7C,WAAK,OAAO,KAAK,KAAK,gBAAgB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM;AACtB,UAAM,YAAY,CAAC,UAAU,WAAW,WAAW,UAAU;AAC7D,UAAM,oBAAoB,UAAU,MAAM,cAAY;AACpD,aAAO,CAAC,MAAM,QAAQ,KAAK,KAAK,qBAAqB,QAAQ,QAAQ,IAAI;AAAA,IAC3E,CAAC;AACD,YAAQ,SAAS;AAAA,MACf,KAAK;AACH,aAAK,OAAO,KAAK;AACjB;AAAA,MACF,KAAK;AACH,YAAI,KAAK,aAAa,mBAAmB;AACvC,eAAK,kBAAkB;AACvB;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,aAAa,mBAAmB;AACvC,eAAK,sBAAsB;AAC3B;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,gBAAgB,QAAQ,KAAK,sBAAsB,IAAI,KAAK,kBAAkB;AACnF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,gBAAgB,QAAQ,KAAK,kBAAkB,IAAI,KAAK,sBAAsB;AACnF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,mBAAmB;AACxB;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,kBAAkB;AACvB;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,WAAW,mBAAmB;AACpD,gBAAM,cAAc,KAAK,mBAAmB,KAAK,eAAe;AAChE,eAAK,sBAAsB,cAAc,IAAI,cAAc,GAAG,CAAC;AAC/D;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,WAAW,mBAAmB;AACpD,gBAAM,cAAc,KAAK,mBAAmB,KAAK,eAAe;AAChE,gBAAM,cAAc,KAAK,eAAe,EAAE;AAC1C,eAAK,sBAAsB,cAAc,cAAc,cAAc,cAAc,GAAG,EAAE;AACxF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACE,YAAI,qBAAqB,eAAe,OAAO,UAAU,GAAG;AAC1D,eAAK,YAAY,UAAU,KAAK;AAAA,QAClC;AAGA;AAAA,IACJ;AACA,SAAK,YAAY,MAAM;AACvB,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,CAAC,CAAC,KAAK,cAAc,KAAK,WAAW,SAAS;AAAA,EACvD;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,sBAAsB,GAAG,CAAC;AAAA,EACjC;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,sBAAsB,KAAK,eAAe,EAAE,SAAS,GAAG,EAAE;AAAA,EACjE;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,mBAAmB,IAAI,KAAK,mBAAmB,IAAI,KAAK,sBAAsB,CAAC;AAAA,EACtF;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,mBAAmB,KAAK,KAAK,QAAQ,KAAK,kBAAkB,IAAI,KAAK,sBAAsB,EAAE;AAAA,EACpG;AAAA,EACA,iBAAiB,MAAM;AACrB,UAAM,YAAY,KAAK,eAAe;AACtC,UAAM,QAAQ,OAAO,SAAS,WAAW,OAAO,UAAU,QAAQ,IAAI;AACtE,UAAM,aAAa,UAAU,KAAK;AAElC,SAAK,YAAY,IAAI,cAAc,OAAO,OAAO,UAAU;AAC3D,SAAK,mBAAmB;AACxB,SAAK,YAAY,4BAA4B,KAAK;AAAA,EACpD;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,uBAAuB,YAAY;AACxC,SAAK,0BAA0B,YAAY;AAC3C,SAAK,YAAY,QAAQ;AACzB,SAAK,YAAY,QAAQ;AACzB,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO;AAC3B,SAAK,QAAQ,KAAK,qBAAqB,KAAK,IAAI,KAAK,wBAAwB,KAAK;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,OAAO;AAC1B,UAAM,QAAQ,KAAK,eAAe;AAClC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,KAAK;AACtC,YAAM,SAAS,KAAK,mBAAmB,QAAQ,IAAI,MAAM,UAAU,MAAM;AACzE,YAAM,OAAO,MAAM,KAAK;AACxB,UAAI,CAAC,KAAK,iBAAiB,IAAI,GAAG;AAChC,aAAK,cAAc,KAAK;AACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,OAAO;AAC7B,SAAK,sBAAsB,KAAK,mBAAmB,OAAO,KAAK;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO,eAAe;AAC1C,UAAM,QAAQ,KAAK,eAAe;AAClC,QAAI,CAAC,MAAM,KAAK,GAAG;AACjB;AAAA,IACF;AACA,WAAO,KAAK,iBAAiB,MAAM,KAAK,CAAC,GAAG;AAC1C,eAAS;AACT,UAAI,CAAC,MAAM,KAAK,GAAG;AACjB;AAAA,MACF;AAAA,IACF;AACA,SAAK,cAAc,KAAK;AAAA,EAC1B;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,aAAO,KAAK,OAAO;AAAA,IACrB;AACA,WAAO,KAAK,kBAAkB,YAAY,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,EACzE;AAAA;AAAA,EAEA,cAAc,UAAU;AACtB,SAAK,YAAY,SAAS,QAAQ;AAClC,UAAM,aAAa,KAAK,YAAY;AACpC,QAAI,YAAY;AACd,YAAM,WAAW,SAAS,QAAQ,UAAU;AAC5C,UAAI,WAAW,MAAM,aAAa,KAAK,kBAAkB;AACvD,aAAK,mBAAmB;AACxB,aAAK,YAAY,4BAA4B,QAAQ;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACF;;;AChWA,IAAM,6BAAN,cAAyC,eAAe;AAAA,EACtD,cAAc,OAAO;AACnB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,kBAAkB;AAAA,IACpC;AACA,UAAM,cAAc,KAAK;AACzB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,gBAAgB;AAAA,IAClC;AAAA,EACF;AACF;;;ACVA,IAAM,kBAAN,cAA8B,eAAe;AAAA,EAC3C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,eAAe,QAAQ;AACrB,SAAK,UAAU;AACf,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,cAAc,IAAI;AACxB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,MAAM,KAAK,OAAO;AAAA,IACpC;AAAA,EACF;AACF;;;ACcA,IAAM,eAAe;AAKrB,SAAS,oBAAoB,IAAI,MAAM,IAAI;AACzC,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,MAAI,IAAI,KAAK,gBAAc,WAAW,KAAK,MAAM,EAAE,GAAG;AACpD;AAAA,EACF;AACA,MAAI,KAAK,EAAE;AACX,KAAG,aAAa,MAAM,IAAI,KAAK,YAAY,CAAC;AAC9C;AAKA,SAAS,uBAAuB,IAAI,MAAM,IAAI;AAC5C,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,QAAM,cAAc,IAAI,OAAO,SAAO,QAAQ,EAAE;AAChD,MAAI,YAAY,QAAQ;AACtB,OAAG,aAAa,MAAM,YAAY,KAAK,YAAY,CAAC;AAAA,EACtD,OAAO;AACL,OAAG,gBAAgB,IAAI;AAAA,EACzB;AACF;AAKA,SAAS,oBAAoB,IAAI,MAAM;AAErC,QAAM,YAAY,GAAG,aAAa,IAAI;AACtC,SAAO,WAAW,MAAM,MAAM,KAAK,CAAC;AACtC;AAaA,IAAM,4BAA4B;AAMlC,IAAM,iCAAiC;AAEvC,IAAI,SAAS;AAMb,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B,mBAAmB,oBAAI,IAAI;AAAA;AAAA,EAE3B,qBAAqB;AAAA;AAAA,EAErB,MAAM,GAAG,QAAQ;AAAA,EACjB,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AACzD,SAAK,MAAM,OAAO,MAAM,IAAI,MAAM;AAAA,EACpC;AAAA,EACA,SAAS,aAAa,SAAS,MAAM;AACnC,QAAI,CAAC,KAAK,gBAAgB,aAAa,OAAO,GAAG;AAC/C;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,OAAO,YAAY,UAAU;AAE/B,mBAAa,SAAS,KAAK,GAAG;AAC9B,WAAK,iBAAiB,IAAI,KAAK;AAAA,QAC7B,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,WAAW,CAAC,KAAK,iBAAiB,IAAI,GAAG,GAAG;AAC1C,WAAK,sBAAsB,SAAS,IAAI;AAAA,IAC1C;AACA,QAAI,CAAC,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACxD,WAAK,qBAAqB,aAAa,GAAG;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,kBAAkB,aAAa,SAAS,MAAM;AAC5C,QAAI,CAAC,WAAW,CAAC,KAAK,eAAe,WAAW,GAAG;AACjD;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACvD,WAAK,wBAAwB,aAAa,GAAG;AAAA,IAC/C;AAGA,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAI,qBAAqB,kBAAkB,mBAAmB,GAAG;AAC/D,aAAK,sBAAsB,GAAG;AAAA,MAChC;AAAA,IACF;AACA,QAAI,KAAK,oBAAoB,WAAW,WAAW,GAAG;AACpD,WAAK,mBAAmB,OAAO;AAC/B,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,oBAAoB,KAAK,UAAU,iBAAiB,IAAI,8BAA8B,KAAK,KAAK,GAAG,IAAI;AAC7G,aAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,WAAK,kCAAkC,kBAAkB,CAAC,CAAC;AAC3D,wBAAkB,CAAC,EAAE,gBAAgB,8BAA8B;AAAA,IACrE;AACA,SAAK,oBAAoB,OAAO;AAChC,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAS,MAAM;AACnC,UAAM,iBAAiB,KAAK,UAAU,cAAc,KAAK;AACzD,iBAAa,gBAAgB,KAAK,GAAG;AACrC,mBAAe,cAAc;AAC7B,QAAI,MAAM;AACR,qBAAe,aAAa,QAAQ,IAAI;AAAA,IAC1C;AACA,SAAK,yBAAyB;AAC9B,SAAK,mBAAmB,YAAY,cAAc;AAClD,SAAK,iBAAiB,IAAI,OAAO,SAAS,IAAI,GAAG;AAAA,MAC/C;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB,KAAK;AACzB,SAAK,iBAAiB,IAAI,GAAG,GAAG,gBAAgB,OAAO;AACvD,SAAK,iBAAiB,OAAO,GAAG;AAAA,EAClC;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,oBAAoB;AAC3B;AAAA,IACF;AACA,UAAM,qBAAqB;AAC3B,UAAM,mBAAmB,KAAK,UAAU,iBAAiB,IAAI,kBAAkB,qBAAqB;AACpG,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAKhD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,UAAM,oBAAoB,KAAK,UAAU,cAAc,KAAK;AAK5D,sBAAkB,MAAM,aAAa;AAGrC,sBAAkB,UAAU,IAAI,kBAAkB;AAClD,sBAAkB,UAAU,IAAI,qBAAqB;AACrD,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,wBAAkB,aAAa,YAAY,QAAQ;AAAA,IACrD;AACA,SAAK,UAAU,KAAK,YAAY,iBAAiB;AACjD,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,kCAAkC,SAAS;AAEzC,UAAM,uBAAuB,oBAAoB,SAAS,kBAAkB,EAAE,OAAO,QAAM,GAAG,QAAQ,yBAAyB,KAAK,CAAC;AACrI,YAAQ,aAAa,oBAAoB,qBAAqB,KAAK,GAAG,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,SAAS,KAAK;AACjC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AAGvD,wBAAoB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACpF,YAAQ,aAAa,gCAAgC,KAAK,GAAG;AAC7D,sBAAkB;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,SAAS,KAAK;AACpC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,sBAAkB;AAClB,2BAAuB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACvF,YAAQ,gBAAgB,8BAA8B;AAAA,EACxD;AAAA;AAAA,EAEA,6BAA6B,SAAS,KAAK;AACzC,UAAM,eAAe,oBAAoB,SAAS,kBAAkB;AACpE,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAM,YAAY,qBAAqB,kBAAkB,eAAe;AACxE,WAAO,CAAC,CAAC,aAAa,aAAa,QAAQ,SAAS,KAAK;AAAA,EAC3D;AAAA;AAAA,EAEA,gBAAgB,SAAS,SAAS;AAChC,QAAI,CAAC,KAAK,eAAe,OAAO,GAAG;AACjC,aAAO;AAAA,IACT;AACA,QAAI,WAAW,OAAO,YAAY,UAAU;AAI1C,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,WAAW,OAAO,KAAK,GAAG,OAAO,GAAG,KAAK;AAChE,UAAM,YAAY,QAAQ,aAAa,YAAY;AAGnD,WAAO,iBAAiB,CAAC,aAAa,UAAU,KAAK,MAAM,iBAAiB;AAAA,EAC9E;AAAA;AAAA,EAEA,eAAe,SAAS;AACtB,WAAO,QAAQ,aAAa,KAAK,UAAU;AAAA,EAC7C;AAAA,EACA,OAAO,YAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,IACvB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,SAAS,OAAO,SAAS,MAAM;AAC7B,SAAO,OAAO,YAAY,WAAW,GAAG,QAAQ,EAAE,IAAI,OAAO,KAAK;AACpE;AAEA,SAAS,aAAa,SAAS,WAAW;AACxC,MAAI,CAAC,QAAQ,IAAI;AACf,YAAQ,KAAK,GAAG,yBAAyB,IAAI,SAAS,IAAI,QAAQ;AAAA,EACpE;AACF;AA2FA,IAAM,wBAAN,cAAoC,UAAU;AAAA,EAC5C;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,UAAU;AACjB,WAAK,kBAAkB,SAAS,IAAI;AAAA,IACtC,OAAO;AACL,WAAK,kBAAkB,WAAW,IAAI;AAAA,IACxC;AAAA,EACF;AAAA,EACA,YAAY,UAAU,UAAU,SAAS,WAAW,mBAAmB,gBAAgB,QAAQ,UAAU;AACvG,UAAM,UAAU,UAAU,SAAS,WAAW,OAAO,OAAO,QAAQ;AACpE,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,SAAS,IAAI;AAAA,EACtC;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,kBAAkB,WAAW,IAAI;AACtC,UAAM,QAAQ;AAAA,EAChB;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,eAAe,aAAa,IAAI;AACrC,SAAK,cAAc,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,eAAe,WAAW,IAAI;AACnC,SAAK,cAAc,KAAK;AAAA,EAC1B;AACF;AAMA,IAAM,sCAAN,MAA0C;AAAA;AAAA,EAExC,YAAY;AAAA;AAAA,EAEZ,aAAa,WAAW;AAEtB,QAAI,KAAK,WAAW;AAClB,gBAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AAAA,IACvE;AACA,SAAK,YAAY,OAAK,KAAK,WAAW,WAAW,CAAC;AAClD,cAAU,QAAQ,kBAAkB,MAAM;AACxC,gBAAU,UAAU,iBAAiB,SAAS,KAAK,WAAW,IAAI;AAAA,IACpE,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,WAAW,WAAW;AACpB,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,cAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AACrE,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,WAAW,OAAO;AAC3B,UAAM,SAAS,MAAM;AACrB,UAAM,gBAAgB,UAAU;AAGhC,QAAI,UAAU,CAAC,cAAc,SAAS,MAAM,KAAK,CAAC,OAAO,UAAU,sBAAsB,GAAG;AAI1F,iBAAW,MAAM;AAEf,YAAI,UAAU,WAAW,CAAC,cAAc,SAAS,UAAU,UAAU,aAAa,GAAG;AACnF,oBAAU,0BAA0B;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAGA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAGhF,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA;AAAA,EAGrB,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,SAAS,WAAW;AAElB,SAAK,kBAAkB,KAAK,gBAAgB,OAAO,QAAM,OAAO,SAAS;AACzE,QAAI,QAAQ,KAAK;AACjB,QAAI,MAAM,QAAQ;AAChB,YAAM,MAAM,SAAS,CAAC,EAAE,SAAS;AAAA,IACnC;AACA,UAAM,KAAK,SAAS;AACpB,cAAU,QAAQ;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,cAAU,SAAS;AACnB,UAAM,QAAQ,KAAK;AACnB,UAAM,IAAI,MAAM,QAAQ,SAAS;AACjC,QAAI,MAAM,IAAI;AACZ,YAAM,OAAO,GAAG,CAAC;AACjB,UAAI,MAAM,QAAQ;AAChB,cAAM,MAAM,SAAS,CAAC,EAAE,QAAQ;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,YAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,WAAW,OAAO,oBAAoB;AAAA,EACtC,UAAU,OAAO,MAAM;AAAA,EACvB,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,UAAM,gBAAgB,OAAO,2BAA2B;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,iBAAiB,iBAAiB,IAAI,oCAAoC;AAAA,EACjF;AAAA,EACA,OAAO,SAAS,SAAS;AAAA,IACvB,OAAO;AAAA,EACT,GAAG;AACD,QAAI;AACJ,QAAI,OAAO,WAAW,WAAW;AAC/B,qBAAe;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,qBAAe;AAAA,IACjB;AACA,WAAO,IAAI,sBAAsB,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,mBAAmB,KAAK,gBAAgB,cAAc,KAAK,SAAS;AAAA,EAClK;AAAA,EACA,OAAO,YAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,8BAA6B;AAAA,IACtC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;AC5jBH,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAK5B,EAAAA,mBAAkBA,mBAAkB,QAAQ,IAAI,CAAC,IAAI;AAKrD,EAAAA,mBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AAKtD,EAAAA,mBAAkBA,mBAAkB,UAAU,IAAI,CAAC,IAAI;AACzD,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,IAAI;AAEJ,IAAI;AAEJ,SAAS,yBAAyB;AAChC,MAAI,2BAA2B,MAAM;AAGnC,QAAI,OAAO,aAAa,YAAY,CAAC,YAAY,OAAO,YAAY,cAAc,CAAC,SAAS;AAC1F,gCAA0B;AAC1B,aAAO;AAAA,IACT;AAEA,QAAI,oBAAoB,SAAS,gBAAgB,OAAO;AACtD,gCAA0B;AAAA,IAC5B,OAAO;AAGL,YAAM,mBAAmB,QAAQ,UAAU;AAC3C,UAAI,kBAAkB;AAKpB,kCAA0B,CAAC,4BAA4B,KAAK,iBAAiB,SAAS,CAAC;AAAA,MACzF,OAAO;AACL,kCAA0B;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,uBAAuB;AAE9B,MAAI,OAAO,aAAa,YAAY,CAAC,UAAU;AAC7C,WAAO,kBAAkB;AAAA,EAC3B;AACA,MAAI,qBAAqB,MAAM;AAE7B,UAAM,kBAAkB,SAAS,cAAc,KAAK;AACpD,UAAM,iBAAiB,gBAAgB;AACvC,oBAAgB,MAAM;AACtB,mBAAe,QAAQ;AACvB,mBAAe,WAAW;AAC1B,mBAAe,aAAa;AAC5B,mBAAe,gBAAgB;AAC/B,mBAAe,WAAW;AAC1B,UAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,UAAM,eAAe,QAAQ;AAC7B,iBAAa,QAAQ;AACrB,iBAAa,SAAS;AACtB,oBAAgB,YAAY,OAAO;AACnC,aAAS,KAAK,YAAY,eAAe;AACzC,wBAAoB,kBAAkB;AAItC,QAAI,gBAAgB,eAAe,GAAG;AAKpC,sBAAgB,aAAa;AAC7B,0BAAoB,gBAAgB,eAAe,IAAI,kBAAkB,UAAU,kBAAkB;AAAA,IACvG;AACA,oBAAgB,OAAO;AAAA,EACzB;AACA,SAAO;AACT;;;AC3FA,SAAS,qBAAqB;AAK5B;AAAA;AAAA,IAEE,OAAO,cAAc,eAAe,CAAC,CAAC;AAAA,IAEtC,OAAO,YAAY,eAAe,CAAC,CAAC;AAAA,IAEpC,OAAO,SAAS,eAAe,CAAC,CAAC;AAAA,IAEjC,OAAO,UAAU,eAAe,CAAC,CAAC;AAAA;AAEtC;;;ACPA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,YAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,YAAsB,gBAAG,2BAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAI;AAEJ,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAkB;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAM;AAE/M,SAAS,yBAAyB;AAEhC,MAAI,qBAAqB;AACvB,WAAO;AAAA,EACT;AAIA,MAAI,OAAO,aAAa,YAAY,CAAC,UAAU;AAC7C,0BAAsB,IAAI,IAAI,mBAAmB;AACjD,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,SAAS,cAAc,OAAO;AACrD,wBAAsB,IAAI,IAAI,oBAAoB,OAAO,WAAS;AAChE,qBAAiB,aAAa,QAAQ,KAAK;AAC3C,WAAO,iBAAiB,SAAS;AAAA,EACnC,CAAC,CAAC;AACF,SAAO;AACT;;;ACpDA,SAAS,sBAAsB,OAAO;AACpC,SAAO,SAAS,QAAQ,GAAG,KAAK,OAAO;AACzC;;;ACFA,SAAS,oBAAoB,OAAO;AAClC,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,SAAO,OAAO,UAAU,WAAW,QAAQ,GAAG,KAAK;AACrD;;;ACiBA,SAAS,kBAAkB,OAAO,YAAY,OAAO;AACnD,QAAM,SAAS,CAAC;AAChB,MAAI,SAAS,MAAM;AACjB,UAAM,eAAe,MAAM,QAAQ,KAAK,IAAI,QAAQ,GAAG,KAAK,GAAG,MAAM,SAAS;AAC9E,eAAW,eAAe,cAAc;AACtC,YAAM,gBAAgB,GAAG,WAAW,GAAG,KAAK;AAC5C,UAAI,eAAe;AACjB,eAAO,KAAK,aAAa;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AC3BA,IAAI;AAAA,CACH,SAAUC,cAAa;AACtB,EAAAA,aAAYA,aAAY,WAAW,IAAI,CAAC,IAAI;AAC5C,EAAAA,aAAYA,aAAY,SAAS,IAAI,CAAC,IAAI;AAC1C,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,EAAAA,aAAYA,aAAY,QAAQ,IAAI,CAAC,IAAI;AAC3C,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAIpC,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,QAAQ,YAAY;AAAA,EACpB,YAAY,WACZ,SACA,QACA,uCAAuC,OAAO;AAC5C,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,uCAAuC;AAAA,EAC9C;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,UAAU,cAAc,IAAI;AAAA,EACnC;AACF;AAGA,IAAM,iCAAiC,gCAAgC;AAAA,EACrE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,qBAAN,MAAyB;AAAA,EACvB,UAAU,oBAAI,IAAI;AAAA;AAAA,EAElB,WAAW,QAAQ,MAAM,SAAS,SAAS;AACzC,UAAM,mBAAmB,KAAK,QAAQ,IAAI,IAAI;AAC9C,QAAI,kBAAkB;AACpB,YAAM,qBAAqB,iBAAiB,IAAI,OAAO;AACvD,UAAI,oBAAoB;AACtB,2BAAmB,IAAI,OAAO;AAAA,MAChC,OAAO;AACL,yBAAiB,IAAI,SAAS,oBAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAAA,MAClD;AAAA,IACF,OAAO;AACL,WAAK,QAAQ,IAAI,MAAM,oBAAI,IAAI,CAAC,CAAC,SAAS,oBAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,aAAO,kBAAkB,MAAM;AAC7B,iBAAS,iBAAiB,MAAM,KAAK,uBAAuB,8BAA8B;AAAA,MAC5F,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,MAAM,SAAS,SAAS;AACpC,UAAM,mBAAmB,KAAK,QAAQ,IAAI,IAAI;AAC9C,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,UAAM,qBAAqB,iBAAiB,IAAI,OAAO;AACvD,QAAI,CAAC,oBAAoB;AACvB;AAAA,IACF;AACA,uBAAmB,OAAO,OAAO;AACjC,QAAI,mBAAmB,SAAS,GAAG;AACjC,uBAAiB,OAAO,OAAO;AAAA,IACjC;AACA,QAAI,iBAAiB,SAAS,GAAG;AAC/B,WAAK,QAAQ,OAAO,IAAI;AACxB,eAAS,oBAAoB,MAAM,KAAK,uBAAuB,8BAA8B;AAAA,IAC/F;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB,WAAS;AAC/B,UAAM,SAAS,gBAAgB,KAAK;AACpC,QAAI,QAAQ;AACV,WAAK,QAAQ,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,YAAY;AAC3D,YAAI,YAAY,UAAU,QAAQ,SAAS,MAAM,GAAG;AAClD,mBAAS,QAAQ,aAAW,QAAQ,YAAY,KAAK,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAMA,IAAM,+BAA+B;AAAA,EACnC,eAAe;AAAA,EACf,cAAc;AAChB;AAKA,IAAM,2BAA2B;AAEjC,IAAM,+BAA+B,gCAAgC;AAAA,EACnE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,oBAAoB,CAAC,aAAa,YAAY;AAEpD,IAAM,kBAAkB,CAAC,WAAW,cAAc,YAAY,aAAa;AAC3E,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,YAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,2BAA2B,EAAE;AAAA,IACzC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC7D,QAAQ,CAAC,6jBAA6jB;AAAA,IACtkB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,2BAA2B;AAAA,MAC7B;AAAA,MACA,QAAQ,CAAC,6jBAA6jB;AAAA,IACxkB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,iBAAiB,oBAAI,IAAI;AAAA;AAAA,EAEzB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,6BAA6B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B;AAAA,EACA,OAAO,gBAAgB,IAAI,mBAAmB;AAAA,EAC9C,YAAY,SAAS,SAAS,qBAAqB,WAAW,UAAU;AACtE,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,YAAY;AAEjB,QAAI,UAAU,WAAW;AACvB,WAAK,oBAAoB,cAAc,mBAAmB;AAAA,IAC5D;AACA,QAAI,UAAU;AACZ,eAAS,IAAI,sBAAsB,EAAE,KAAK,sBAAsB;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,GAAG,GAAG,SAAS,CAAC,GAAG;AAC9B,UAAM,gBAAgB,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,sBAAsB;AAChH,UAAM,kBAAkB,kCACnB,+BACA,OAAO;AAEZ,QAAI,OAAO,UAAU;AACnB,UAAI,cAAc,OAAO,cAAc,QAAQ;AAC/C,UAAI,cAAc,MAAM,cAAc,SAAS;AAAA,IACjD;AACA,UAAM,SAAS,OAAO,UAAU,yBAAyB,GAAG,GAAG,aAAa;AAC5E,UAAM,UAAU,IAAI,cAAc;AAClC,UAAM,UAAU,IAAI,cAAc;AAClC,UAAM,gBAAgB,gBAAgB;AACtC,UAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,WAAO,UAAU,IAAI,oBAAoB;AACzC,WAAO,MAAM,OAAO,GAAG,UAAU,MAAM;AACvC,WAAO,MAAM,MAAM,GAAG,UAAU,MAAM;AACtC,WAAO,MAAM,SAAS,GAAG,SAAS,CAAC;AACnC,WAAO,MAAM,QAAQ,GAAG,SAAS,CAAC;AAGlC,QAAI,OAAO,SAAS,MAAM;AACxB,aAAO,MAAM,kBAAkB,OAAO;AAAA,IACxC;AACA,WAAO,MAAM,qBAAqB,GAAG,aAAa;AAClD,SAAK,kBAAkB,YAAY,MAAM;AAKzC,UAAM,iBAAiB,OAAO,iBAAiB,MAAM;AACrD,UAAM,yBAAyB,eAAe;AAC9C,UAAM,yBAAyB,eAAe;AAM9C,UAAM,sCAAsC,2BAA2B;AAAA;AAAA,IAGvE,2BAA2B,QAAQ,2BAA2B;AAAA,IAE9D,cAAc,UAAU,KAAK,cAAc,WAAW;AAEtD,UAAM,YAAY,IAAI,UAAU,MAAM,QAAQ,QAAQ,mCAAmC;AAKzF,WAAO,MAAM,YAAY;AACzB,cAAU,QAAQ,YAAY;AAC9B,QAAI,CAAC,OAAO,YAAY;AACtB,WAAK,6BAA6B;AAAA,IACpC;AACA,QAAI,iBAAiB;AAGrB,QAAI,CAAC,wCAAwC,iBAAiB,gBAAgB,eAAe;AAC3F,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAM,kBAAkB,MAAM;AAE5B,cAAI,gBAAgB;AAClB,2BAAe,gBAAgB;AAAA,UACjC;AACA,uBAAa,aAAa;AAC1B,eAAK,wBAAwB,SAAS;AAAA,QACxC;AACA,cAAM,qBAAqB,MAAM,KAAK,eAAe,SAAS;AAQ9D,cAAM,gBAAgB,WAAW,oBAAoB,gBAAgB,GAAG;AACxE,eAAO,iBAAiB,iBAAiB,eAAe;AAIxD,eAAO,iBAAiB,oBAAoB,kBAAkB;AAC9D,yBAAiB;AAAA,UACf;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,eAAe,IAAI,WAAW,cAAc;AAGjD,QAAI,uCAAuC,CAAC,eAAe;AACzD,WAAK,wBAAwB,SAAS;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,WAAW;AAEvB,QAAI,UAAU,UAAU,YAAY,cAAc,UAAU,UAAU,YAAY,QAAQ;AACxF;AAAA,IACF;AACA,UAAM,WAAW,UAAU;AAC3B,UAAM,kBAAkB,kCACnB,+BACA,UAAU,OAAO;AAItB,aAAS,MAAM,qBAAqB,GAAG,gBAAgB,YAAY;AACnE,aAAS,MAAM,UAAU;AACzB,cAAU,QAAQ,YAAY;AAG9B,QAAI,UAAU,wCAAwC,CAAC,gBAAgB,cAAc;AACnF,WAAK,wBAAwB,SAAS;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,kBAAkB,EAAE,QAAQ,YAAU,OAAO,QAAQ,CAAC;AAAA,EAC7D;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,kBAAkB,EAAE,QAAQ,YAAU;AACzC,UAAI,CAAC,OAAO,OAAO,YAAY;AAC7B,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,mBAAmB,qBAAqB;AACtC,UAAM,UAAU,cAAc,mBAAmB;AACjD,QAAI,CAAC,KAAK,UAAU,aAAa,CAAC,WAAW,YAAY,KAAK,iBAAiB;AAC7E;AAAA,IACF;AAEA,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB;AAGvB,sBAAkB,QAAQ,UAAQ;AAChC,sBAAe,cAAc,WAAW,KAAK,SAAS,MAAM,SAAS,IAAI;AAAA,IAC3E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAAO;AACjB,QAAI,MAAM,SAAS,aAAa;AAC9B,WAAK,aAAa,KAAK;AAAA,IACzB,WAAW,MAAM,SAAS,cAAc;AACtC,WAAK,cAAc,KAAK;AAAA,IAC1B,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AAIA,QAAI,CAAC,KAAK,4BAA4B;AAMpC,WAAK,QAAQ,kBAAkB,MAAM;AACnC,wBAAgB,QAAQ,UAAQ;AAC9B,eAAK,gBAAgB,iBAAiB,MAAM,MAAM,4BAA4B;AAAA,QAChF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,6BAA6B;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB,WAAW;AACjC,QAAI,UAAU,UAAU,YAAY,WAAW;AAC7C,WAAK,wBAAwB,SAAS;AAAA,IACxC,WAAW,UAAU,UAAU,YAAY,YAAY;AACrD,WAAK,eAAe,SAAS;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,WAAW;AACjC,UAAM,8BAA8B,cAAc,KAAK;AACvD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,UAAU;AACd,cAAU,QAAQ,YAAY;AAK9B,QAAI,CAAC,eAAe,CAAC,+BAA+B,CAAC,KAAK,iBAAiB;AACzE,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,WAAW;AACxB,UAAM,iBAAiB,KAAK,eAAe,IAAI,SAAS,KAAK;AAC7D,SAAK,eAAe,OAAO,SAAS;AAEpC,QAAI,CAAC,KAAK,eAAe,MAAM;AAC7B,WAAK,iBAAiB;AAAA,IACxB;AAGA,QAAI,cAAc,KAAK,4BAA4B;AACjD,WAAK,6BAA6B;AAAA,IACpC;AACA,cAAU,QAAQ,YAAY;AAC9B,QAAI,mBAAmB,MAAM;AAC3B,gBAAU,QAAQ,oBAAoB,iBAAiB,eAAe,eAAe;AACrF,gBAAU,QAAQ,oBAAoB,oBAAoB,eAAe,kBAAkB;AAC3F,UAAI,eAAe,kBAAkB,MAAM;AACzC,qBAAa,eAAe,aAAa;AAAA,MAC3C;AAAA,IACF;AACA,cAAU,QAAQ,OAAO;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa,OAAO;AAGlB,UAAM,kBAAkB,gCAAgC,KAAK;AAC7D,UAAM,mBAAmB,KAAK,wBAAwB,KAAK,IAAI,IAAI,KAAK,uBAAuB;AAC/F,QAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,mBAAmB,CAAC,kBAAkB;AACzE,WAAK,iBAAiB;AACtB,WAAK,aAAa,MAAM,SAAS,MAAM,SAAS,KAAK,QAAQ,YAAY;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,iCAAiC,KAAK,GAAG;AAI5E,WAAK,uBAAuB,KAAK,IAAI;AACrC,WAAK,iBAAiB;AAGtB,YAAM,UAAU,MAAM;AAGtB,UAAI,SAAS;AACX,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,eAAK,aAAa,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC,EAAE,SAAS,KAAK,QAAQ,YAAY;AAAA,QACrF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,eAAe;AACb,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AACA,SAAK,iBAAiB;AAEtB,SAAK,kBAAkB,EAAE,QAAQ,YAAU;AAGzC,YAAM,YAAY,OAAO,UAAU,YAAY,WAAW,OAAO,OAAO,wBAAwB,OAAO,UAAU,YAAY;AAC7H,UAAI,CAAC,OAAO,OAAO,cAAc,WAAW;AAC1C,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,WAAO,MAAM,KAAK,KAAK,eAAe,KAAK,CAAC;AAAA,EAC9C;AAAA;AAAA,EAEA,uBAAuB;AACrB,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,wBAAkB,QAAQ,UAAQ,gBAAe,cAAc,cAAc,MAAM,SAAS,IAAI,CAAC;AACjG,UAAI,KAAK,4BAA4B;AACnC,wBAAgB,QAAQ,UAAQ,QAAQ,oBAAoB,MAAM,MAAM,4BAA4B,CAAC;AACrG,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACF;AAIA,SAAS,yBAAyB,GAAG,GAAG,MAAM;AAC5C,QAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AACxE,QAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC;AACxE,SAAO,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AAChD;AAGA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAChF,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,cAAc,OAAO,UAAU;AAAA,EAC/B,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,OAAO;AACT,WAAK,wBAAwB;AAAA,IAC/B;AACA,SAAK,YAAY;AACjB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY,KAAK,YAAY;AAAA,EAC3C;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB;AAAA,EACjB,cAAc;AACZ,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAM,WAAW,OAAO,QAAQ;AAChC,UAAM,gBAAgB,OAAO,2BAA2B;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW,OAAO,QAAQ;AAGhC,SAAK,iBAAiB,iBAAiB,CAAC;AACxC,SAAK,kBAAkB,IAAI,eAAe,MAAM,QAAQ,KAAK,aAAa,UAAU,QAAQ;AAAA,EAC9F;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB;AACtB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,qBAAqB;AAAA,EAC5C;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,gBAAgB,WAAW;AAAA,EAClC;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,gBAAgB,wBAAwB;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,eAAe;AACjB,WAAO;AAAA,MACL,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,WAAW,iDACN,KAAK,eAAe,YACnB,KAAK,mBAAmB,mBAAmB;AAAA,QAC7C,eAAe;AAAA,QACf,cAAc;AAAA,MAChB,IAAI,CAAC,IACF,KAAK;AAAA,MAEV,sBAAsB,KAAK,eAAe;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,WAAO,KAAK,YAAY,CAAC,CAAC,KAAK,eAAe;AAAA,EAChD;AAAA;AAAA,EAEA,+BAA+B;AAC7B,QAAI,CAAC,KAAK,YAAY,KAAK,gBAAgB;AACzC,WAAK,gBAAgB,mBAAmB,KAAK,OAAO;AAAA,IACtD;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,WAAW,IAAI,GAAG,QAAQ;AAC/B,QAAI,OAAO,cAAc,UAAU;AACjC,aAAO,KAAK,gBAAgB,aAAa,WAAW,GAAG,kCAClD,KAAK,eACL,OACJ;AAAA,IACH,OAAO;AACL,aAAO,KAAK,gBAAgB,aAAa,GAAG,GAAG,kCAC1C,KAAK,eACL,UACJ;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,YAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,IACzD,WAAW,CAAC,GAAG,YAAY;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,wBAAwB,IAAI,SAAS;AAAA,MACtD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,kBAAkB,OAAO;AAAA,MACpC,WAAW,CAAC,GAAG,sBAAsB,WAAW;AAAA,MAChD,UAAU,CAAC,GAAG,qBAAqB,UAAU;AAAA,MAC7C,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,MACvC,WAAW,CAAC,GAAG,sBAAsB,WAAW;AAAA,MAChD,UAAU,CAAC,GAAG,qBAAqB,UAAU;AAAA,MAC7C,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,IAC5C;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,gCAAgC;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;ACtrBH,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AACX;AAMA,IAAM,0BAA0B,CAAC,SAAS,aAAa,cAAc,YAAY;AAEjF,IAAM,yBAAyB;AAE/B,IAAM,qBAAqB;AAE3B,IAAM,oBAAoB;AAE1B,IAAM,oBAAoB;AAS1B,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,OAAO,QAAQ;AAAA,EAC3B,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,uBAAuB,OAAO,2BAA2B;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,SAAS,oBAAI,IAAI;AAAA,EACjB,cAAc;AACZ,UAAM,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AACnE,SAAK,iBAAiB,KAAK,QAAQ,kBAAkB,MAAM;AACzD,aAAO,wBAAwB,IAAI,UAAQ,sBAAsB,UAAU,KAAK,WAAW,MAAM,KAAK,gBAAgB,oBAAoB,CAAC;AAAA,IAC7I,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,KAAK,OAAO,KAAK;AAC/B,eAAW,QAAQ,OAAO;AACxB,WAAK,cAAc,IAAI;AAAA,IACzB;AACA,SAAK,eAAe,QAAQ,aAAW,QAAQ,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,MAAM,QAAQ;AAE5B,SAAK,aAAa,wBAAwB,KAAK,sBAAsB,aAAa,EAAE;AAEpF,QAAI,OAAO,aAAa,CAAC,KAAK,aAAa,kBAAkB,GAAG;AAC9D,WAAK,aAAa,oBAAoB,OAAO,aAAa,EAAE;AAAA,IAC9D;AAEA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AACA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,MAAM,UAAU;AAC1B,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AAEnC,QAAI,QAAQ;AACV,aAAO,OAAO,iBAAiB;AAC/B,UAAI,CAAC,YAAY,CAAC,OAAO,gBAAgB;AACvC,eAAO,iBAAiB;AACxB,eAAO,SAAS,mBAAmB,IAAI;AAAA,MACzC;AAAA,IACF,WAAW,UAAU;AAGnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC,OAAO;AACL,WAAK,gBAAgB,iBAAiB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,WAAS;AACxB,UAAM,cAAc,gBAAgB,KAAK;AACzC,QAAI,uBAAuB,aAAa;AAEtC,YAAM,UAAU,YAAY,QAAQ,IAAI,sBAAsB,KAAK,KAAK,sBAAsB,aAAa,EAAE,IAAI;AACjH,UAAI,SAAS;AACX,aAAK,cAAc,OAAO;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,MAAM;AAClB,QAAI,CAAC,KAAK,aAAa,KAAK,OAAO,IAAI,IAAI,GAAG;AAC5C;AAAA,IACF;AAEA,SAAK,cAAc,aAAa,GAAG,OAAO;AAC1C,UAAM,WAAW,KAAK,UAAU,cAAc,MAAM;AACpD,aAAS,UAAU,IAAI,cAAc,KAAK,aAAa,kBAAkB,CAAC;AAC1E,SAAK,OAAO,QAAQ;AACpB,UAAM,mBAAmB,KAAK,mBAAmB;AACjD,UAAM,gBAAgB,KAAK;AAC3B,UAAM,gBAAgB,mBAAmB,IAAI,eAAe,WAAW,iBAAiB,6BAA6B;AACrH,UAAM,eAAe,mBAAmB,IAAI,eAAe,WAAW,gBAAgB,6BAA6B;AACnH,UAAM,SAAS;AAAA,MACb,gBAAgB,oBAAoB,eAAe,YAAY,KAAK,aAAa,iBAAiB;AAAA,MAClG,cAAc;AAAA,QACZ,UAAU,KAAK,aAAa,iBAAiB;AAAA,QAC7C,sBAAsB,eAAe;AAAA,QACrC,WAAW;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,WAAW,IAAI,eAAe,QAAQ,KAAK,SAAS,UAAU,KAAK,WAAW,KAAK,SAAS;AAClG,UAAM,iBAAiB,CAAC,OAAO;AAC/B,QAAI,gBAAgB;AAClB,eAAS,mBAAmB,IAAI;AAAA,IAClC;AACA,SAAK,OAAO,IAAI,MAAM;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,gBAAgB,sBAAsB;AAAA,EAC7C;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AACnC,QAAI,QAAQ;AACV,aAAO,SAAS,qBAAqB;AACrC,WAAK,OAAO,OAAO,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAO,YAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACpKH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,YAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC9D,QAAQ,CAAC,8jBAAgkB;AAAA,IACzkB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,8jBAAgkB;AAAA,IAC3kB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACzBH,IAAM,MAAM,CAAC,mBAAmB,EAAE;AAClC,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAEhE,IAAM,kBAAkB;AAAA,EACtB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,mCAAmC;AAAA;AAAA;AAAA;AAAA,EAInC,wBAAwB;AAAA;AAAA;AAAA,EAGxB,+BAA+B;AAAA,EAC/B,WAAW;AACb;AAEA,IAAM,+BAA+B,CAAC;AAAA,EACpC,WAAW;AAAA,EACX,YAAY,CAAC,cAAc,gBAAgB;AAC7C,GAAG;AAAA,EACD,WAAW;AAAA,EACX,YAAY,CAAC,cAAc,0BAA0B,2BAA2B;AAClF,GAAG;AAAA,EACD,WAAW;AAAA,EACX,YAAY,CAAC,cAAc,sBAAsB,uBAAuB;AAC1E,GAAG;AAAA,EACD,WAAW;AAAA,EACX,YAAY,CAAC,cAAc,wBAAwB,yBAAyB;AAC9E,GAAG;AAAA,EACD,WAAW;AAAA,EACX,YAAY,CAAC,WAAW,oBAAoB,aAAa;AAC3D,GAAG;AAAA,EACD,WAAW;AAAA,EACX,YAAY,CAAC,WAAW,oBAAoB,iBAAiB,kBAAkB;AACjF,GAAG;AAAA,EACD,WAAW;AAAA,EACX,YAAY,CAAC,mBAAmB,qBAAqB;AACvD,CAAC;AAED,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,MAAM;AAAA,EACvB,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,gBAAgB,OAAO,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,gBAAgB,OAAO,eAAe;AAAA;AAAA,EAEtC,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQT;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB;AACtB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,iBAAiB;AAAA;AAAA,EAEjB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA;AAAA,EACA,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,UAAM,SAAS,OAAO,mBAAmB;AAAA,MACvC,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,YAAY,QAAQ;AAC1B,SAAK,sBAAsB,QAAQ,uBAAuB;AAC1D,SAAK,QAAQ,QAAQ,SAAS;AAC9B,SAAK,eAAe,gBAAgB,SAAS;AAAA,MAC3C,WAAW;AAAA,IACb,CAAC;AAGD,eAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF,KAAK,8BAA8B;AACjC,UAAI,QAAQ,aAAa,SAAS,GAAG;AACnC,kBAAU,IAAI,GAAG,UAAU;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,cAAc,QAAQ,KAAK,aAAa,IAAI;AAAA,EACnD;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,SAAK,eAAe,cAAc,KAAK,YAAY,aAAa;AAAA,EAClE;AAAA;AAAA,EAEA,MAAM,SAAS,WAAW,SAAS;AACjC,QAAI,QAAQ;AACV,WAAK,cAAc,SAAS,KAAK,YAAY,eAAe,QAAQ,OAAO;AAAA,IAC7E,OAAO;AACL,WAAK,YAAY,cAAc,MAAM,OAAO;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,gBAAgB,MAAM;AAC7B,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,YAAY,KAAK,sBAAsB,OAAO;AAAA,EAC5D;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,uBAAuB,CAAC,KAAK,WAAW,OAAO;AAAA,EAC7D;AAAA,EACA,wBAAwB;AACtB,SAAK,eAAe,YAAY,KAAK,YAAY,eAAe,KAAK,iBAAiB,KAAK,QAAQ;AAAA,EACrG;AAAA,EACA,OAAO,YAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,cAAc,CAAC,GAAG,iBAAiB,gBAAgB,gBAAgB;AAAA,MACnE,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,IACzF;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,IAAM,kBAAkB;AAAA;AAAA;AAAA,EAGtB,mBAAmB;AAAA,EACnB,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,mCAAmC;AAAA;AAAA;AAAA;AAAA,EAInC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA;AAAA;AAAA;AAAA,EAIxB,wBAAwB;AAAA;AAAA;AAAA,EAGxB,+BAA+B;AAAA,EAC/B,WAAW;AACb;AAIA,IAAM,gBAAN,MAAM,uBAAsB,cAAc;AAAA,EACxC,YAAY,OAAO,SAAS;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,WAAW;AACT,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,gBAAgB,KAAK,UAAU,OAAO,KAAK,YAAY,eAAe,SAAS,KAAK,mBAAmB;AAAA,IAC9G,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,sBAAsB,WAAS;AAE7B,QAAI,KAAK,UAAU;AACjB,YAAM,eAAe;AACrB,YAAM,yBAAyB;AAAA,IACjC;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,gBAAgB,MAAM;AAC7B,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,OAAO,YAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,oCAA+B,kCAAgC,gCAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS;AAC7C,eAAO,SAAS,OAAO,SAAY,gBAAgB,KAAK;AAAA,MAC1D,CAAC;AAAA,IACH;AAAA,IACA,UAAU,CAAI,oCAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS;AAClB,iBAAO,SAAS,OAAO,SAAY,gBAAgB,KAAK;AAAA,QAC1D;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,gBAAN,MAAM,uBAAsB,cAAc;AAAA,EACxC,cAAc;AACZ,UAAM;AACN,SAAK,cAAc,gBAAgB,KAAK,YAAY,eAAe;AAAA,MACjE,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,OAAO,YAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,mBAAmB,EAAE,CAAC;AAAA,IAC7C,UAAU;AAAA,IACV,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,YAAY,IAAI,sBAAsB,CAAC,EAAE,iBAAiB,IAAI,iBAAiB,CAAC;AAC/F,QAAG,qBAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,sBAAY,2BAA2B,IAAI,QAAQ,EAAE,uCAAuC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,KAAK,EAAE,uBAAuB,IAAI;AAAA,MACrP;AAAA,IACF;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,oCAA0B;AAAA,IACxC,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,oCAAoC,yBAAyB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC3I,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,0BAAgB;AACnB,QAAG,oBAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,uBAAa,CAAC;AACjB,QAAG,oBAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,8yHAAkzH,kVAAkV;AAAA,IAC7oI,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,8yHAAkzH,kVAAkV;AAAA,IAC/oI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,gBAAN,MAAM,uBAAsB,cAAc;AAAA,EACxC,OAAO,YAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,oCAA+B,kCAAgC,gCAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,KAAK,mBAAmB,EAAE,CAAC;AAAA,IACxC,UAAU;AAAA,IACV,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,YAAY,IAAI,sBAAsB,CAAC,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,sBAAsB,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,iBAAiB,CAAC;AACzK,QAAG,qBAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,sBAAY,2BAA2B,IAAI,QAAQ,EAAE,uCAAuC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,KAAK,EAAE,uBAAuB,IAAI;AAAA,MACrP;AAAA,IACF;AAAA,IACA,UAAU,CAAC,aAAa,WAAW;AAAA,IACnC,UAAU,CAAI,oCAA0B;AAAA,IACxC,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,oCAAoC,yBAAyB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC3I,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,0BAAgB;AACnB,QAAG,oBAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,uBAAa,CAAC;AACjB,QAAG,oBAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,KAAK,GAAG;AAAA,IACjB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,8yHAAkzH,kVAAkV;AAAA,IAC/oI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AClYH,IAAM,eAAe,IAAI,eAAe,eAAe;AAAA,EACrD,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,uBAAuB;AAC9B,SAAO,OAAO,QAAQ;AACxB;AAGA,IAAM,qBAAqB;AAE3B,SAAS,uBAAuB,UAAU;AACxC,QAAM,QAAQ,UAAU,YAAY,KAAK;AACzC,MAAI,UAAU,UAAU,OAAO,cAAc,eAAe,WAAW,UAAU;AAC/E,WAAO,mBAAmB,KAAK,UAAU,QAAQ,IAAI,QAAQ;AAAA,EAC/D;AACA,SAAO,UAAU,QAAQ,QAAQ;AACnC;AAKA,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA,EAEnB,QAAQ;AAAA;AAAA,EAER,SAAS,IAAI,aAAa;AAAA,EAC1B,cAAc;AACZ,UAAM,YAAY,OAAO,cAAc;AAAA,MACrC,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,WAAW;AACb,YAAM,UAAU,UAAU,OAAO,UAAU,KAAK,MAAM;AACtD,YAAM,UAAU,UAAU,kBAAkB,UAAU,gBAAgB,MAAM;AAC5E,WAAK,QAAQ,uBAAuB,WAAW,WAAW,KAAK;AAAA,IACjE;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA,EACA,OAAO,YAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,IACxB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACpEH,IAAM,MAAN,MAAM,KAAI;AAAA;AAAA,EAER,OAAO;AAAA;AAAA,EAEP,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI,OAAO;AACb,UAAM,gBAAgB,KAAK;AAI3B,SAAK,OAAO,uBAAuB,KAAK;AACxC,SAAK,UAAU;AACf,QAAI,kBAAkB,KAAK,QAAQ,KAAK,gBAAgB;AACtD,WAAK,OAAO,KAAK,KAAK,IAAI;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA,EACA,OAAO,YAAO,SAAS,YAAY,mBAAmB;AACpD,WAAO,KAAK,qBAAqB,MAAK;AAAA,EACxC;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,OAAO,EAAE,CAAC;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,iBAAiB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,OAAO,IAAI,OAAO;AAAA,MACnC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,IACP;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,KAAK;AAAA,IAChB,UAAU,CAAI,6BAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,KAAK,CAAC;AAAA,IAC5E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,YAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,GAAG;AAAA,IACb,SAAS,CAAC,GAAG;AAAA,EACf,CAAC;AAAA,EACD,OAAO,YAAsB,gBAAG,2BAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,GAAG;AAAA,MACb,SAAS,CAAC,GAAG;AAAA,IACf,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACzGH,IAAM,yBAAyB,IAAI,eAAe,qBAAqB;AAAA,EACrE,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AASD,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AAGZ,WAAO,wBAAwB,EAAE,qCAAqC;AAAA,EACxE;AAAA,EACA,OAAO,YAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU;AAAA,IACpB,SAAS,CAAC,UAAU;AAAA,EACtB,CAAC;AAAA,EACD,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY,UAAU;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,MACpB,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;AC5CH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,YAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,SAAS;AAAA,IACpC,SAAS,CAAC,WAAW,eAAe;AAAA,EACtC,CAAC;AAAA,EACD,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,SAAS;AAAA,MACpC,SAAS,CAAC,WAAW,eAAe;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACxBH,IAAMC,OAAM,CAAC,cAAc,EAAE;AAC7B,IAAMC,OAAM,CAAC,CAAC,CAAC,IAAI,GAAG,kBAAkB,GAAG,mBAAmB,EAAE,GAAG,CAAC,YAAY,GAAG,mBAAmB,EAAE,GAAG,CAAC,IAAI,iBAAiB,IAAI,GAAG,mBAAmB,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,mBAAmB,IAAI,GAAG,gBAAgB,GAAG,CAAC,YAAY,mBAAmB,EAAE,GAAG,CAAC,IAAI,iBAAiB,IAAI,mBAAmB,EAAE,CAAC,CAAC;AAC/S,IAAMC,OAAM,CAAC,mHAAmH,KAAK,+FAA+F;AACpO,IAAMC,OAAM;AACZ,IAAM,MAAM;AACZ,IAAM,MAAM,CAAC,WAAW,EAAE;AAC1B,IAAM,MAAM,CAAC,gBAAgB,EAAE;AAC/B,IAAM,MAAM;AAyBZ,IAAM,YAAN,MAAM,mBAAkB,cAAc;AAAA,EACpC,OAAO,YAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,gCAA2B,8BAA4B,gCAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,cAAc,EAAE,GAAG,CAAC,UAAU,qBAAqB,EAAE,GAAG,CAAC,UAAU,mBAAmB,EAAE,GAAG,CAAC,UAAU,sBAAsB,EAAE,CAAC;AAAA,IACtJ,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,YAAY,IAAI,sBAAsB,CAAC,EAAE,iBAAiB,IAAI,iBAAiB,CAAC;AAC/F,QAAG,qBAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,sBAAY,2BAA2B,IAAI,QAAQ,EAAE,uCAAuC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,KAAK,EAAE,uBAAuB,IAAI;AAAA,MACrP;AAAA,IACF;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,oCAA0B;AAAA,IACxC,OAAOH;AAAA,IACP,oBAAoBE;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC1I,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,0BAAgBD,IAAG;AACtB,QAAG,oBAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,uBAAa,CAAC;AACjB,QAAG,yBAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,uBAAa;AAChB,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,oBAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAAA,MACzC;AACA,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,sBAAsB,CAAC,IAAI,MAAM,EAAE,mBAAmB,IAAI,MAAM;AAAA,MACjF;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,8/jBAAkgkB,kVAAkV;AAAA,IAC71kB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA;AAAA;AAAA,MAIV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,8/jBAAkgkB,kVAAkV;AAAA,IAC/1kB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAWH,IAAM,YAAN,MAAM,mBAAkB,cAAc;AAAA,EACpC,OAAO,YAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,gCAA2B,8BAA4B,gCAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,KAAK,cAAc,EAAE,GAAG,CAAC,KAAK,qBAAqB,EAAE,GAAG,CAAC,KAAK,mBAAmB,EAAE,GAAG,CAAC,KAAK,sBAAsB,EAAE,CAAC;AAAA,IAClI,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,YAAY,IAAI,sBAAsB,CAAC,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,sBAAsB,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,iBAAiB,CAAC;AACzK,QAAG,qBAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,sBAAY,2BAA2B,IAAI,QAAQ,EAAE,uCAAuC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,KAAK,EAAE,uBAAuB,IAAI;AAAA,MACrP;AAAA,IACF;AAAA,IACA,UAAU,CAAC,aAAa,WAAW;AAAA,IACnC,UAAU,CAAI,oCAA0B;AAAA,IACxC,OAAOD;AAAA,IACP,oBAAoBE;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC1I,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,0BAAgBD,IAAG;AACtB,QAAG,oBAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,uBAAa,CAAC;AACjB,QAAG,yBAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,uBAAa;AAChB,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,oBAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAAA,MACzC;AACA,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,sBAAsB,CAAC,IAAI,MAAM,EAAE,mBAAmB,IAAI,MAAM;AAAA,MACjF;AAAA,IACF;AAAA,IACA,QAAQ,CAACE,MAAK,GAAG;AAAA,IACjB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,MACN,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,8/jBAAkgkB,kVAAkV;AAAA,IAC/1kB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,0BAA0B,IAAI,eAAe,+BAA+B;AAAA,EAChF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,kCAAkC;AACzC,SAAO;AAAA;AAAA,IAEL,OAAO;AAAA,EACT;AACF;AAEA,IAAM,WAAW,gCAAgC;AAQjD,IAAM,eAAN,MAAM,sBAAqB,cAAc;AAAA,EACvC,WAAW,OAAO,yBAAyB;AAAA,IACzC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS;AAAA,EACT;AAAA,EACA,cAAc;AACZ,UAAM;AACN,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,QAAQ,KAAK,SAAS,SAAS,SAAS;AAAA,EAC/C;AAAA,EACA,OAAO,YAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,WAAW,EAAE,CAAC;AAAA,IACrC,UAAU;AAAA,IACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,YAAY,IAAI,sBAAsB,CAAC,EAAE,iBAAiB,IAAI,iBAAiB,CAAC;AAC/F,QAAG,qBAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,sBAAY,2BAA2B,IAAI,QAAQ,EAAE,uCAAuC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,KAAK,EAAE,uBAAuB,IAAI,EAAE,qBAAqB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,QAAQ;AAAA,MAC9T;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,oCAA0B;AAAA,IACxC,OAAO;AAAA,IACP,oBAAoBD;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC1I,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,0BAAgBD,IAAG;AACtB,QAAG,oBAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,uBAAa,CAAC;AACjB,QAAG,yBAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,uBAAa;AAChB,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,oBAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAAA,MACzC;AACA,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,sBAAsB,CAAC,IAAI,MAAM,EAAE,mBAAmB,IAAI,MAAM;AAAA,MACjF;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,ylSAA+lS;AAAA,IACxmS,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM,iCACD,kBADC;AAAA,QAEJ,6BAA6B;AAAA,QAC7B,gCAAgC;AAAA,MAClC;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,ylSAA+lS;AAAA,IAC1mS,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,0BAAyB,cAAc;AAAA,EAC3C,WAAW,OAAO,yBAAyB;AAAA,IACzC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS;AAAA,EACT,cAAc;AACZ,UAAM;AACN,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,QAAQ,KAAK,SAAS,SAAS,SAAS;AAAA,EAC/C;AAAA,EACA,OAAO,YAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,gBAAgB,EAAE,CAAC;AAAA,IAC1C,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,YAAY,IAAI,sBAAsB,CAAC,EAAE,iBAAiB,IAAI,iBAAiB,CAAC;AAC/F,QAAG,qBAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,sBAAY,2BAA2B,IAAI,QAAQ,EAAE,uCAAuC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,KAAK,EAAE,uBAAuB,IAAI;AAAA,MACrP;AAAA,IACF;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,oCAA0B;AAAA,IACxC,OAAO;AAAA,IACP,oBAAoBC;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC1I,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,0BAAgBD,IAAG;AACtB,QAAG,oBAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,uBAAa,CAAC;AACjB,QAAG,yBAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,uBAAa;AAChB,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,oBAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAAA,MACzC;AACA,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,sBAAsB,CAAC,IAAI,MAAM,EAAE,mBAAmB,IAAI,MAAM;AAAA,MACjF;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,ylSAA+lS;AAAA,IAC1mS,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAQH,IAAM,eAAN,MAAM,sBAAqB,UAAU;AAAA,EACnC,WAAW,OAAO,yBAAyB;AAAA,IACzC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS;AAAA,EACT;AAAA,EACA,cAAc;AACZ,UAAM;AACN,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,QAAQ,KAAK,SAAS,SAAS,SAAS;AAAA,EAC/C;AAAA,EACA,OAAO,YAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,KAAK,WAAW,EAAE,CAAC;AAAA,IAChC,UAAU;AAAA,IACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,YAAY,IAAI,sBAAsB,CAAC,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,sBAAsB,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,iBAAiB,CAAC;AACzK,QAAG,qBAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,sBAAY,2BAA2B,IAAI,QAAQ,EAAE,uCAAuC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,KAAK,EAAE,uBAAuB,IAAI,EAAE,qBAAqB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,QAAQ;AAAA,MAC9T;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,UAAU,CAAC,aAAa,WAAW;AAAA,IACnC,UAAU,CAAI,oCAA0B;AAAA,IACxC,OAAO;AAAA,IACP,oBAAoBC;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC1I,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,QAAG,0BAAgBD,IAAG;AACtB,QAAG,oBAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,uBAAa,CAAC;AACjB,QAAG,yBAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,uBAAa;AAChB,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,oBAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAAA,MACzC;AACA,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,sBAAsB,CAAC,IAAI,MAAM,EAAE,mBAAmB,IAAI,MAAM;AAAA,MACjF;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM,iCACD,kBADC;AAAA,QAEJ,6BAA6B;AAAA,QAC7B,gCAAgC;AAAA,MAClC;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,ylSAA+lS;AAAA,IAC1mS,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,0BAAyB,UAAU;AAAA,EACvC,WAAW,OAAO,yBAAyB;AAAA,IACzC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS;AAAA,EACT,cAAc;AACZ,UAAM;AACN,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,QAAQ,KAAK,SAAS,SAAS,SAAS;AAAA,EAC/C;AAAA,EACA,OAAO,YAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,KAAK,gBAAgB,EAAE,CAAC;AAAA,IACrC,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,YAAY,IAAI,sBAAsB,CAAC,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,sBAAsB,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,iBAAiB,CAAC;AACzK,QAAG,qBAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,EAAE;AACjD,QAAG,sBAAY,2BAA2B,IAAI,QAAQ,EAAE,uCAAuC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,KAAK,EAAE,uBAAuB,IAAI;AAAA,MACrP;AAAA,IACF;AAAA,IACA,UAAU,CAAC,aAAa,WAAW;AAAA,IACnC,UAAU,CAAI,oCAA0B;AAAA,IACxC,OAAO;AAAA,IACP,oBAAoBC;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,IAC1I,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,0BAAgBD,IAAG;AACtB,QAAG,oBAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,uBAAa,CAAC;AACjB,QAAG,yBAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,uBAAa;AAChB,QAAG,uBAAa,GAAG,CAAC;AACpB,QAAG,oBAAU,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAAA,MACzC;AACA,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,sBAAsB,CAAC,IAAI,MAAM,EAAE,mBAAmB,IAAI,MAAM;AAAA,MACjF;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,ylSAA+lS;AAAA,IAC1mS,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,YAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB,WAAW,WAAW,eAAe,kBAAkB,kBAAkB,eAAe,cAAc,YAAY;AAAA,IAC9J,SAAS,CAAC,WAAW,WAAW,eAAe,eAAe,kBAAkB,kBAAkB,cAAc,cAAc,eAAe;AAAA,EAC/I,CAAC;AAAA,EACD,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,iBAAiB,eAAe;AAAA,EAC7D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,WAAW,WAAW,eAAe,kBAAkB,kBAAkB,eAAe,cAAc,YAAY;AAAA,MAC9J,SAAS,CAAC,WAAW,WAAW,eAAe,eAAe,kBAAkB,kBAAkB,cAAc,cAAc,eAAe;AAAA,IAC/I,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["document", "FocusMonitorDetectionMode", "window", "window", "HighContrastMode", "items", "RtlScrollAxisType", "RippleState", "_c0", "_c1", "_c2", "_c3"], "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]}