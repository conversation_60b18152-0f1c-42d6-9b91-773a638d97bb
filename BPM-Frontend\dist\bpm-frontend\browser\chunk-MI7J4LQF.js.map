{"version": 3, "sources": ["src/app/core/guards/auth.guard.ts", "src/app/core/guards/role.guard.ts", "src/app/core/guards/no-auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\nimport { Observable, map, take } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate, CanActivateChild {\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkAuth(route, state);\n  }\n\n  canActivateChild(\n    childRoute: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkAuth(childRoute, state);\n  }\n\n  private checkAuth(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {\n    return this.authService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        if (isAuthenticated) {\n          // Check if route requires specific roles\n          const requiredRoles = route.data?.['roles'] as string[];\n          if (requiredRoles && requiredRoles.length > 0) {\n            const hasRequiredRole = this.authService.hasAnyRole(requiredRoles);\n            if (!hasRequiredRole) {\n              this.router.navigate(['/unauthorized']);\n              return false;\n            }\n          }\n          return true;\n        } else {\n          // Store the attempted URL for redirecting after login\n          this.authService.redirectUrl = state.url;\n          this.router.navigate(['/auth/login']);\n          return false;\n        }\n      })\n    );\n  }\n}", "import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, Router } from '@angular/router';\nimport { Observable, map, take } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RoleGuard implements CanActivate {\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(route: ActivatedRouteSnapshot): Observable<boolean> {\n    const requiredRoles = route.data?.['roles'] as string[];\n    \n    if (!requiredRoles || requiredRoles.length === 0) {\n      return new Observable(observer => {\n        observer.next(true);\n        observer.complete();\n      });\n    }\n\n    return this.authService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        if (!isAuthenticated) {\n          this.router.navigate(['/auth/login']);\n          return false;\n        }\n\n        const hasRequiredRole = this.authService.hasAnyRole(requiredRoles);\n        if (!hasRequiredRole) {\n          this.router.navigate(['/unauthorized']);\n          return false;\n        }\n\n        return true;\n      })\n    );\n  }\n}", "import { Injectable } from '@angular/core';\nimport { CanActivate, Router } from '@angular/router';\nimport { Observable, map, take } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class NoAuthGuard implements CanActivate {\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(): Observable<boolean> {\n    return this.authService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        if (isAuthenticated) {\n          // User is already authenticated, redirect to dashboard\n          this.router.navigate(['/dashboard']);\n          return false;\n        }\n        return true;\n      })\n    );\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;AAQM,IAAO,YAAP,MAAO,WAAS;EAEV;EACA;EAFV,YACU,aACA,QAAc;AADd,SAAA,cAAA;AACA,SAAA,SAAA;EACP;EAEH,YACE,OACA,OAA0B;AAE1B,WAAO,KAAK,UAAU,OAAO,KAAK;EACpC;EAEA,iBACE,YACA,OAA0B;AAE1B,WAAO,KAAK,UAAU,YAAY,KAAK;EACzC;EAEQ,UAAU,OAA+B,OAA0B;AACzE,WAAO,KAAK,YAAY,iBAAiB,KACvC,KAAK,CAAC,GACN,IAAI,qBAAkB;AACpB,UAAI,iBAAiB;AAEnB,cAAM,gBAAgB,MAAM,OAAO,OAAO;AAC1C,YAAI,iBAAiB,cAAc,SAAS,GAAG;AAC7C,gBAAM,kBAAkB,KAAK,YAAY,WAAW,aAAa;AACjE,cAAI,CAAC,iBAAiB;AACpB,iBAAK,OAAO,SAAS,CAAC,eAAe,CAAC;AACtC,mBAAO;UACT;QACF;AACA,eAAO;MACT,OAAO;AAEL,aAAK,YAAY,cAAc,MAAM;AACrC,aAAK,OAAO,SAAS,CAAC,aAAa,CAAC;AACpC,eAAO;MACT;IACF,CAAC,CAAC;EAEN;;qCA3CW,YAAS,mBAAA,WAAA,GAAA,mBAAA,MAAA,CAAA;EAAA;4EAAT,YAAS,SAAT,WAAS,WAAA,YAFR,OAAM,CAAA;;;sEAEP,WAAS,CAAA;UAHrB;WAAW;MACV,YAAY;KACb;;;;;ACCK,IAAO,YAAP,MAAO,WAAS;EAEV;EACA;EAFV,YACU,aACA,QAAc;AADd,SAAA,cAAA;AACA,SAAA,SAAA;EACP;EAEH,YAAY,OAA6B;AACvC,UAAM,gBAAgB,MAAM,OAAO,OAAO;AAE1C,QAAI,CAAC,iBAAiB,cAAc,WAAW,GAAG;AAChD,aAAO,IAAI,WAAW,cAAW;AAC/B,iBAAS,KAAK,IAAI;AAClB,iBAAS,SAAQ;MACnB,CAAC;IACH;AAEA,WAAO,KAAK,YAAY,iBAAiB,KACvC,KAAK,CAAC,GACN,IAAI,qBAAkB;AACpB,UAAI,CAAC,iBAAiB;AACpB,aAAK,OAAO,SAAS,CAAC,aAAa,CAAC;AACpC,eAAO;MACT;AAEA,YAAM,kBAAkB,KAAK,YAAY,WAAW,aAAa;AACjE,UAAI,CAAC,iBAAiB;AACpB,aAAK,OAAO,SAAS,CAAC,eAAe,CAAC;AACtC,eAAO;MACT;AAEA,aAAO;IACT,CAAC,CAAC;EAEN;;qCAjCW,YAAS,mBAAA,WAAA,GAAA,mBAAA,MAAA,CAAA;EAAA;4EAAT,YAAS,SAAT,WAAS,WAAA,YAFR,OAAM,CAAA;;;sEAEP,WAAS,CAAA;UAHrB;WAAW;MACV,YAAY;KACb;;;;;ACCK,IAAO,cAAP,MAAO,aAAW;EAEZ;EACA;EAFV,YACU,aACA,QAAc;AADd,SAAA,cAAA;AACA,SAAA,SAAA;EACP;EAEH,cAAW;AACT,WAAO,KAAK,YAAY,iBAAiB,KACvC,KAAK,CAAC,GACN,IAAI,qBAAkB;AACpB,UAAI,iBAAiB;AAEnB,aAAK,OAAO,SAAS,CAAC,YAAY,CAAC;AACnC,eAAO;MACT;AACA,aAAO;IACT,CAAC,CAAC;EAEN;;qCAlBW,cAAW,mBAAA,WAAA,GAAA,mBAAA,MAAA,CAAA;EAAA;4EAAX,cAAW,SAAX,aAAW,WAAA,YAFV,OAAM,CAAA;;;sEAEP,aAAW,CAAA;UAHvB;WAAW;MACV,YAAY;KACb;;;", "names": []}