import {
  MatSnackBar,
  MatSnackBarModule
} from "./chunk-4HGSRZQD.js";
import "./chunk-DQTTXTF2.js";
import {
  MatChip,
  MatChipsModule
} from "./chunk-KJM35DAP.js";
import "./chunk-PJGOPMTU.js";
import "./chunk-4WCUDP7B.js";
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
  MatTableModule
} from "./chunk-Z6DK6RU5.js";
import "./chunk-I7LZP7WV.js";
import "./chunk-FTI6XIW5.js";
import "./chunk-R6R3KTSK.js";
import {
  Mat<PERSON><PERSON>,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle
} from "./chunk-QRMDFVVO.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-FSO3A3K2.js";
import {
  Mat<PERSON><PERSON>on,
  MatButtonModule,
  MatIconButton
} from "./chunk-SVX3GQPM.js";
import {
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-XLTWONBW.js";

// src/app/features/admin/components/role-management/role-management.component.ts
function RoleManagementComponent_th_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 16);
    \u0275\u0275text(1, "Role Name");
    \u0275\u0275elementEnd();
  }
}
function RoleManagementComponent_td_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 17)(1, "div", 18)(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "small");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const role_r1 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(role_r1.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(role_r1.description);
  }
}
function RoleManagementComponent_th_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 16);
    \u0275\u0275text(1, "Permissions");
    \u0275\u0275elementEnd();
  }
}
function RoleManagementComponent_td_20_mat_chip_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-chip", 22);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const permission_r2 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", permission_r2, " ");
  }
}
function RoleManagementComponent_td_20_span_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 23);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const role_r3 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" +", role_r3.permissions.length - 3, " more ");
  }
}
function RoleManagementComponent_td_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 17)(1, "div", 19);
    \u0275\u0275template(2, RoleManagementComponent_td_20_mat_chip_2_Template, 2, 1, "mat-chip", 20)(3, RoleManagementComponent_td_20_span_3_Template, 2, 1, "span", 21);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const role_r3 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", role_r3.permissions.slice(0, 3));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", role_r3.permissions.length > 3);
  }
}
function RoleManagementComponent_th_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 16);
    \u0275\u0275text(1, "Users");
    \u0275\u0275elementEnd();
  }
}
function RoleManagementComponent_td_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 17)(1, "span", 24);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const role_r4 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("", role_r4.userCount, " users");
  }
}
function RoleManagementComponent_th_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 16);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function RoleManagementComponent_td_26_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "td", 17)(1, "div", 25)(2, "button", 26);
    \u0275\u0275listener("click", function RoleManagementComponent_td_26_Template_button_click_2_listener() {
      const role_r6 = \u0275\u0275restoreView(_r5).$implicit;
      const ctx_r6 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r6.editRole(role_r6));
    });
    \u0275\u0275elementStart(3, "mat-icon");
    \u0275\u0275text(4, "edit");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "button", 27);
    \u0275\u0275listener("click", function RoleManagementComponent_td_26_Template_button_click_5_listener() {
      const role_r6 = \u0275\u0275restoreView(_r5).$implicit;
      const ctx_r6 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r6.viewPermissions(role_r6));
    });
    \u0275\u0275elementStart(6, "mat-icon");
    \u0275\u0275text(7, "visibility");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "button", 28);
    \u0275\u0275listener("click", function RoleManagementComponent_td_26_Template_button_click_8_listener() {
      const role_r6 = \u0275\u0275restoreView(_r5).$implicit;
      const ctx_r6 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r6.deleteRole(role_r6));
    });
    \u0275\u0275elementStart(9, "mat-icon");
    \u0275\u0275text(10, "delete");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const role_r6 = ctx.$implicit;
    \u0275\u0275advance(8);
    \u0275\u0275property("disabled", role_r6.name === "Admin");
  }
}
function RoleManagementComponent_tr_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 29);
  }
}
function RoleManagementComponent_tr_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 30);
  }
}
function RoleManagementComponent_div_37_mat_chip_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-chip", 22);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const permission_r8 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", permission_r8, " ");
  }
}
function RoleManagementComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 31)(1, "h4");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 32);
    \u0275\u0275template(4, RoleManagementComponent_div_37_mat_chip_4_Template, 2, 1, "mat-chip", 20);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const category_r9 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(category_r9.name);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", category_r9.permissions);
  }
}
var RoleManagementComponent = class _RoleManagementComponent {
  snackBar;
  roles = [];
  displayedColumns = ["name", "permissions", "userCount", "actions"];
  permissionCategories = [
    {
      name: "User Management",
      permissions: ["Create User", "Edit User", "Delete User", "View Users"]
    },
    {
      name: "Request Management",
      permissions: ["Create Request", "Approve Request", "Reject Request", "View All Requests"]
    },
    {
      name: "Workflow Management",
      permissions: ["Create Workflow", "Edit Workflow", "Delete Workflow", "Manage Steps"]
    },
    {
      name: "System Administration",
      permissions: ["System Settings", "View Logs", "Backup Data", "Manage Roles"]
    }
  ];
  constructor(snackBar) {
    this.snackBar = snackBar;
  }
  ngOnInit() {
    this.loadRoles();
  }
  loadRoles() {
    this.roles = [
      {
        id: "1",
        name: "Admin",
        description: "Full system access",
        permissions: ["Create User", "Edit User", "Delete User", "View Users", "System Settings", "View Logs"],
        userCount: 2
      },
      {
        id: "2",
        name: "Manager",
        description: "Team management and approval rights",
        permissions: ["Approve Request", "Reject Request", "View All Requests", "Create Request"],
        userCount: 5
      },
      {
        id: "3",
        name: "HR",
        description: "Human resources management",
        permissions: ["View Users", "Create Request", "Approve Request", "View All Requests"],
        userCount: 3
      },
      {
        id: "4",
        name: "Employee",
        description: "Basic user access",
        permissions: ["Create Request", "View Own Requests"],
        userCount: 25
      }
    ];
  }
  createRole() {
    this.snackBar.open("Create role dialog would open here", "Close", { duration: 3e3 });
  }
  editRole(role) {
    this.snackBar.open(`Edit role: ${role.name}`, "Close", { duration: 3e3 });
  }
  viewPermissions(role) {
    this.snackBar.open(`View permissions for: ${role.name}`, "Close", { duration: 3e3 });
  }
  deleteRole(role) {
    if (role.name === "Admin") {
      this.snackBar.open("Cannot delete Admin role", "Close", { duration: 3e3 });
      return;
    }
    this.snackBar.open(`Delete role: ${role.name}`, "Close", { duration: 3e3 });
  }
  static \u0275fac = function RoleManagementComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RoleManagementComponent)(\u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RoleManagementComponent, selectors: [["app-role-management"]], decls: 38, vars: 4, consts: [[1, "role-management-container"], [1, "header-actions"], ["mat-raised-button", "", "color", "primary", 3, "click"], [1, "table-container"], ["mat-table", "", 1, "roles-table", 3, "dataSource"], ["matColumnDef", "name"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "permissions"], ["matColumnDef", "userCount"], ["matColumnDef", "actions"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", "class", "role-row", 4, "matRowDef", "matRowDefColumns"], [1, "permissions-card"], [1, "permissions-grid"], ["class", "permission-category", 4, "ngFor", "ngForOf"], ["mat-header-cell", ""], ["mat-cell", ""], [1, "role-info"], [1, "permissions-container"], ["class", "permission-chip", 4, "ngFor", "ngForOf"], ["class", "more-permissions", 4, "ngIf"], [1, "permission-chip"], [1, "more-permissions"], [1, "user-count"], [1, "action-buttons"], ["mat-icon-button", "", "matTooltip", "Edit Role", 3, "click"], ["mat-icon-button", "", "matTooltip", "View Permissions", 3, "click"], ["mat-icon-button", "", "color", "warn", "matTooltip", "Delete Role", 3, "click", "disabled"], ["mat-header-row", ""], ["mat-row", "", 1, "role-row"], [1, "permission-category"], [1, "permission-list"]], template: function RoleManagementComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title")(4, "mat-icon");
      \u0275\u0275text(5, "admin_panel_settings");
      \u0275\u0275elementEnd();
      \u0275\u0275text(6, " Role Management ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "div", 1)(8, "button", 2);
      \u0275\u0275listener("click", function RoleManagementComponent_Template_button_click_8_listener() {
        return ctx.createRole();
      });
      \u0275\u0275elementStart(9, "mat-icon");
      \u0275\u0275text(10, "add");
      \u0275\u0275elementEnd();
      \u0275\u0275text(11, " Create Role ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(12, "mat-card-content")(13, "div", 3)(14, "table", 4);
      \u0275\u0275elementContainerStart(15, 5);
      \u0275\u0275template(16, RoleManagementComponent_th_16_Template, 2, 0, "th", 6)(17, RoleManagementComponent_td_17_Template, 6, 2, "td", 7);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementContainerStart(18, 8);
      \u0275\u0275template(19, RoleManagementComponent_th_19_Template, 2, 0, "th", 6)(20, RoleManagementComponent_td_20_Template, 4, 2, "td", 7);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementContainerStart(21, 9);
      \u0275\u0275template(22, RoleManagementComponent_th_22_Template, 2, 0, "th", 6)(23, RoleManagementComponent_td_23_Template, 3, 1, "td", 7);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementContainerStart(24, 10);
      \u0275\u0275template(25, RoleManagementComponent_th_25_Template, 2, 0, "th", 6)(26, RoleManagementComponent_td_26_Template, 11, 1, "td", 7);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275template(27, RoleManagementComponent_tr_27_Template, 1, 0, "tr", 11)(28, RoleManagementComponent_tr_28_Template, 1, 0, "tr", 12);
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(29, "mat-card", 13)(30, "mat-card-header")(31, "mat-card-title")(32, "mat-icon");
      \u0275\u0275text(33, "security");
      \u0275\u0275elementEnd();
      \u0275\u0275text(34, " Available Permissions ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(35, "mat-card-content")(36, "div", 14);
      \u0275\u0275template(37, RoleManagementComponent_div_37_Template, 5, 2, "div", 15);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(14);
      \u0275\u0275property("dataSource", ctx.roles);
      \u0275\u0275advance(13);
      \u0275\u0275property("matHeaderRowDef", ctx.displayedColumns);
      \u0275\u0275advance();
      \u0275\u0275property("matRowDefColumns", ctx.displayedColumns);
      \u0275\u0275advance(9);
      \u0275\u0275property("ngForOf", ctx.permissionCategories);
    }
  }, dependencies: [
    CommonModule,
    NgForOf,
    NgIf,
    RouterModule,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatButtonModule,
    MatButton,
    MatIconButton,
    MatIconModule,
    MatIcon,
    MatTableModule,
    MatTable,
    MatHeaderCellDef,
    MatHeaderRowDef,
    MatColumnDef,
    MatCellDef,
    MatRowDef,
    MatHeaderCell,
    MatCell,
    MatHeaderRow,
    MatRow,
    MatChipsModule,
    MatChip,
    MatSnackBarModule
  ], styles: ["\n\n.role-management-container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\nmat-card[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\nmat-card-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.table-container[_ngcontent-%COMP%] {\n  overflow-x: auto;\n}\n.roles-table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.role-row[_ngcontent-%COMP%]:hover {\n  background-color: #f5f5f5;\n}\n.role-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.role-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  font-size: 1rem;\n}\n.role-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.8rem;\n}\n.permissions-container[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.25rem;\n  flex-wrap: wrap;\n  align-items: center;\n}\n.permission-chip[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  font-size: 0.75rem;\n}\n.more-permissions[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.8rem;\n  font-style: italic;\n}\n.user-count[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.25rem;\n}\n.permissions-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1rem;\n}\n.permission-category[_ngcontent-%COMP%] {\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.permission-category[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 1rem 0;\n  color: #333;\n}\n.permission-list[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.25rem;\n  flex-wrap: wrap;\n}\n@media (max-width: 768px) {\n  .permissions-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .action-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=role-management.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RoleManagementComponent, [{
    type: Component,
    args: [{ selector: "app-role-management", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatTableModule,
      MatChipsModule,
      MatSnackBarModule
    ], template: `
    <div class="role-management-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>admin_panel_settings</mat-icon>
            Role Management
          </mat-card-title>
          <div class="header-actions">
            <button mat-raised-button color="primary" (click)="createRole()">
              <mat-icon>add</mat-icon>
              Create Role
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="roles" class="roles-table">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Role Name</th>
                <td mat-cell *matCellDef="let role">
                  <div class="role-info">
                    <strong>{{role.name}}</strong>
                    <small>{{role.description}}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Permissions Column -->
              <ng-container matColumnDef="permissions">
                <th mat-header-cell *matHeaderCellDef>Permissions</th>
                <td mat-cell *matCellDef="let role">
                  <div class="permissions-container">
                    <mat-chip *ngFor="let permission of role.permissions.slice(0, 3)" class="permission-chip">
                      {{permission}}
                    </mat-chip>
                    <span *ngIf="role.permissions.length > 3" class="more-permissions">
                      +{{role.permissions.length - 3}} more
                    </span>
                  </div>
                </td>
              </ng-container>

              <!-- User Count Column -->
              <ng-container matColumnDef="userCount">
                <th mat-header-cell *matHeaderCellDef>Users</th>
                <td mat-cell *matCellDef="let role">
                  <span class="user-count">{{role.userCount}} users</span>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let role">
                  <div class="action-buttons">
                    <button mat-icon-button (click)="editRole(role)" matTooltip="Edit Role">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button (click)="viewPermissions(role)" matTooltip="View Permissions">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="deleteRole(role)" 
                            [disabled]="role.name === 'Admin'" matTooltip="Delete Role">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="role-row"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Permissions Overview -->
      <mat-card class="permissions-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>security</mat-icon>
            Available Permissions
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div class="permissions-grid">
            <div *ngFor="let category of permissionCategories" class="permission-category">
              <h4>{{category.name}}</h4>
              <div class="permission-list">
                <mat-chip *ngFor="let permission of category.permissions" class="permission-chip">
                  {{permission}}
                </mat-chip>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;460f82c1b526700ea50aa1b85aa07f131ae9a03dd48c95c355524f3c944efa58;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/admin/components/role-management/role-management.component.ts */\n.role-management-container {\n  padding: 1rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\nmat-card {\n  margin-bottom: 1rem;\n}\nmat-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.table-container {\n  overflow-x: auto;\n}\n.roles-table {\n  width: 100%;\n}\n.role-row:hover {\n  background-color: #f5f5f5;\n}\n.role-info {\n  display: flex;\n  flex-direction: column;\n}\n.role-info strong {\n  font-size: 1rem;\n}\n.role-info small {\n  color: #666;\n  font-size: 0.8rem;\n}\n.permissions-container {\n  display: flex;\n  gap: 0.25rem;\n  flex-wrap: wrap;\n  align-items: center;\n}\n.permission-chip {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  font-size: 0.75rem;\n}\n.more-permissions {\n  color: #666;\n  font-size: 0.8rem;\n  font-style: italic;\n}\n.user-count {\n  color: #666;\n  font-size: 0.9rem;\n}\n.action-buttons {\n  display: flex;\n  gap: 0.25rem;\n}\n.permissions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1rem;\n}\n.permission-category {\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.permission-category h4 {\n  margin: 0 0 1rem 0;\n  color: #333;\n}\n.permission-list {\n  display: flex;\n  gap: 0.25rem;\n  flex-wrap: wrap;\n}\n@media (max-width: 768px) {\n  .permissions-grid {\n    grid-template-columns: 1fr;\n  }\n  .action-buttons {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=role-management.component.css.map */\n"] }]
  }], () => [{ type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RoleManagementComponent, { className: "RoleManagementComponent", filePath: "src/app/features/admin/components/role-management/role-management.component.ts", lineNumber: 249 });
})();
export {
  RoleManagementComponent
};
//# sourceMappingURL=chunk-6ECMYQ5A.js.map
