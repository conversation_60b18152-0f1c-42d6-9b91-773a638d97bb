{"version": 3, "sources": ["src/app/features/auth/components/register/register.component.scss"], "sourcesContent": [".register-card {\n  width: 100%;\n  border: none;\n  box-shadow: none;\n  background: transparent;\n  \n  mat-card-header {\n    text-align: center;\n    margin-bottom: 1.5rem;\n    \n    mat-card-title {\n      font-size: 1.5rem;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 0.5rem;\n    }\n    \n    mat-card-subtitle {\n      color: #666;\n      font-size: 0.9rem;\n    }\n  }\n}\n\n.register-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  \n  .full-width {\n    width: 100%;\n  }\n  \n  .half-width {\n    width: calc(50% - 0.5rem);\n  }\n  \n  .name-row {\n    display: flex;\n    gap: 1rem;\n  }\n  \n  mat-form-field {\n    .mat-mdc-form-field-subscript-wrapper {\n      margin-top: 0.5rem;\n    }\n  }\n}\n\n.terms-section {\n  margin: 0.5rem 0 1.5rem 0;\n  \n  mat-checkbox {\n    font-size: 0.875rem;\n    \n    .mat-checkbox-label {\n      line-height: 1.4;\n    }\n  }\n  \n  .terms-link {\n    color: #667eea;\n    text-decoration: none;\n    transition: color 0.3s ease;\n    \n    &:hover {\n      color: #764ba2;\n      text-decoration: underline;\n    }\n  }\n  \n  mat-error {\n    font-size: 0.75rem;\n    color: #f44336;\n    margin-top: 0.5rem;\n    display: block;\n  }\n}\n\n.register-button {\n  height: 48px;\n  font-size: 1rem;\n  font-weight: 500;\n  border-radius: 8px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  transition: all 0.3s ease;\n  \n  &:hover:not(:disabled) {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n  }\n  \n  &:disabled {\n    opacity: 0.7;\n    cursor: not-allowed;\n  }\n  \n  mat-spinner {\n    margin-right: 8px;\n  }\n}\n\n.card-actions {\n  padding: 1rem 0 0 0;\n  justify-content: center;\n  \n  .signin-text {\n    text-align: center;\n    color: #666;\n    font-size: 0.9rem;\n    margin: 0;\n    \n    .signin-link {\n      color: #667eea;\n      text-decoration: none;\n      font-weight: 500;\n      transition: color 0.3s ease;\n      \n      &:hover {\n        color: #764ba2;\n        text-decoration: underline;\n      }\n    }\n  }\n}\n\n// Custom snackbar styles\n:host ::ng-deep {\n  .success-snackbar {\n    background-color: #4caf50;\n    color: white;\n  }\n  \n  .error-snackbar {\n    background-color: #f44336;\n    color: white;\n  }\n}\n\n// Responsive Design\n@media (max-width: 768px) {\n  .name-row {\n    flex-direction: column;\n    gap: 1rem;\n    \n    .half-width {\n      width: 100%;\n    }\n  }\n}\n\n@media (max-width: 480px) {\n  .register-button {\n    height: 44px;\n    font-size: 0.9rem;\n  }\n  \n  .terms-section {\n    mat-checkbox {\n      font-size: 0.8rem;\n    }\n  }\n}\n\n// Animation for form elements\n.register-form {\n  animation: fadeInUp 0.6s ease-out 0.2s both;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// Password strength indicator (optional enhancement)\n.password-strength {\n  margin-top: 0.5rem;\n  \n  .strength-bar {\n    height: 4px;\n    background-color: #e0e0e0;\n    border-radius: 2px;\n    overflow: hidden;\n    \n    .strength-fill {\n      height: 100%;\n      transition: all 0.3s ease;\n      \n      &.weak {\n        width: 33%;\n        background-color: #f44336;\n      }\n      \n      &.medium {\n        width: 66%;\n        background-color: #ff9800;\n      }\n      \n      &.strong {\n        width: 100%;\n        background-color: #4caf50;\n      }\n    }\n  }\n  \n  .strength-text {\n    font-size: 0.75rem;\n    margin-top: 0.25rem;\n    \n    &.weak { color: #f44336; }\n    &.medium { color: #ff9800; }\n    &.strong { color: #4caf50; }\n  }\n}"], "mappings": ";AAAA,CAAA;AACE,SAAA;AACA,UAAA;AACA,cAAA;AACA,cAAA;;AAEA,CANF,cAME;AACE,cAAA;AACA,iBAAA;;AAEA,CAVJ,cAUI,gBAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAjBJ,cAiBI,gBAAA;AACE,SAAA;AACA,aAAA;;AAKN,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAEA,CALF,cAKE,CAAA;AACE,SAAA;;AAGF,CATF,cASE,CAAA;AACE,SAAA,KAAA,IAAA,EAAA;;AAGF,CAbF,cAaE,CAAA;AACE,WAAA;AACA,OAAA;;AAIA,CAnBJ,cAmBI,eAAA,CAAA;AACE,cAAA;;AAKN,CAAA;AACE,UAAA,OAAA,EAAA,OAAA;;AAEA,CAHF,cAGE;AACE,aAAA;;AAEA,CANJ,cAMI,aAAA,CAAA;AACE,eAAA;;AAIJ,CAXF,cAWE,CAAA;AACE,SAAA;AACA,mBAAA;AACA,cAAA,MAAA,KAAA;;AAEA,CAhBJ,cAgBI,CALF,UAKE;AACE,SAAA;AACA,mBAAA;;AAIJ,CAtBF,cAsBE;AACE,aAAA;AACA,SAAA;AACA,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,UAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CARF,eAQE,MAAA,KAAA;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CAbF,eAaE;AACE,WAAA;AACA,UAAA;;AAGF,CAlBF,gBAkBE;AACE,gBAAA;;AAIJ,CAAA;AACE,WAAA,KAAA,EAAA,EAAA;AACA,mBAAA;;AAEA,CAJF,aAIE,CAAA;AACE,cAAA;AACA,SAAA;AACA,aAAA;AACA,UAAA;;AAEA,CAVJ,aAUI,CANF,YAME,CAAA;AACE,SAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA,MAAA,KAAA;;AAEA,CAhBN,aAgBM,CAZJ,YAYI,CANF,WAME;AACE,SAAA;AACA,mBAAA;;AAQN,MAAA,UAAA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,MAAA,UAAA,CAAA;AACE,oBAAA;AACA,SAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GAxGA;AAyGE,oBAAA;AACA,SAAA;;AAEA,GA5GF,SA4GE,CAhHF;AAiHI,WAAA;;;AAKN,OAAA,CAAA,SAAA,EAAA;AACE,GAzEF;AA0EI,YAAA;AACA,eAAA;;AAIA,GA7GJ,cA6GI;AACE,eAAA;;;AAMN,CA7IA;AA8IE,aAAA,SAAA,KAAA,SAAA,KAAA;;AAGF,WAHE;AAIA;AACE,aAAA;AACA,eAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA;;;AAKJ,CAAA;AACE,cAAA;;AAEA,CAHF,kBAGE,CAAA;AACE,UAAA;AACA,oBAAA;AACA,iBAAA;AACA,YAAA;;AAEA,CATJ,kBASI,CANF,aAME,CAAA;AACE,UAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAbN,kBAaM,CAVJ,aAUI,CAJF,aAIE,CAAA;AACE,SAAA;AACA,oBAAA;;AAGF,CAlBN,kBAkBM,CAfJ,aAeI,CATF,aASE,CAAA;AACE,SAAA;AACA,oBAAA;;AAGF,CAvBN,kBAuBM,CApBJ,aAoBI,CAdF,aAcE,CAAA;AACE,SAAA;AACA,oBAAA;;AAKN,CA9BF,kBA8BE,CAAA;AACE,aAAA;AACA,cAAA;;AAEA,CAlCJ,kBAkCI,CAJF,aAIE,CArBE;AAqBO,SAAA;;AACT,CAnCJ,kBAmCI,CALF,aAKE,CAjBE;AAiBS,SAAA;;AACX,CApCJ,kBAoCI,CANF,aAME,CAbE;AAaS,SAAA;;", "names": []}