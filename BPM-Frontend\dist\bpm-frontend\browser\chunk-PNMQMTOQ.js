import {
  Mat<PERSON>aginat<PERSON>,
  MatPaginatorModule
} from "./chunk-HF3CCJRO.js";
import {
  MatDialog,
  MatDialogModule
} from "./chunk-4WLZHDJZ.js";
import {
  MatOption,
  MatSelect,
  MatSelectModule
} from "./chunk-2ZKWC7Y6.js";
import "./chunk-F4VZNS7P.js";
import "./chunk-THNYDRFY.js";
import {
  MatFormFieldModule,
  MatInput,
  MatInputModule
} from "./chunk-Z5T6RGZC.js";
import {
  MatSnackBar,
  MatSnackBarModule
} from "./chunk-4HGSRZQD.js";
import "./chunk-DQTTXTF2.js";
import "./chunk-CPP3G34D.js";
import {
  RequestService
} from "./chunk-AM3AE65N.js";
import {
  MatChip,
  MatChipsModule
} from "./chunk-KJM35DAP.js";
import {
  RequestType
} from "./chunk-XJ5TS5V6.js";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>uff<PERSON>
} from "./chunk-PJGOPMTU.js";
import {
  DefaultValueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel,
  ReactiveFormsModule
} from "./chunk-4WCUDP7B.js";
import {
  MatProgressSpinner,
  MatProgressSpinnerModule
} from "./chunk-PIT7R6CL.js";
import {
  AuthService
} from "./chunk-EP3FZZM6.js";
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
  MatTableModule
} from "./chunk-Z6DK6RU5.js";
import "./chunk-I7LZP7WV.js";
import "./chunk-FTI6XIW5.js";
import "./chunk-R6R3KTSK.js";
import {
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle
} from "./chunk-QRMDFVVO.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-FSO3A3K2.js";
import {
  MatButton,
  MatButtonModule,
  MatIconButton
} from "./chunk-SVX3GQPM.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-ECY6M2NH.js";
import {
  CommonModule,
  Component,
  DatePipe,
  NgIf,
  SlicePipe,
  Subject,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵpipeBind3,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-XLTWONBW.js";

// src/app/features/requests/components/request-approval/request-approval.component.ts
var _c0 = (a0) => ["/requests/details", a0];
var _c1 = () => [5, 10, 25, 50];
function RequestApprovalComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 13);
    \u0275\u0275element(1, "mat-spinner");
    \u0275\u0275elementEnd();
  }
}
function RequestApprovalComponent_div_35_th_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "Requester");
    \u0275\u0275elementEnd();
  }
}
function RequestApprovalComponent_div_35_td_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 27)(1, "div", 28)(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "small");
    \u0275\u0275text(5);
    \u0275\u0275pipe(6, "date");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const request_r1 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(request_r1.initiatorName);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(6, 2, request_r1.createdAt, "short"));
  }
}
function RequestApprovalComponent_div_35_th_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "Request");
    \u0275\u0275elementEnd();
  }
}
function RequestApprovalComponent_div_35_td_7_p_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 32);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "slice");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const request_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" ", \u0275\u0275pipeBind3(2, 2, request_r2.description, 0, 100), "", request_r2.description.length > 100 ? "..." : "", " ");
  }
}
function RequestApprovalComponent_div_35_td_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 27)(1, "div", 29)(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 30);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275template(6, RequestApprovalComponent_div_35_td_7_p_6_Template, 3, 6, "p", 31);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(request_r2.title || "No Title");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r2.getRequestTypeLabel(request_r2.type));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", request_r2.description);
  }
}
function RequestApprovalComponent_div_35_th_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "Current Step");
    \u0275\u0275elementEnd();
  }
}
function RequestApprovalComponent_div_35_td_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 27)(1, "div", 33)(2, "span", 34);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "mat-chip", 35);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const request_r4 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r2.getCurrentStepName(request_r4));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r2.getCurrentStepRole(request_r4));
  }
}
function RequestApprovalComponent_div_35_th_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "Priority");
    \u0275\u0275elementEnd();
  }
}
function RequestApprovalComponent_div_35_td_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 27)(1, "mat-chip");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r5 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275classMap(ctx_r2.getPriorityClass(request_r5));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r2.getPriorityLabel(request_r5), " ");
  }
}
function RequestApprovalComponent_div_35_th_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function RequestApprovalComponent_div_35_td_16_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "td", 27)(1, "div", 36)(2, "button", 37)(3, "mat-icon");
    \u0275\u0275text(4, "visibility");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "button", 38);
    \u0275\u0275listener("click", function RequestApprovalComponent_div_35_td_16_Template_button_click_5_listener() {
      const request_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.approveRequest(request_r7));
    });
    \u0275\u0275elementStart(6, "mat-icon");
    \u0275\u0275text(7, "check");
    \u0275\u0275elementEnd();
    \u0275\u0275text(8, " Approve ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "button", 39);
    \u0275\u0275listener("click", function RequestApprovalComponent_div_35_td_16_Template_button_click_9_listener() {
      const request_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.rejectRequest(request_r7));
    });
    \u0275\u0275elementStart(10, "mat-icon");
    \u0275\u0275text(11, "close");
    \u0275\u0275elementEnd();
    \u0275\u0275text(12, " Reject ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const request_r7 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(1, _c0, request_r7.id));
  }
}
function RequestApprovalComponent_div_35_tr_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 40);
  }
}
function RequestApprovalComponent_div_35_tr_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 41);
  }
}
function RequestApprovalComponent_div_35_div_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 42)(1, "mat-icon");
    \u0275\u0275text(2, "assignment_turned_in");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h3");
    \u0275\u0275text(4, "No pending approvals");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "All requests have been processed or there are no requests requiring your approval.");
    \u0275\u0275elementEnd()();
  }
}
function RequestApprovalComponent_div_35_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14)(1, "table", 15);
    \u0275\u0275elementContainerStart(2, 16);
    \u0275\u0275template(3, RequestApprovalComponent_div_35_th_3_Template, 2, 0, "th", 17)(4, RequestApprovalComponent_div_35_td_4_Template, 7, 5, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(5, 19);
    \u0275\u0275template(6, RequestApprovalComponent_div_35_th_6_Template, 2, 0, "th", 17)(7, RequestApprovalComponent_div_35_td_7_Template, 7, 3, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(8, 20);
    \u0275\u0275template(9, RequestApprovalComponent_div_35_th_9_Template, 2, 0, "th", 17)(10, RequestApprovalComponent_div_35_td_10_Template, 6, 2, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(11, 21);
    \u0275\u0275template(12, RequestApprovalComponent_div_35_th_12_Template, 2, 0, "th", 17)(13, RequestApprovalComponent_div_35_td_13_Template, 3, 3, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(14, 22);
    \u0275\u0275template(15, RequestApprovalComponent_div_35_th_15_Template, 2, 0, "th", 17)(16, RequestApprovalComponent_div_35_td_16_Template, 13, 3, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(17, RequestApprovalComponent_div_35_tr_17_Template, 1, 0, "tr", 23)(18, RequestApprovalComponent_div_35_tr_18_Template, 1, 0, "tr", 24);
    \u0275\u0275elementEnd();
    \u0275\u0275template(19, RequestApprovalComponent_div_35_div_19_Template, 7, 0, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("dataSource", ctx_r2.requests);
    \u0275\u0275advance(16);
    \u0275\u0275property("matHeaderRowDef", ctx_r2.displayedColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r2.displayedColumns);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.requests.length === 0);
  }
}
function RequestApprovalComponent_mat_paginator_36_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-paginator", 43);
    \u0275\u0275listener("page", function RequestApprovalComponent_mat_paginator_36_Template_mat_paginator_page_0_listener($event) {
      \u0275\u0275restoreView(_r8);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onPageChange($event));
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275property("length", ctx_r2.totalCount)("pageSize", ctx_r2.pageSize)("pageSizeOptions", \u0275\u0275pureFunction0(4, _c1))("pageIndex", ctx_r2.currentPage - 1);
  }
}
var RequestApprovalComponent = class _RequestApprovalComponent {
  requestService;
  authService;
  dialog;
  snackBar;
  destroy$ = new Subject();
  requests = [];
  displayedColumns = ["requester", "request", "currentStep", "priority", "actions"];
  loading = false;
  // Pagination
  totalCount = 0;
  currentPage = 1;
  pageSize = 10;
  // Filters
  searchTerm = "";
  selectedType = "";
  // Enums for template
  RequestType = RequestType;
  constructor(requestService, authService, dialog, snackBar) {
    this.requestService = requestService;
    this.authService = authService;
    this.dialog = dialog;
    this.snackBar = snackBar;
  }
  ngOnInit() {
    this.loadPendingRequests();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadPendingRequests() {
    this.loading = true;
    const params = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm || void 0,
      sortBy: "createdAt",
      sortDirection: "asc"
    };
    this.requestService.getPendingApprovals(params).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response) => {
        this.requests = response.data;
        this.totalCount = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading pending requests:", error);
        this.loading = false;
      }
    });
  }
  onSearchChange() {
    this.currentPage = 1;
    this.loadPendingRequests();
  }
  onFilterChange() {
    this.currentPage = 1;
    this.loadPendingRequests();
  }
  onPageChange(event) {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadPendingRequests();
  }
  approveRequest(request) {
    const currentStep = this.getCurrentStep(request);
    if (!currentStep)
      return;
    const approvalData = {
      comments: "Approved by manager"
    };
    this.requestService.approveStep(request.id, currentStep.id, approvalData).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.snackBar.open("Request approved successfully", "Close", { duration: 3e3 });
        this.loadPendingRequests();
      },
      error: (error) => {
        console.error("Error approving request:", error);
        this.snackBar.open("Error approving request", "Close", { duration: 3e3 });
      }
    });
  }
  rejectRequest(request) {
    const currentStep = this.getCurrentStep(request);
    if (!currentStep)
      return;
    const rejectionData = {
      comments: "Rejected by manager"
    };
    this.requestService.rejectStep(request.id, currentStep.id, rejectionData).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.snackBar.open("Request rejected", "Close", { duration: 3e3 });
        this.loadPendingRequests();
      },
      error: (error) => {
        console.error("Error rejecting request:", error);
        this.snackBar.open("Error rejecting request", "Close", { duration: 3e3 });
      }
    });
  }
  getCurrentStep(request) {
    return request.requestSteps.find((step) => step.status === 1);
  }
  getCurrentStepName(request) {
    const currentStep = this.getCurrentStep(request);
    return currentStep?.workflowStepName || "Unknown";
  }
  getCurrentStepRole(request) {
    const currentStep = this.getCurrentStep(request);
    return currentStep?.responsibleRole || "Unknown";
  }
  getRequestTypeLabel(type) {
    switch (type) {
      case RequestType.Leave:
        return "Leave";
      case RequestType.Expense:
        return "Expense";
      case RequestType.Training:
        return "Training";
      case RequestType.ITSupport:
        return "IT Support";
      case RequestType.ProfileUpdate:
        return "Profile";
      default:
        return "Unknown";
    }
  }
  getPriorityLabel(request) {
    const daysSinceCreated = Math.floor((Date.now() - new Date(request.createdAt).getTime()) / (1e3 * 60 * 60 * 24));
    if (daysSinceCreated > 7)
      return "High";
    if (daysSinceCreated > 3)
      return "Medium";
    return "Low";
  }
  getPriorityClass(request) {
    const priority = this.getPriorityLabel(request);
    switch (priority) {
      case "High":
        return "priority-high";
      case "Medium":
        return "priority-medium";
      case "Low":
        return "priority-low";
      default:
        return "";
    }
  }
  static \u0275fac = function RequestApprovalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RequestApprovalComponent)(\u0275\u0275directiveInject(RequestService), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(MatDialog), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RequestApprovalComponent, selectors: [["app-request-approval"]], decls: 37, vars: 11, consts: [[1, "approval-container"], [1, "header-stats"], [1, "pending-count"], [1, "filters"], ["appearance", "outline"], ["matInput", "", "placeholder", "Search requests...", 3, "ngModelChange", "ngModel"], ["matSuffix", ""], [3, "ngModelChange", "selectionChange", "ngModel"], ["value", ""], [3, "value"], ["class", "loading-container", 4, "ngIf"], ["class", "table-container", 4, "ngIf"], ["showFirstLastButtons", "", 3, "length", "pageSize", "pageSizeOptions", "pageIndex", "page", 4, "ngIf"], [1, "loading-container"], [1, "table-container"], ["mat-table", "", 1, "approval-table", 3, "dataSource"], ["matColumnDef", "requester"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "request"], ["matColumnDef", "currentStep"], ["matColumnDef", "priority"], ["matColumnDef", "actions"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", "class", "approval-row", 4, "matRowDef", "matRowDefColumns"], ["class", "no-data", 4, "ngIf"], ["mat-header-cell", ""], ["mat-cell", ""], [1, "requester-info"], [1, "request-info"], [1, "request-type"], ["class", "request-description", 4, "ngIf"], [1, "request-description"], [1, "step-info"], [1, "step-name"], [1, "step-role"], [1, "action-buttons"], ["mat-icon-button", "", "matTooltip", "View Details", 3, "routerLink"], ["mat-raised-button", "", "color", "primary", "matTooltip", "Approve", 3, "click"], ["mat-raised-button", "", "color", "warn", "matTooltip", "Reject", 3, "click"], ["mat-header-row", ""], ["mat-row", "", 1, "approval-row"], [1, "no-data"], ["showFirstLastButtons", "", 3, "page", "length", "pageSize", "pageSizeOptions", "pageIndex"]], template: function RequestApprovalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title")(4, "mat-icon");
      \u0275\u0275text(5, "approval");
      \u0275\u0275elementEnd();
      \u0275\u0275text(6, " Pending Approvals ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "div", 1)(8, "mat-chip", 2);
      \u0275\u0275text(9);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(10, "mat-card-content")(11, "div", 3)(12, "mat-form-field", 4)(13, "mat-label");
      \u0275\u0275text(14, "Search");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "input", 5);
      \u0275\u0275twoWayListener("ngModelChange", function RequestApprovalComponent_Template_input_ngModelChange_15_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);
        return $event;
      });
      \u0275\u0275listener("ngModelChange", function RequestApprovalComponent_Template_input_ngModelChange_15_listener() {
        return ctx.onSearchChange();
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "mat-icon", 6);
      \u0275\u0275text(17, "search");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(18, "mat-form-field", 4)(19, "mat-label");
      \u0275\u0275text(20, "Type");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "mat-select", 7);
      \u0275\u0275twoWayListener("ngModelChange", function RequestApprovalComponent_Template_mat_select_ngModelChange_21_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedType, $event) || (ctx.selectedType = $event);
        return $event;
      });
      \u0275\u0275listener("selectionChange", function RequestApprovalComponent_Template_mat_select_selectionChange_21_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(22, "mat-option", 8);
      \u0275\u0275text(23, "All Types");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(24, "mat-option", 9);
      \u0275\u0275text(25, "Leave");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "mat-option", 9);
      \u0275\u0275text(27, "Expense");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "mat-option", 9);
      \u0275\u0275text(29, "Training");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "mat-option", 9);
      \u0275\u0275text(31, "IT Support");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "mat-option", 9);
      \u0275\u0275text(33, "Profile Update");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(34, RequestApprovalComponent_div_34_Template, 2, 0, "div", 10)(35, RequestApprovalComponent_div_35_Template, 20, 4, "div", 11)(36, RequestApprovalComponent_mat_paginator_36_Template, 1, 5, "mat-paginator", 12);
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(9);
      \u0275\u0275textInterpolate1("", ctx.totalCount, " pending");
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.searchTerm);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedType);
      \u0275\u0275advance(3);
      \u0275\u0275property("value", ctx.RequestType.Leave);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestType.Expense);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestType.Training);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestType.ITSupport);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestType.ProfileUpdate);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.totalCount > 0);
    }
  }, dependencies: [
    CommonModule,
    NgIf,
    SlicePipe,
    DatePipe,
    RouterModule,
    RouterLink,
    FormsModule,
    DefaultValueAccessor,
    NgControlStatus,
    NgModel,
    ReactiveFormsModule,
    MatTableModule,
    MatTable,
    MatHeaderCellDef,
    MatHeaderRowDef,
    MatColumnDef,
    MatCellDef,
    MatRowDef,
    MatHeaderCell,
    MatCell,
    MatHeaderRow,
    MatRow,
    MatButtonModule,
    MatButton,
    MatIconButton,
    MatIconModule,
    MatIcon,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatChipsModule,
    MatChip,
    MatPaginatorModule,
    MatPaginator,
    MatFormFieldModule,
    MatFormField,
    MatLabel,
    MatSuffix,
    MatInputModule,
    MatInput,
    MatSelectModule,
    MatSelect,
    MatOption,
    MatProgressSpinnerModule,
    MatProgressSpinner,
    MatDialogModule,
    MatSnackBarModule
  ], styles: ["\n\n.approval-container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\nmat-card-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.pending-count[_ngcontent-%COMP%] {\n  background-color: #ff9800;\n  color: white;\n}\n.filters[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  flex-wrap: wrap;\n}\n.filters[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  min-width: 200px;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.table-container[_ngcontent-%COMP%] {\n  overflow-x: auto;\n}\n.approval-table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.approval-row[_ngcontent-%COMP%]:hover {\n  background-color: #f5f5f5;\n}\n.requester-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.requester-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n}\n.requester-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.8rem;\n}\n.request-info[_ngcontent-%COMP%] {\n  max-width: 300px;\n}\n.request-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  display: block;\n  margin-bottom: 0.25rem;\n}\n.request-type[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n.request-description[_ngcontent-%COMP%] {\n  margin: 0.5rem 0 0 0;\n  color: #666;\n  font-size: 0.8rem;\n  line-height: 1.4;\n}\n.step-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.step-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n  font-size: 0.9rem;\n}\n.step-role[_ngcontent-%COMP%] {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n  font-size: 0.75rem;\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  min-width: auto;\n}\n.priority-high[_ngcontent-%COMP%] {\n  background-color: #ffebee;\n  color: #c62828;\n}\n.priority-medium[_ngcontent-%COMP%] {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.priority-low[_ngcontent-%COMP%] {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #4caf50;\n}\n@media (max-width: 768px) {\n  .filters[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .filters[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n    min-width: 100%;\n  }\n  .action-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=request-approval.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RequestApprovalComponent, [{
    type: Component,
    args: [{ selector: "app-request-approval", standalone: true, imports: [
      CommonModule,
      RouterModule,
      FormsModule,
      ReactiveFormsModule,
      MatTableModule,
      MatButtonModule,
      MatIconModule,
      MatCardModule,
      MatChipsModule,
      MatPaginatorModule,
      MatFormFieldModule,
      MatInputModule,
      MatSelectModule,
      MatProgressSpinnerModule,
      MatDialogModule,
      MatSnackBarModule
    ], template: `
    <div class="approval-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>approval</mat-icon>
            Pending Approvals
          </mat-card-title>
          <div class="header-stats">
            <mat-chip class="pending-count">{{totalCount}} pending</mat-chip>
          </div>
        </mat-card-header>

        <mat-card-content>
          <!-- Filters -->
          <div class="filters">
            <mat-form-field appearance="outline">
              <mat-label>Search</mat-label>
              <input matInput [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()" placeholder="Search requests...">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Type</mat-label>
              <mat-select [(ngModel)]="selectedType" (selectionChange)="onFilterChange()">
                <mat-option value="">All Types</mat-option>
                <mat-option [value]="RequestType.Leave">Leave</mat-option>
                <mat-option [value]="RequestType.Expense">Expense</mat-option>
                <mat-option [value]="RequestType.Training">Training</mat-option>
                <mat-option [value]="RequestType.ITSupport">IT Support</mat-option>
                <mat-option [value]="RequestType.ProfileUpdate">Profile Update</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Loading Spinner -->
          <div *ngIf="loading" class="loading-container">
            <mat-spinner></mat-spinner>
          </div>

          <!-- Requests Table -->
          <div *ngIf="!loading" class="table-container">
            <table mat-table [dataSource]="requests" class="approval-table">
              <!-- Requester Column -->
              <ng-container matColumnDef="requester">
                <th mat-header-cell *matHeaderCellDef>Requester</th>
                <td mat-cell *matCellDef="let request">
                  <div class="requester-info">
                    <strong>{{request.initiatorName}}</strong>
                    <small>{{request.createdAt | date:'short'}}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Request Column -->
              <ng-container matColumnDef="request">
                <th mat-header-cell *matHeaderCellDef>Request</th>
                <td mat-cell *matCellDef="let request">
                  <div class="request-info">
                    <strong>{{request.title || 'No Title'}}</strong>
                    <span class="request-type">{{getRequestTypeLabel(request.type)}}</span>
                    <p class="request-description" *ngIf="request.description">
                      {{request.description | slice:0:100}}{{request.description.length > 100 ? '...' : ''}}
                    </p>
                  </div>
                </td>
              </ng-container>

              <!-- Current Step Column -->
              <ng-container matColumnDef="currentStep">
                <th mat-header-cell *matHeaderCellDef>Current Step</th>
                <td mat-cell *matCellDef="let request">
                  <div class="step-info">
                    <span class="step-name">{{getCurrentStepName(request)}}</span>
                    <mat-chip class="step-role">{{getCurrentStepRole(request)}}</mat-chip>
                  </div>
                </td>
              </ng-container>

              <!-- Priority Column -->
              <ng-container matColumnDef="priority">
                <th mat-header-cell *matHeaderCellDef>Priority</th>
                <td mat-cell *matCellDef="let request">
                  <mat-chip [class]="getPriorityClass(request)">
                    {{getPriorityLabel(request)}}
                  </mat-chip>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let request">
                  <div class="action-buttons">
                    <button mat-icon-button [routerLink]="['/requests/details', request.id]" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-raised-button color="primary" (click)="approveRequest(request)" matTooltip="Approve">
                      <mat-icon>check</mat-icon>
                      Approve
                    </button>
                    <button mat-raised-button color="warn" (click)="rejectRequest(request)" matTooltip="Reject">
                      <mat-icon>close</mat-icon>
                      Reject
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="approval-row"></tr>
            </table>

            <!-- No Data Message -->
            <div *ngIf="requests.length === 0" class="no-data">
              <mat-icon>assignment_turned_in</mat-icon>
              <h3>No pending approvals</h3>
              <p>All requests have been processed or there are no requests requiring your approval.</p>
            </div>
          </div>

          <!-- Pagination -->
          <mat-paginator
            *ngIf="!loading && totalCount > 0"
            [length]="totalCount"
            [pageSize]="pageSize"
            [pageSizeOptions]="[5, 10, 25, 50]"
            [pageIndex]="currentPage - 1"
            (page)="onPageChange($event)"
            showFirstLastButtons>
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;3280d606b92f7539ed6d7e57d09da88a46be84e7fb28f2941ff5aa2d52b2723b;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/requests/components/request-approval/request-approval.component.ts */\n.approval-container {\n  padding: 1rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\nmat-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.pending-count {\n  background-color: #ff9800;\n  color: white;\n}\n.filters {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  flex-wrap: wrap;\n}\n.filters mat-form-field {\n  min-width: 200px;\n}\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.table-container {\n  overflow-x: auto;\n}\n.approval-table {\n  width: 100%;\n}\n.approval-row:hover {\n  background-color: #f5f5f5;\n}\n.requester-info {\n  display: flex;\n  flex-direction: column;\n}\n.requester-info strong {\n  font-size: 0.9rem;\n}\n.requester-info small {\n  color: #666;\n  font-size: 0.8rem;\n}\n.request-info {\n  max-width: 300px;\n}\n.request-info strong {\n  display: block;\n  margin-bottom: 0.25rem;\n}\n.request-type {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n.request-description {\n  margin: 0.5rem 0 0 0;\n  color: #666;\n  font-size: 0.8rem;\n  line-height: 1.4;\n}\n.step-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.step-name {\n  font-weight: 500;\n  font-size: 0.9rem;\n}\n.step-role {\n  background-color: #f3e5f5;\n  color: #7b1fa2;\n  font-size: 0.75rem;\n}\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.action-buttons button {\n  min-width: auto;\n}\n.priority-high {\n  background-color: #ffebee;\n  color: #c62828;\n}\n.priority-medium {\n  background-color: #fff3e0;\n  color: #ef6c00;\n}\n.priority-low {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n}\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data mat-icon {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #4caf50;\n}\n@media (max-width: 768px) {\n  .filters {\n    flex-direction: column;\n  }\n  .filters mat-form-field {\n    min-width: 100%;\n  }\n  .action-buttons {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=request-approval.component.css.map */\n"] }]
  }], () => [{ type: RequestService }, { type: AuthService }, { type: MatDialog }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RequestApprovalComponent, { className: "RequestApprovalComponent", filePath: "src/app/features/requests/components/request-approval/request-approval.component.ts", lineNumber: 343 });
})();
export {
  RequestApprovalComponent
};
//# sourceMappingURL=chunk-PNMQMTOQ.js.map
